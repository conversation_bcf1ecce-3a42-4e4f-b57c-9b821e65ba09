'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { io, Socket } from 'socket.io-client'
import { useSession } from 'next-auth/react'
import { useAuthStore } from '@/store/authStore'
import { useLocationStore } from '@/store/locationStore'
import { SocketEvents } from '@/types'

interface SocketContextType {
  socket: Socket | null
  isConnected: boolean
}

const SocketContext = createContext<SocketContextType>({
  socket: null,
  isConnected: false,
})

export const useSocket = () => {
  const context = useContext(SocketContext)
  if (!context) {
    throw new Error('useSocket must be used within a SocketProvider')
  }
  return context
}

interface SocketProviderProps {
  children: React.ReactNode
}

export function SocketProvider({ children }: SocketProviderProps) {
  const [socket, setSocket] = useState<Socket | null>(null)
  const [isConnected, setIsConnected] = useState(false)
  const { data: session } = useSession()
  const { user } = useAuthStore()
  const { updateFriendLocation, removeFriendLocation } = useLocationStore()

  useEffect(() => {
    if (!user && !session) return

    const serverUrl = process.env.NEXT_PUBLIC_SERVER_URL || 'http://localhost:3001'

    const socketInstance = io(serverUrl, {
      auth: {
        userId: user?.id || (session?.user as any)?.id,
        token: (session as any)?.accessToken,
      },
      transports: ['websocket', 'polling'],
    })

    // Connection events
    socketInstance.on('connect', () => {
      console.log('Connected to server')
      setIsConnected(true)
    })

    socketInstance.on('disconnect', () => {
      console.log('Disconnected from server')
      setIsConnected(false)
    })

    socketInstance.on('connect_error', (error) => {
      console.error('Connection error:', error)
      setIsConnected(false)
    })

    // Location events
    socketInstance.on('location:update', (data) => {
      console.log('Friend location update:', data)
      // This will be handled by the location store
    })

    socketInstance.on('friend:online', (data) => {
      console.log('Friend online status:', data)
      // Update friend online status
    })

    socketInstance.on('friend:request', (data) => {
      console.log('New friend request:', data)
      // Show notification for new friend request
    })

    socketInstance.on('friend:accepted', (data) => {
      console.log('Friend request accepted:', data)
      // Show notification and update friends list
    })

    socketInstance.on('error', (data) => {
      console.error('Socket error:', data)
    })

    setSocket(socketInstance)

    return () => {
      socketInstance.disconnect()
      setSocket(null)
      setIsConnected(false)
    }
  }, [user, session, updateFriendLocation, removeFriendLocation])

  return (
    <SocketContext.Provider value={{ socket, isConnected }}>
      {children}
    </SocketContext.Provider>
  )
}
