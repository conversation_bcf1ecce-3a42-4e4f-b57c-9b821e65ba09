'use client'

import { useState, useRef, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Layers,
  MapPin,
  Users,
  Navigation
} from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { useLocationStore } from '@/store/locationStore.demo'
import { useAuthStore } from '@/store/authStore.demo'
import { MapControls } from './MapControls'
import { FloorSelector } from './FloorSelector'
import { FriendMarker } from './FriendMarker'

export function InteractiveMap() {
  const mapRef = useRef<HTMLDivElement>(null)
  const svgRef = useRef<SVGSVGElement>(null)

  const {
    friendLocations,
    currentLocation,
    mapViewState,
    setMapViewState
  } = useLocationStore()
  const { user } = useAuthStore()

  const [isDragging, setIsDragging] = useState(false)
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 })
  const [showFriends, setShowFriends] = useState(true)

  // Handle mouse/touch events for pan and zoom
  const handleMouseDown = (e: React.MouseEvent) => {
    setIsDragging(true)
    setDragStart({ x: e.clientX, y: e.clientY })
  }

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging) return

    const deltaX = e.clientX - dragStart.x
    const deltaY = e.clientY - dragStart.y

    setMapViewState({
      center: {
        x: mapViewState.center.x - deltaX / mapViewState.zoom,
        y: mapViewState.center.y - deltaY / mapViewState.zoom
      }
    })

    setDragStart({ x: e.clientX, y: e.clientY })
  }

  const handleMouseUp = () => {
    setIsDragging(false)
  }

  const handleWheel = (e: React.WheelEvent) => {
    e.preventDefault()
    const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1
    const newZoom = Math.max(0.5, Math.min(3, mapViewState.zoom * zoomFactor))

    setMapViewState({ zoom: newZoom })
  }

  const handleZoomIn = () => {
    setMapViewState({
      zoom: Math.min(3, mapViewState.zoom * 1.2)
    })
  }

  const handleZoomOut = () => {
    setMapViewState({
      zoom: Math.max(0.5, mapViewState.zoom * 0.8)
    })
  }

  const handleResetView = () => {
    setMapViewState({
      zoom: 1,
      center: { x: 0, y: 0 }
    })
  }

  const handleFloorChange = (floor: number) => {
    setMapViewState({ currentFloor: floor })
  }

  // Sample SVG map data (you'll replace this with actual SYK floor plans)
  const sampleMapData = {
    width: 800,
    height: 600,
    rooms: [
      { id: '101', name: 'Classroom 101', x: 50, y: 50, width: 100, height: 80 },
      { id: '102', name: 'Classroom 102', x: 200, y: 50, width: 100, height: 80 },
      { id: '103', name: 'Library', x: 350, y: 50, width: 150, height: 120 },
      { id: '104', name: 'Cafeteria', x: 50, y: 200, width: 200, height: 100 },
      { id: '105', name: 'Gym', x: 300, y: 250, width: 180, height: 150 },
    ]
  }

  return (
    <div className="relative h-full bg-gray-100 overflow-hidden">
      {/* Map Container */}
      <div
        ref={mapRef}
        className="w-full h-full cursor-grab active:cursor-grabbing"
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        onWheel={handleWheel}
      >
        <svg
          ref={svgRef}
          className="w-full h-full"
          viewBox={`${mapViewState.center.x - 400 / mapViewState.zoom} ${mapViewState.center.y - 300 / mapViewState.zoom} ${800 / mapViewState.zoom} ${600 / mapViewState.zoom}`}
          style={{
            transform: `scale(${mapViewState.zoom})`,
            transformOrigin: 'center'
          }}
        >
          {/* Background */}
          <rect
            x="0"
            y="0"
            width={sampleMapData.width}
            height={sampleMapData.height}
            fill="#f8fafc"
            stroke="#e2e8f0"
            strokeWidth="2"
          />

          {/* Grid */}
          <defs>
            <pattern id="grid" width="50" height="50" patternUnits="userSpaceOnUse">
              <path d="M 50 0 L 0 0 0 50" fill="none" stroke="#e2e8f0" strokeWidth="1" opacity="0.5" />
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />

          {/* Rooms */}
          {sampleMapData.rooms.map((room) => (
            <g key={room.id}>
              <rect
                x={room.x}
                y={room.y}
                width={room.width}
                height={room.height}
                fill="#ffffff"
                stroke="#cbd5e1"
                strokeWidth="2"
                className="hover:fill-blue-50 cursor-pointer transition-colors"
              />
              <text
                x={room.x + room.width / 2}
                y={room.y + room.height / 2}
                textAnchor="middle"
                dominantBaseline="middle"
                className="text-sm font-medium fill-gray-700 pointer-events-none"
              >
                {room.name}
              </text>
            </g>
          ))}

          {/* Current User Location */}
          {currentLocation && (
            <g>
              <circle
                cx={currentLocation.coordinates.x}
                cy={currentLocation.coordinates.y}
                r="8"
                fill="#3b82f6"
                stroke="#ffffff"
                strokeWidth="3"
                className="animate-pulse"
              />
              <circle
                cx={currentLocation.coordinates.x}
                cy={currentLocation.coordinates.y}
                r="20"
                fill="none"
                stroke="#3b82f6"
                strokeWidth="2"
                opacity="0.3"
                className="animate-ping"
              />
            </g>
          )}

          {/* Friend Locations */}
          {showFriends && friendLocations.map((friendLocation) => (
            <FriendMarker
              key={friendLocation.friend.id}
              friendLocation={friendLocation}
              currentFloor={mapViewState.currentFloor}
            />
          ))}
        </svg>
      </div>

      {/* Floor Selector */}
      <FloorSelector
        currentFloor={mapViewState.currentFloor}
        floors={[1, 2, 3, 4]}
        onFloorChange={handleFloorChange}
      />

      {/* Map Controls */}
      <MapControls
        onZoomIn={handleZoomIn}
        onZoomOut={handleZoomOut}
        onReset={handleResetView}
        showFriends={showFriends}
        onToggleFriends={() => setShowFriends(!showFriends)}
      />

      {/* Map Info */}
      <div className="absolute top-4 left-4 bg-white rounded-lg shadow-sm border p-3">
        <div className="flex items-center space-x-2 text-sm">
          <MapPin className="w-4 h-4 text-primary-600" />
          <span className="font-medium">
            {mapViewState.currentBuilding.charAt(0).toUpperCase() + mapViewState.currentBuilding.slice(1)} Building
          </span>
          <span className="text-gray-500">•</span>
          <span className="text-gray-600">Floor {mapViewState.currentFloor}</span>
        </div>

        {friendLocations.length > 0 && (
          <div className="flex items-center space-x-2 text-sm text-gray-600 mt-1">
            <Users className="w-4 h-4" />
            <span>{friendLocations.length} friends nearby</span>
          </div>
        )}
      </div>

      {/* Legend */}
      <div className="absolute bottom-4 left-4 bg-white rounded-lg shadow-sm border p-3">
        <h3 className="text-sm font-medium text-gray-900 mb-2">Legend</h3>
        <div className="space-y-2 text-xs">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
            <span>Your location</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            <span>Friends</span>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-white border border-gray-300"></div>
            <span>Rooms</span>
          </div>
        </div>
      </div>
    </div>
  )
}
