import { create } from 'zustand'
import { Location, FriendLocation, MapViewState } from '@/types'

interface LocationState {
  currentLocation: Location | null
  friendLocations: FriendLocation[]
  mapViewState: MapViewState
  isLocationEnabled: boolean
  isLoading: boolean
  error: string | null
  lastUpdate: Date | null
  
  // Actions
  setCurrentLocation: (location: Location | null) => void
  setFriendLocations: (locations: FriendLocation[]) => void
  updateFriendLocation: (location: FriendLocation) => void
  removeFriendLocation: (userId: string) => void
  setMapViewState: (viewState: Partial<MapViewState>) => void
  setLocationEnabled: (enabled: boolean) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  
  // Location methods
  requestLocationPermission: () => Promise<boolean>
  getCurrentPosition: () => Promise<GeolocationPosition | null>
  updateLocation: (coordinates: { x: number; y: number; floor: number; building: string }) => Promise<boolean>
  shareLocation: (enabled: boolean) => Promise<boolean>
  getFriendLocations: () => Promise<void>
  initialize: () => void
}

const defaultMapViewState: MapViewState = {
  zoom: 1,
  center: { x: 0, y: 0 },
  currentFloor: 1,
  currentBuilding: 'main'
}

// Demo friend locations
const demoFriendLocations: FriendLocation[] = [
  {
    id: '1',
    userId: '2',
    coordinates: {
      x: 150,
      y: 100,
      floor: 1,
      building: 'main'
    },
    timestamp: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
    isActive: true,
    friend: {
      id: '2',
      email: '<EMAIL>',
      name: 'Emma Johnson',
      image: undefined,
      avatar: undefined,
      isOnline: true,
      lastSeen: new Date(Date.now() - 2 * 60 * 1000),
      privacySettings: {
        shareLocation: true,
        invisibleMode: false,
        allowFriendRequests: true,
        showOnlineStatus: true
      },
      createdAt: new Date(),
      updatedAt: new Date()
    }
  },
  {
    id: '2',
    userId: '3',
    coordinates: {
      x: 400,
      y: 200,
      floor: 1,
      building: 'main'
    },
    timestamp: new Date(Date.now() - 1 * 60 * 1000), // 1 minute ago
    isActive: true,
    friend: {
      id: '3',
      email: '<EMAIL>',
      name: 'Alex Chen',
      image: undefined,
      avatar: undefined,
      isOnline: true,
      lastSeen: new Date(Date.now() - 30 * 1000),
      privacySettings: {
        shareLocation: true,
        invisibleMode: false,
        allowFriendRequests: true,
        showOnlineStatus: true
      },
      createdAt: new Date(),
      updatedAt: new Date()
    }
  }
]

export const useLocationStore = create<LocationState>((set, get) => ({
  currentLocation: {
    id: 'current',
    userId: '1',
    coordinates: {
      x: 250,
      y: 150,
      floor: 1,
      building: 'main'
    },
    timestamp: new Date(),
    isActive: true
  },
  friendLocations: demoFriendLocations,
  mapViewState: defaultMapViewState,
  isLocationEnabled: true,
  isLoading: false,
  error: null,
  lastUpdate: new Date(),

  setCurrentLocation: (location) => {
    set({ 
      currentLocation: location,
      lastUpdate: new Date(),
      error: null 
    })
  },

  setFriendLocations: (locations) => {
    set({ friendLocations: locations })
  },

  updateFriendLocation: (location) => {
    const { friendLocations } = get()
    const existingIndex = friendLocations.findIndex(fl => fl.friend.id === location.friend.id)
    
    if (existingIndex >= 0) {
      const updated = [...friendLocations]
      updated[existingIndex] = location
      set({ friendLocations: updated })
    } else {
      set({ friendLocations: [...friendLocations, location] })
    }
  },

  removeFriendLocation: (userId) => {
    const { friendLocations } = get()
    set({ 
      friendLocations: friendLocations.filter(fl => fl.friend.id !== userId) 
    })
  },

  setMapViewState: (viewState) => {
    const { mapViewState } = get()
    set({ 
      mapViewState: { ...mapViewState, ...viewState } 
    })
  },

  setLocationEnabled: (enabled) => {
    set({ isLocationEnabled: enabled })
  },

  setLoading: (isLoading) => {
    set({ isLoading })
  },

  setError: (error) => {
    set({ error })
  },

  requestLocationPermission: async () => {
    set({ isLoading: true, error: null })
    
    // Simulate permission request
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    set({ 
      isLocationEnabled: true, 
      isLoading: false,
      error: null 
    })
    return true
  },

  getCurrentPosition: async () => {
    // Simulate getting position
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // Return a mock position
    return {
      coords: {
        latitude: 60.1699,
        longitude: 24.9384,
        accuracy: 10,
        altitude: null,
        altitudeAccuracy: null,
        heading: null,
        speed: null
      },
      timestamp: Date.now()
    } as GeolocationPosition
  },

  updateLocation: async (coordinates) => {
    set({ isLoading: true, error: null })
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const location: Location = {
      id: 'current',
      userId: '1',
      coordinates,
      timestamp: new Date(),
      isActive: true
    }
    
    set({ 
      currentLocation: location,
      isLoading: false,
      lastUpdate: new Date(),
      error: null 
    })
    return true
  },

  shareLocation: async (enabled) => {
    set({ isLoading: true, error: null })
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500))
    
    set({ 
      isLoading: false,
      error: null 
    })
    return true
  },

  getFriendLocations: async () => {
    set({ isLoading: true, error: null })
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500))
    
    set({ 
      friendLocations: demoFriendLocations,
      isLoading: false,
      error: null 
    })
  },

  initialize: () => {
    // Initialize with demo data
    set({
      isLocationEnabled: true,
      friendLocations: demoFriendLocations
    })
  },
}))
