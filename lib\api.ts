import axios from 'axios'
import { getSession } from 'next-auth/react'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api'

// Create axios instance
export const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token
api.interceptors.request.use(
  async (config) => {
    try {
      const session = await getSession()
      if ((session as any)?.accessToken) {
        config.headers.Authorization = `Bearer ${(session as any).accessToken}`
      }
    } catch (error) {
      console.error('Error getting session:', error)
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => {
    return response
  },
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      if (typeof window !== 'undefined') {
        window.location.href = '/auth/signin'
      }
    }
    return Promise.reject(error)
  }
)

// API endpoints
export const authAPI = {
  login: (email: string, password: string) =>
    api.post('/auth/login', { email, password }),

  register: (name: string, email: string, password: string) =>
    api.post('/auth/register', { name, email, password }),

  logout: () =>
    api.post('/auth/logout'),

  getProfile: () =>
    api.get('/auth/profile'),

  updateProfile: (data: any) =>
    api.put('/auth/profile', data),

  updatePrivacy: (settings: any) =>
    api.put('/auth/privacy', { privacySettings: settings }),
}

export const friendsAPI = {
  getFriends: () =>
    api.get('/friends'),

  getFriendRequests: () =>
    api.get('/friends/requests'),

  sendFriendRequest: (userId: string, message?: string) =>
    api.post('/friends/request', { userId, message }),

  acceptFriendRequest: (requestId: string) =>
    api.post(`/friends/request/${requestId}/accept`),

  rejectFriendRequest: (requestId: string) =>
    api.post(`/friends/request/${requestId}/reject`),

  removeFriend: (friendId: string) =>
    api.delete(`/friends/${friendId}`),

  searchUsers: (query: string, type: 'name' | 'email' = 'name') =>
    api.get(`/friends/search?query=${encodeURIComponent(query)}&type=${type}`),

  blockUser: (userId: string) =>
    api.post(`/friends/block/${userId}`),

  unblockUser: (userId: string) =>
    api.delete(`/friends/block/${userId}`),
}

export const locationAPI = {
  updateLocation: (coordinates: any) =>
    api.post('/location/update', { coordinates, timestamp: new Date().toISOString() }),

  getFriendLocations: () =>
    api.get('/location/friends'),

  shareLocation: (enabled: boolean) =>
    api.post('/location/share', { enabled }),

  getLocationHistory: (userId?: string, limit: number = 50) =>
    api.get(`/location/history?${userId ? `userId=${userId}&` : ''}limit=${limit}`),
}

export const mapAPI = {
  getMapData: (building: string, floor?: number) =>
    api.get(`/maps/${building}${floor ? `?floor=${floor}` : ''}`),

  getBuildings: () =>
    api.get('/maps/buildings'),

  getRooms: (building: string, floor?: number) =>
    api.get(`/maps/rooms/${building}${floor ? `?floor=${floor}` : ''}`),

  getZones: (building: string, floor?: number) =>
    api.get(`/maps/zones/${building}${floor ? `?floor=${floor}` : ''}`),
}

export const notificationAPI = {
  getNotifications: (limit: number = 20, offset: number = 0) =>
    api.get(`/notifications?limit=${limit}&offset=${offset}`),

  markAsRead: (notificationId: string) =>
    api.put(`/notifications/${notificationId}/read`),

  markAllAsRead: () =>
    api.put('/notifications/read-all'),

  deleteNotification: (notificationId: string) =>
    api.delete(`/notifications/${notificationId}`),

  updateSettings: (settings: any) =>
    api.put('/notifications/settings', settings),
}

// Utility functions
export const handleApiError = (error: any): string => {
  if (error.response?.data?.message) {
    return error.response.data.message
  }
  if (error.message) {
    return error.message
  }
  return 'An unexpected error occurred'
}

export const isNetworkError = (error: any): boolean => {
  return !error.response && error.request
}

export const isServerError = (error: any): boolean => {
  return error.response?.status >= 500
}

export const isClientError = (error: any): boolean => {
  return error.response?.status >= 400 && error.response?.status < 500
}
