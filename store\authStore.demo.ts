import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { User, PrivacySettings } from '@/types'

interface AuthState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
  
  // Actions
  setUser: (user: User | null) => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  login: (email: string, password: string) => Promise<boolean>
  register: (name: string, email: string, password: string) => Promise<boolean>
  logout: () => void
  updateProfile: (data: Partial<User>) => Promise<boolean>
  updatePrivacySettings: (settings: PrivacySettings) => Promise<boolean>
  initialize: () => void
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      setUser: (user) => {
        set({ 
          user, 
          isAuthenticated: !!user,
          error: null 
        })
      },

      setLoading: (isLoading) => {
        set({ isLoading })
      },

      setError: (error) => {
        set({ error })
      },

      login: async (email, password) => {
        set({ isLoading: true, error: null })
        
        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // For demo purposes, accept any email/password combination
        if (email && password) {
          const user: User = {
            id: '1',
            email: email,
            name: email.split('@')[0], // Use part before @ as name
            image: undefined,
            avatar: undefined,
            isOnline: true,
            lastSeen: new Date(),
            privacySettings: {
              shareLocation: true,
              invisibleMode: false,
              allowFriendRequests: true,
              showOnlineStatus: true
            },
            createdAt: new Date(),
            updatedAt: new Date()
          }
          
          set({ 
            user, 
            isAuthenticated: true, 
            isLoading: false,
            error: null 
          })
          return true
        } else {
          set({ 
            error: 'Please enter email and password',
            isLoading: false 
          })
          return false
        }
      },

      register: async (name, email, password) => {
        set({ isLoading: true, error: null })
        
        // Simulate API call delay
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // For demo purposes, accept any registration
        if (name && email && password) {
          const user: User = {
            id: '1',
            email: email,
            name: name,
            image: undefined,
            avatar: undefined,
            isOnline: true,
            lastSeen: new Date(),
            privacySettings: {
              shareLocation: true,
              invisibleMode: false,
              allowFriendRequests: true,
              showOnlineStatus: true
            },
            createdAt: new Date(),
            updatedAt: new Date()
          }
          
          set({ 
            user, 
            isAuthenticated: true, 
            isLoading: false,
            error: null 
          })
          return true
        } else {
          set({ 
            error: 'Please fill in all fields',
            isLoading: false 
          })
          return false
        }
      },

      logout: () => {
        set({ 
          user: null, 
          isAuthenticated: false, 
          error: null 
        })
        // Clear localStorage
        localStorage.removeItem('auth-storage')
      },

      updateProfile: async (data) => {
        const { user } = get()
        if (!user) return false

        set({ isLoading: true, error: null })
        
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 500))
        
        set({ 
          user: { ...user, ...data },
          isLoading: false,
          error: null 
        })
        return true
      },

      updatePrivacySettings: async (settings) => {
        const { user } = get()
        if (!user) return false

        set({ isLoading: true, error: null })
        
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 500))
        
        set({ 
          user: { ...user, privacySettings: settings },
          isLoading: false,
          error: null 
        })
        return true
      },

      initialize: () => {
        // This will be called on app start to restore auth state
        const stored = localStorage.getItem('auth-storage')
        if (stored) {
          try {
            const parsed = JSON.parse(stored)
            if (parsed.state?.user) {
              set({ 
                user: parsed.state.user,
                isAuthenticated: true 
              })
            }
          } catch (error) {
            console.error('Failed to restore auth state:', error)
          }
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({ 
        user: state.user,
        isAuthenticated: state.isAuthenticated 
      }),
    }
  )
)
