(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[931],{8395:function(e,t,a){Promise.resolve().then(a.bind(a,253))},253:function(e,t,a){"use strict";a.r(t),a.d(t,{default:function(){return P}});var r=a(7437),s=a(2749),i=a(4033),n=a(2265),o=a(7319),l=a(5251),c=a(6142),d=a(5750),u=a(9036),m=a(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let p=(0,m.Z)("Smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]]),x=(0,m.Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);var h=a(1295),f=a(9313),y=a(2167),g=a(2549),b=a(7972);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let v=(0,m.Z)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);var w=a(7216),j=a(9670),N=a(7700),k=a(1657),E=a(5925);function C(e){let{isOpen:t,onClose:a,mode:i,onModeChange:o}=e,[c,d]=(0,n.useState)({name:"",email:"",password:"",confirmPassword:""}),[u,m]=(0,n.useState)(!1),[p,x]=(0,n.useState)({}),{login:C,register:S,isLoading:P}=(0,N.t)(),A=(e,t)=>{d(a=>({...a,[e]:t})),p[e]&&x(t=>({...t,[e]:""}))},z=()=>{let e={};if((0,k.oH)(c.email)||(e.email="Please enter a valid email address"),c.password){if("register"===i){let t=(0,k.uo)(c.password);t.isValid||(e.password=t.errors[0])}}else e.password="Password is required";return"register"===i&&(c.name.trim()||(e.name="Name is required"),c.password!==c.confirmPassword&&(e.confirmPassword="Passwords do not match")),x(e),0===Object.keys(e).length},I=async e=>{if(e.preventDefault(),z())try{("login"===i?await C(c.email,c.password):await S(c.name,c.email,c.password))&&(E.default.success("login"===i?"Welcome back!":"Account created successfully!"),a())}catch(e){E.default.error("Something went wrong. Please try again.")}};return t?(0,r.jsx)(y.M,{children:(0,r.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:[(0,r.jsx)(l.E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},onClick:a,className:"absolute inset-0 bg-black/50 backdrop-blur-sm"}),(0,r.jsxs)(l.E.div,{initial:{opacity:0,scale:.95,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.95,y:20},className:"relative w-full max-w-md bg-white rounded-2xl shadow-2xl",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"login"===i?"Welcome Back":"Create Account"}),(0,r.jsx)("button",{onClick:a,className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,r.jsx)(g.Z,{className:"w-5 h-5"})})]}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)(f.z,{onClick:()=>{(0,s.signIn)("google",{callbackUrl:"/dashboard"})},variant:"outline",className:"w-full mb-6",disabled:P,children:[(0,r.jsx)(h.Z,{className:"w-5 h-5 mr-2"}),"Continue with Google"]}),(0,r.jsxs)("div",{className:"relative mb-6",children:[(0,r.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,r.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,r.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,r.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"Or continue with email"})})]}),(0,r.jsxs)("form",{onSubmit:I,className:"space-y-4",children:["register"===i&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Full Name"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(b.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,r.jsx)("input",{type:"text",value:c.name,onChange:e=>A("name",e.target.value),className:"w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent ".concat(p.name?"border-red-500":"border-gray-300"),placeholder:"Enter your full name",disabled:P})]}),p.name&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:p.name})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(h.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,r.jsx)("input",{type:"email",value:c.email,onChange:e=>A("email",e.target.value),className:"w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent ".concat(p.email?"border-red-500":"border-gray-300"),placeholder:"Enter your email",disabled:P})]}),p.email&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:p.email})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(v,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,r.jsx)("input",{type:u?"text":"password",value:c.password,onChange:e=>A("password",e.target.value),className:"w-full pl-10 pr-12 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent ".concat(p.password?"border-red-500":"border-gray-300"),placeholder:"Enter your password",disabled:P}),(0,r.jsx)("button",{type:"button",onClick:()=>m(!u),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:u?(0,r.jsx)(w.Z,{className:"w-5 h-5"}):(0,r.jsx)(j.Z,{className:"w-5 h-5"})})]}),p.password&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:p.password})]}),"register"===i&&(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Confirm Password"}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(v,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,r.jsx)("input",{type:u?"text":"password",value:c.confirmPassword,onChange:e=>A("confirmPassword",e.target.value),className:"w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent ".concat(p.confirmPassword?"border-red-500":"border-gray-300"),placeholder:"Confirm your password",disabled:P})]}),p.confirmPassword&&(0,r.jsx)("p",{className:"mt-1 text-sm text-red-600",children:p.confirmPassword})]}),(0,r.jsx)(f.z,{type:"submit",className:"w-full",disabled:P,children:P?"Please wait...":"login"===i?"Sign In":"Create Account"})]}),(0,r.jsx)("div",{className:"mt-6 text-center",children:(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["login"===i?"Don't have an account?":"Already have an account?",(0,r.jsx)("button",{onClick:()=>o("login"===i?"register":"login"),className:"ml-1 text-primary-600 hover:text-primary-700 font-medium",disabled:P,children:"login"===i?"Sign up":"Sign in"})]})})]})]})]})}):null}function S(){let[e,t]=(0,n.useState)(!1),[a,i]=(0,n.useState)("login"),o=()=>{i("register"),t(!0)},m=[{icon:c.Z,title:"Real-time Location",description:"See where your friends are on campus in real-time with precise indoor positioning."},{icon:d.Z,title:"Friend Network",description:"Connect with classmates and build your campus social network easily."},{icon:u.Z,title:"Privacy First",description:"Full control over your location sharing with advanced privacy settings."},{icon:p,title:"Mobile Optimized",description:"Works perfectly on all devices with a native app-like experience."}];return(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50",children:[(0,r.jsx)("header",{className:"relative z-10 px-4 py-6",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto flex items-center justify-between",children:[(0,r.jsxs)(l.E.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},className:"flex items-center space-x-2",children:[(0,r.jsx)("div",{className:"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center",children:(0,r.jsx)(c.Z,{className:"w-5 h-5 text-white"})}),(0,r.jsx)("span",{className:"text-xl font-bold text-gray-900",children:"SYK SchoolFinder"})]}),(0,r.jsxs)(l.E.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},className:"flex items-center space-x-4",children:[(0,r.jsx)(f.z,{variant:"ghost",onClick:()=>{i("login"),t(!0)},className:"text-gray-600 hover:text-gray-900",children:"Sign In"}),(0,r.jsx)(f.z,{onClick:o,children:"Get Started"})]})]})}),(0,r.jsxs)("section",{className:"relative px-4 py-20",children:[(0,r.jsxs)("div",{className:"max-w-7xl mx-auto text-center",children:[(0,r.jsxs)(l.E.h1,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},className:"text-5xl md:text-6xl font-bold text-gray-900 mb-6",children:["Find Your Friends",(0,r.jsx)("br",{}),(0,r.jsx)("span",{className:"text-primary-600",children:"Around Campus"})]}),(0,r.jsx)(l.E.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},className:"text-xl text-gray-600 mb-8 max-w-3xl mx-auto",children:"Connect with your classmates and see their real-time locations on detailed SYK campus maps. Never miss a study session or lunch meetup again."}),(0,r.jsxs)(l.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},className:"flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-4",children:[(0,r.jsxs)(f.z,{size:"lg",onClick:o,className:"w-full sm:w-auto",children:["Get Started Free",(0,r.jsx)(x,{className:"ml-2 w-5 h-5"})]}),(0,r.jsxs)(f.z,{variant:"outline",size:"lg",onClick:()=>(0,s.signIn)("google"),className:"w-full sm:w-auto",children:[(0,r.jsx)(h.Z,{className:"mr-2 w-5 h-5"}),"Sign in with Google"]})]})]}),(0,r.jsx)(l.E.div,{initial:{opacity:0,y:40},animate:{opacity:1,y:0},transition:{delay:.4},className:"max-w-4xl mx-auto mt-16",children:(0,r.jsx)("div",{className:"relative",children:(0,r.jsx)("div",{className:"bg-white rounded-2xl shadow-2xl p-8 border",children:(0,r.jsx)("div",{className:"aspect-video bg-gradient-to-br from-primary-100 to-secondary-100 rounded-lg flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(c.Z,{className:"w-16 h-16 text-primary-600 mx-auto mb-4"}),(0,r.jsx)("p",{className:"text-lg font-medium text-gray-700",children:"Interactive Campus Map"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"Coming Soon"})]})})})})})]}),(0,r.jsx)("section",{className:"px-4 py-20 bg-white",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,r.jsxs)(l.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},className:"text-center mb-16",children:[(0,r.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:"Everything You Need"}),(0,r.jsx)("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"Powerful features designed to help you stay connected with your campus community."})]}),(0,r.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8",children:m.map((e,t)=>(0,r.jsxs)(l.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{delay:.1*t},className:"text-center",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-primary-100 rounded-2xl flex items-center justify-center mx-auto mb-6",children:(0,r.jsx)(e.icon,{className:"w-8 h-8 text-primary-600"})}),(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:e.title}),(0,r.jsx)("p",{className:"text-gray-600",children:e.description})]},e.title))})]})}),(0,r.jsx)("section",{className:"px-4 py-20 bg-primary-600",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,r.jsx)(l.E.h2,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},className:"text-3xl md:text-4xl font-bold text-white mb-6",children:"Ready to Connect?"}),(0,r.jsx)(l.E.p,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{delay:.1},className:"text-xl text-primary-100 mb-8",children:"Join your classmates and start exploring SYK campus together."}),(0,r.jsx)(l.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{delay:.2},children:(0,r.jsxs)(f.z,{size:"lg",variant:"secondary",onClick:o,className:"bg-white text-primary-600 hover:bg-gray-50",children:["Get Started Now",(0,r.jsx)(x,{className:"ml-2 w-5 h-5"})]})})]})}),(0,r.jsx)("footer",{className:"px-4 py-8 bg-gray-900",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto text-center",children:(0,r.jsx)("p",{className:"text-gray-400",children:"\xa9 2024 SYK SchoolFinder. Built for the SYK community."})})}),(0,r.jsx)(C,{isOpen:e,onClose:()=>t(!1),mode:a,onModeChange:i})]})}function P(){let{data:e,status:t}=(0,s.useSession)(),a=(0,i.useRouter)();return((0,n.useEffect)(()=>{"authenticated"===t&&e&&a.push("/dashboard")},[t,e,a]),"loading"===t||"authenticated"===t)?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsx)(o.T,{size:"lg"})}):(0,r.jsx)(S,{})}},9313:function(e,t,a){"use strict";a.d(t,{z:function(){return n}});var r=a(7437),s=a(2265);let i=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"default",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"default",a=arguments.length>2?arguments[2]:void 0,r={default:"bg-primary-600 text-white hover:bg-primary-700",destructive:"bg-red-600 text-white hover:bg-red-700",outline:"border border-gray-300 bg-white hover:bg-gray-50 text-gray-700",secondary:"bg-secondary-600 text-white hover:bg-secondary-700",ghost:"hover:bg-gray-100 text-gray-700",link:"underline-offset-4 hover:underline text-primary-600"},s={default:"h-10 py-2 px-4",sm:"h-9 px-3 rounded-md",lg:"h-11 px-8 rounded-lg",icon:"h-10 w-10"};return["inline-flex items-center justify-center rounded-lg text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",r[e]||r.default,s[t]||s.default,a].filter(Boolean).join(" ")},n=(0,s.forwardRef)((e,t)=>{let{className:a,variant:s="default",size:n="default",...o}=e;return(0,r.jsx)("button",{className:i(s,n,a),ref:t,...o})});n.displayName="Button"},7319:function(e,t,a){"use strict";a.d(t,{T:function(){return i}});var r=a(7437),s=a(1657);function i(e){let{size:t="md",className:a,color:i="primary"}=e;return(0,r.jsx)("div",{className:(0,s.cn)("flex items-center justify-center",a),children:(0,r.jsx)("div",{className:(0,s.cn)("animate-spin rounded-full border-2 border-current border-t-transparent",{sm:"w-4 h-4",md:"w-6 h-6",lg:"w-8 h-8"}[t],{primary:"text-primary-600",secondary:"text-secondary-600",white:"text-white"}[i]),role:"status","aria-label":"Loading",children:(0,r.jsx)("span",{className:"sr-only",children:"Loading..."})})})}},1657:function(e,t,a){"use strict";a.d(t,{Qm:function(){return c},cn:function(){return i},oH:function(){return o},pi:function(){return n},uo:function(){return l}});var r=a(7042),s=a(4769);function i(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.m6)((0,r.W)(t))}function n(e){let t=new Date,a=new Date(e),r=Math.floor((t.getTime()-a.getTime())/1e3);if(r<60)return"Just now";let s=Math.floor(r/60);if(s<60)return"".concat(s,"m ago");let i=Math.floor(s/60);if(i<24)return"".concat(i,"h ago");let n=Math.floor(i/24);return n<7?"".concat(n,"d ago"):new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})}function o(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}function l(e){let t=[];return e.length<8&&t.push("Password must be at least 8 characters long"),/[A-Z]/.test(e)||t.push("Password must contain at least one uppercase letter"),/[a-z]/.test(e)||t.push("Password must contain at least one lowercase letter"),/\d/.test(e)||t.push("Password must contain at least one number"),{isValid:0===t.length,errors:t}}function c(e){return e.split(" ").map(e=>e.charAt(0).toUpperCase()).join("").slice(0,2)}},7700:function(e,t,a){"use strict";a.d(t,{t:function(){return i}});var r=a(4660),s=a(4810);let i=(0,r.Ue)()((0,s.tJ)((e,t)=>({user:null,isAuthenticated:!1,isLoading:!1,error:null,setUser:t=>{e({user:t,isAuthenticated:!!t,error:null})},setLoading:t=>{e({isLoading:t})},setError:t=>{e({error:t})},login:async(t,a)=>(e({isLoading:!0,error:null}),await new Promise(e=>setTimeout(e,1e3)),t&&a)?(e({user:{id:"1",email:t,name:t.split("@")[0],image:void 0,avatar:void 0,isOnline:!0,lastSeen:new Date,privacySettings:{shareLocation:!0,invisibleMode:!1,allowFriendRequests:!0,showOnlineStatus:!0},createdAt:new Date,updatedAt:new Date},isAuthenticated:!0,isLoading:!1,error:null}),!0):(e({error:"Please enter email and password",isLoading:!1}),!1),register:async(t,a,r)=>(e({isLoading:!0,error:null}),await new Promise(e=>setTimeout(e,1e3)),t&&a&&r)?(e({user:{id:"1",email:a,name:t,image:void 0,avatar:void 0,isOnline:!0,lastSeen:new Date,privacySettings:{shareLocation:!0,invisibleMode:!1,allowFriendRequests:!0,showOnlineStatus:!0},createdAt:new Date,updatedAt:new Date},isAuthenticated:!0,isLoading:!1,error:null}),!0):(e({error:"Please fill in all fields",isLoading:!1}),!1),logout:()=>{e({user:null,isAuthenticated:!1,error:null}),localStorage.removeItem("auth-storage")},updateProfile:async a=>{let{user:r}=t();return!!r&&(e({isLoading:!0,error:null}),await new Promise(e=>setTimeout(e,500)),e({user:{...r,...a},isLoading:!1,error:null}),!0)},updatePrivacySettings:async a=>{let{user:r}=t();return!!r&&(e({isLoading:!0,error:null}),await new Promise(e=>setTimeout(e,500)),e({user:{...r,privacySettings:a},isLoading:!1,error:null}),!0)},initialize:()=>{let t=localStorage.getItem("auth-storage");if(t)try{var a;let r=JSON.parse(t);(null===(a=r.state)||void 0===a?void 0:a.user)&&e({user:r.state.user,isAuthenticated:!0})}catch(e){console.error("Failed to restore auth state:",e)}}}),{name:"auth-storage",partialize:e=>({user:e.user,isAuthenticated:e.isAuthenticated})}))},5925:function(e,t,a){"use strict";let r,s;a.r(t),a.d(t,{CheckmarkIcon:function(){return W},ErrorIcon:function(){return q},LoaderIcon:function(){return G},ToastBar:function(){return el},ToastIcon:function(){return ea},Toaster:function(){return em},default:function(){return ep},resolveValue:function(){return k},toast:function(){return R},useToaster:function(){return _},useToasterStore:function(){return T}});var i,n=a(2265);let o={data:""},l=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||o,c=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,d=/\/\*[^]*?\*\/|  +/g,u=/\n+/g,m=(e,t)=>{let a="",r="",s="";for(let i in e){let n=e[i];"@"==i[0]?"i"==i[1]?a=i+" "+n+";":r+="f"==i[1]?m(n,i):i+"{"+m(n,"k"==i[1]?"":t)+"}":"object"==typeof n?r+=m(n,t?t.replace(/([^,])+/g,e=>i.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,t=>/&/.test(t)?t.replace(/&/g,e):e?e+" "+t:t)):i):null!=n&&(i=/^--/.test(i)?i:i.replace(/[A-Z]/g,"-$&").toLowerCase(),s+=m.p?m.p(i,n):i+":"+n+";")}return a+(t&&s?t+"{"+s+"}":s)+r},p={},x=e=>{if("object"==typeof e){let t="";for(let a in e)t+=a+x(e[a]);return t}return e},h=(e,t,a,r,s)=>{var i;let n=x(e),o=p[n]||(p[n]=(e=>{let t=0,a=11;for(;t<e.length;)a=101*a+e.charCodeAt(t++)>>>0;return"go"+a})(n));if(!p[o]){let t=n!==e?e:(e=>{let t,a,r=[{}];for(;t=c.exec(e.replace(d,""));)t[4]?r.shift():t[3]?(a=t[3].replace(u," ").trim(),r.unshift(r[0][a]=r[0][a]||{})):r[0][t[1]]=t[2].replace(u," ").trim();return r[0]})(e);p[o]=m(s?{["@keyframes "+o]:t}:t,a?"":"."+o)}let l=a&&p.g?p.g:null;return a&&(p.g=p[o]),i=p[o],l?t.data=t.data.replace(l,i):-1===t.data.indexOf(i)&&(t.data=r?i+t.data:t.data+i),o},f=(e,t,a)=>e.reduce((e,r,s)=>{let i=t[s];if(i&&i.call){let e=i(a),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;i=t?"."+t:e&&"object"==typeof e?e.props?"":m(e,""):!1===e?"":e}return e+r+(null==i?"":i)},"");function y(e){let t=this||{},a=e.call?e(t.p):e;return h(a.unshift?a.raw?f(a,[].slice.call(arguments,1),t.p):a.reduce((e,a)=>Object.assign(e,a&&a.call?a(t.p):a),{}):a,l(t.target),t.g,t.o,t.k)}y.bind({g:1});let g,b,v,w=y.bind({k:1});function j(e,t){let a=this||{};return function(){let r=arguments;function s(i,n){let o=Object.assign({},i),l=o.className||s.className;a.p=Object.assign({theme:b&&b()},o),a.o=/ *go\d+/.test(l),o.className=y.apply(a,r)+(l?" "+l:""),t&&(o.ref=n);let c=e;return e[0]&&(c=o.as||e,delete o.as),v&&c[0]&&v(o),g(c,o)}return t?t(s):s}}var N=e=>"function"==typeof e,k=(e,t)=>N(e)?e(t):e,E=(r=0,()=>(++r).toString()),C=()=>{if(void 0===s&&"u">typeof window){let e=matchMedia("(prefers-reduced-motion: reduce)");s=!e||e.matches}return s},S="default",P=(e,t)=>{let{toastLimit:a}=e.settings;switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,a)};case 1:return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case 2:let{toast:r}=t;return P(e,{type:e.toasts.find(e=>e.id===r.id)?1:0,toast:r});case 3:let{toastId:s}=t;return{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,dismissed:!0,visible:!1}:e)};case 4:return void 0===t.toastId?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let i=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(e=>({...e,pauseDuration:e.pauseDuration+i}))}}},A=[],z={toasts:[],pausedAt:void 0,settings:{toastLimit:20}},I={},D=(e,t=S)=>{I[t]=P(I[t]||z,e),A.forEach(([e,a])=>{e===t&&a(I[t])})},L=e=>Object.keys(I).forEach(t=>D(e,t)),O=e=>Object.keys(I).find(t=>I[t].toasts.some(t=>t.id===e)),$=(e=S)=>t=>{D(t,e)},M={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},T=(e={},t=S)=>{let[a,r]=(0,n.useState)(I[t]||z),s=(0,n.useRef)(I[t]);(0,n.useEffect)(()=>(s.current!==I[t]&&r(I[t]),A.push([t,r]),()=>{let e=A.findIndex(([e])=>e===t);e>-1&&A.splice(e,1)}),[t]);let i=a.toasts.map(t=>{var a,r,s;return{...e,...e[t.type],...t,removeDelay:t.removeDelay||(null==(a=e[t.type])?void 0:a.removeDelay)||(null==e?void 0:e.removeDelay),duration:t.duration||(null==(r=e[t.type])?void 0:r.duration)||(null==e?void 0:e.duration)||M[t.type],style:{...e.style,...null==(s=e[t.type])?void 0:s.style,...t.style}}});return{...a,toasts:i}},F=(e,t="blank",a)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...a,id:(null==a?void 0:a.id)||E()}),Z=e=>(t,a)=>{let r=F(t,e,a);return $(r.toasterId||O(r.id))({type:2,toast:r}),r.id},R=(e,t)=>Z("blank")(e,t);R.error=Z("error"),R.success=Z("success"),R.loading=Z("loading"),R.custom=Z("custom"),R.dismiss=(e,t)=>{let a={type:3,toastId:e};t?$(t)(a):L(a)},R.dismissAll=e=>R.dismiss(void 0,e),R.remove=(e,t)=>{let a={type:4,toastId:e};t?$(t)(a):L(a)},R.removeAll=e=>R.remove(void 0,e),R.promise=(e,t,a)=>{let r=R.loading(t.loading,{...a,...null==a?void 0:a.loading});return"function"==typeof e&&(e=e()),e.then(e=>{let s=t.success?k(t.success,e):void 0;return s?R.success(s,{id:r,...a,...null==a?void 0:a.success}):R.dismiss(r),e}).catch(e=>{let s=t.error?k(t.error,e):void 0;s?R.error(s,{id:r,...a,...null==a?void 0:a.error}):R.dismiss(r)}),e};var V=1e3,_=(e,t="default")=>{let{toasts:a,pausedAt:r}=T(e,t),s=(0,n.useRef)(new Map).current,i=(0,n.useCallback)((e,t=V)=>{if(s.has(e))return;let a=setTimeout(()=>{s.delete(e),o({type:4,toastId:e})},t);s.set(e,a)},[]);(0,n.useEffect)(()=>{if(r)return;let e=Date.now(),s=a.map(a=>{if(a.duration===1/0)return;let r=(a.duration||0)+a.pauseDuration-(e-a.createdAt);if(r<0){a.visible&&R.dismiss(a.id);return}return setTimeout(()=>R.dismiss(a.id,t),r)});return()=>{s.forEach(e=>e&&clearTimeout(e))}},[a,r,t]);let o=(0,n.useCallback)($(t),[t]),l=(0,n.useCallback)(()=>{o({type:5,time:Date.now()})},[o]),c=(0,n.useCallback)((e,t)=>{o({type:1,toast:{id:e,height:t}})},[o]),d=(0,n.useCallback)(()=>{r&&o({type:6,time:Date.now()})},[r,o]),u=(0,n.useCallback)((e,t)=>{let{reverseOrder:r=!1,gutter:s=8,defaultPosition:i}=t||{},n=a.filter(t=>(t.position||i)===(e.position||i)&&t.height),o=n.findIndex(t=>t.id===e.id),l=n.filter((e,t)=>t<o&&e.visible).length;return n.filter(e=>e.visible).slice(...r?[l+1]:[0,l]).reduce((e,t)=>e+(t.height||0)+s,0)},[a]);return(0,n.useEffect)(()=>{a.forEach(e=>{if(e.dismissed)i(e.id,e.removeDelay);else{let t=s.get(e.id);t&&(clearTimeout(t),s.delete(e.id))}})},[a,i]),{toasts:a,handlers:{updateHeight:c,startPause:l,endPause:d,calculateOffset:u}}},Y=w`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,H=w`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,U=w`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,q=j("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Y} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${H} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${U} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,B=w`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,G=j("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${B} 1s linear infinite;
`,K=w`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,J=w`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,W=j("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${K} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${J} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,Q=j("div")`
  position: absolute;
`,X=j("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,ee=w`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,et=j("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${ee} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,ea=({toast:e})=>{let{icon:t,type:a,iconTheme:r}=e;return void 0!==t?"string"==typeof t?n.createElement(et,null,t):t:"blank"===a?null:n.createElement(X,null,n.createElement(G,{...r}),"loading"!==a&&n.createElement(Q,null,"error"===a?n.createElement(q,{...r}):n.createElement(W,{...r})))},er=e=>`
0% {transform: translate3d(0,${-200*e}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,es=e=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${-150*e}%,-1px) scale(.6); opacity:0;}
`,ei=j("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,en=j("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,eo=(e,t)=>{let a=e.includes("top")?1:-1,[r,s]=C()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[er(a),es(a)];return{animation:t?`${w(r)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${w(s)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},el=n.memo(({toast:e,position:t,style:a,children:r})=>{let s=e.height?eo(e.position||t||"top-center",e.visible):{opacity:0},i=n.createElement(ea,{toast:e}),o=n.createElement(en,{...e.ariaProps},k(e.message,e));return n.createElement(ei,{className:e.className,style:{...s,...a,...e.style}},"function"==typeof r?r({icon:i,message:o}):n.createElement(n.Fragment,null,i,o))});i=n.createElement,m.p=void 0,g=i,b=void 0,v=void 0;var ec=({id:e,className:t,style:a,onHeightUpdate:r,children:s})=>{let i=n.useCallback(t=>{if(t){let a=()=>{r(e,t.getBoundingClientRect().height)};a(),new MutationObserver(a).observe(t,{subtree:!0,childList:!0,characterData:!0})}},[e,r]);return n.createElement("div",{ref:i,className:t,style:a},s)},ed=(e,t)=>{let a=e.includes("top"),r=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:C()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(a?1:-1)}px)`,...a?{top:0}:{bottom:0},...r}},eu=y`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,em=({reverseOrder:e,position:t="top-center",toastOptions:a,gutter:r,children:s,toasterId:i,containerStyle:o,containerClassName:l})=>{let{toasts:c,handlers:d}=_(a,i);return n.createElement("div",{"data-rht-toaster":i||"",style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...o},className:l,onMouseEnter:d.startPause,onMouseLeave:d.endPause},c.map(a=>{let i=a.position||t,o=ed(i,d.calculateOffset(a,{reverseOrder:e,gutter:r,defaultPosition:t}));return n.createElement(ec,{id:a.id,key:a.id,onHeightUpdate:d.updateHeight,className:a.visible?eu:"",style:o},"custom"===a.type?k(a.message,a):s?s(a):n.createElement(el,{toast:a,position:i}))}))},ep=R}},function(e){e.O(0,[375,679,971,938,744],function(){return e(e.s=8395)}),_N_E=e.O()}]);