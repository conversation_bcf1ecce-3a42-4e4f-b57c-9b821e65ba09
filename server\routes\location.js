const express = require('express')
const Location = require('../models/Location')
const User = require('../models/User')
const { authenticateToken } = require('../middleware/auth')

const router = express.Router()

// All routes require authentication
router.use(authenticateToken)

// Update user location
router.post('/update', async (req, res) => {
  try {
    const { coordinates, accuracy, source = 'manual' } = req.body

    if (!coordinates || !coordinates.x || !coordinates.y || !coordinates.floor || !coordinates.building) {
      return res.status(400).json({
        success: false,
        message: 'Invalid coordinates. x, y, floor, and building are required'
      })
    }

    // Check if user allows location sharing
    const user = await User.findById(req.userId)
    if (!user.privacySettings.shareLocation) {
      return res.status(403).json({
        success: false,
        message: 'Location sharing is disabled'
      })
    }

    // Deactivate old locations
    await Location.deactivateOldLocations(req.userId)

    // Create new location
    const location = new Location({
      userId: req.userId,
      coordinates: {
        x: parseFloat(coordinates.x),
        y: parseFloat(coordinates.y),
        floor: parseInt(coordinates.floor),
        building: coordinates.building
      },
      accuracy: accuracy ? parseFloat(accuracy) : null,
      source,
      timestamp: new Date(),
      isActive: true
    })

    await location.save()

    res.json({
      success: true,
      message: 'Location updated successfully',
      data: location
    })
  } catch (error) {
    console.error('Update location error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to update location'
    })
  }
})

// Get friend locations
router.get('/friends', async (req, res) => {
  try {
    const friendLocations = await Location.getFriendLocations(req.userId)
    
    res.json({
      success: true,
      data: friendLocations
    })
  } catch (error) {
    console.error('Get friend locations error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to get friend locations'
    })
  }
})

// Get current user location
router.get('/current', async (req, res) => {
  try {
    const location = await Location.getLatestLocation(req.userId)
    
    res.json({
      success: true,
      data: location
    })
  } catch (error) {
    console.error('Get current location error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to get current location'
    })
  }
})

// Toggle location sharing
router.post('/share', async (req, res) => {
  try {
    const { enabled } = req.body

    const user = await User.findById(req.userId)
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      })
    }

    user.privacySettings.shareLocation = Boolean(enabled)
    await user.save()

    // If disabling, deactivate current location
    if (!enabled) {
      await Location.deactivateOldLocations(req.userId)
    }

    res.json({
      success: true,
      message: `Location sharing ${enabled ? 'enabled' : 'disabled'}`,
      data: {
        shareLocation: user.privacySettings.shareLocation
      }
    })
  } catch (error) {
    console.error('Toggle location sharing error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to toggle location sharing'
    })
  }
})

// Get location history
router.get('/history', async (req, res) => {
  try {
    const { userId, limit = 50 } = req.query
    const targetUserId = userId || req.userId

    // If requesting another user's history, check if they're friends
    if (userId && userId !== req.userId.toString()) {
      const currentUser = await User.findById(req.userId)
      if (!currentUser.isFriendWith(userId)) {
        return res.status(403).json({
          success: false,
          message: 'Not authorized to view this user\'s location history'
        })
      }
    }

    const history = await Location.getLocationHistory(targetUserId, parseInt(limit))
    
    res.json({
      success: true,
      data: history
    })
  } catch (error) {
    console.error('Get location history error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to get location history'
    })
  }
})

// Find nearby friends
router.get('/nearby', async (req, res) => {
  try {
    const { maxDistance = 50 } = req.query
    
    const nearbyFriends = await Location.findNearbyUsers(req.userId, parseFloat(maxDistance))
    
    res.json({
      success: true,
      data: nearbyFriends
    })
  } catch (error) {
    console.error('Find nearby friends error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to find nearby friends'
    })
  }
})

// Delete location data
router.delete('/data', async (req, res) => {
  try {
    const { olderThan } = req.query
    
    let query = { userId: req.userId }
    
    if (olderThan) {
      const date = new Date(olderThan)
      if (isNaN(date.getTime())) {
        return res.status(400).json({
          success: false,
          message: 'Invalid date format'
        })
      }
      query.timestamp = { $lt: date }
    }

    const result = await Location.deleteMany(query)
    
    res.json({
      success: true,
      message: `Deleted ${result.deletedCount} location records`,
      data: {
        deletedCount: result.deletedCount
      }
    })
  } catch (error) {
    console.error('Delete location data error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to delete location data'
    })
  }
})

// Get location statistics
router.get('/stats', async (req, res) => {
  try {
    const { days = 7 } = req.query
    const startDate = new Date()
    startDate.setDate(startDate.getDate() - parseInt(days))

    const stats = await Location.aggregate([
      {
        $match: {
          userId: req.userId,
          timestamp: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: {
            building: '$coordinates.building',
            floor: '$coordinates.floor'
          },
          count: { $sum: 1 },
          firstVisit: { $min: '$timestamp' },
          lastVisit: { $max: '$timestamp' }
        }
      },
      {
        $sort: { count: -1 }
      }
    ])

    const totalLocations = await Location.countDocuments({
      userId: req.userId,
      timestamp: { $gte: startDate }
    })

    res.json({
      success: true,
      data: {
        totalLocations,
        locationsByArea: stats,
        period: `${days} days`
      }
    })
  } catch (error) {
    console.error('Get location stats error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to get location statistics'
    })
  }
})

module.exports = router
