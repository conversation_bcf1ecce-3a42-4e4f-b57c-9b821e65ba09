import{Component as n,createElement as t,options as e,toChildArray as r,Fragment as u,render as o,hydrate as i,createContext as c,createRef as f,cloneElement as l}from"preact";export{Component,Fragment,createContext,createElement,createRef}from"preact";import{useCallback as a,useContext as s,useDebugValue as h,useEffect as v,useId as d,useImperativeHandle as p,useLayoutEffect as m,useMemo as y,useReducer as _,useRef as b,useState as S}from"preact/hooks";export*from"preact/hooks";function g(n,t){for(var e in n)if("__source"!==e&&!(e in t))return!0;for(var r in t)if("__source"!==r&&n[r]!==t[r])return!0;return!1}function E(n,t){this.props=n,this.context=t}function C(n,e){function r(n){var t=this.props.ref,r=t==n.ref;return!r&&t&&(t.call?t(null):t.current=null),e?!e(this.props,n)||!r:g(this.props,n)}function u(e){return this.shouldComponentUpdate=r,t(n,e)}return u.displayName="Memo("+(n.displayName||n.name)+")",u.prototype.isReactComponent=!0,u.__f=!0,u}(E.prototype=new n).isPureReactComponent=!0,E.prototype.shouldComponentUpdate=function(n,t){return g(this.props,n)||g(this.state,t)};var x=e.__b;e.__b=function(n){n.type&&n.type.__f&&n.ref&&(n.props.ref=n.ref,n.ref=null),x&&x(n)};var R="undefined"!=typeof Symbol&&Symbol.for&&Symbol.for("react.forward_ref")||3911;function w(n){function t(t){if(!("ref"in t))return n(t,null);var e=t.ref;delete t.ref;var r=n(t,e);return t.ref=e,r}return t.$$typeof=R,t.render=t,t.prototype.isReactComponent=t.__f=!0,t.displayName="ForwardRef("+(n.displayName||n.name)+")",t}var k=function(n,t){return null==n?null:r(r(n).map(t))},I={map:k,forEach:k,count:function(n){return n?r(n).length:0},only:function(n){var t=r(n);if(1!==t.length)throw"Children.only";return t[0]},toArray:r},N=e.__e;e.__e=function(n,t,e,r){if(n.then)for(var u,o=t;o=o.__;)if((u=o.__c)&&u.__c)return null==t.__e&&(t.__e=e.__e,t.__k=e.__k),u.__c(n,t);N(n,t,e,r)};var M=e.unmount;function T(n,t,e){return n&&(n.__c&&n.__c.__H&&(n.__c.__H.__.forEach(function(n){"function"==typeof n.__c&&n.__c()}),n.__c.__H=null),null!=(n=function(n,t){for(var e in t)n[e]=t[e];return n}({},n)).__c&&(n.__c.__P===e&&(n.__c.__P=t),n.__c=null),n.__k=n.__k&&n.__k.map(function(n){return T(n,t,e)})),n}function A(n,t,e){return n&&e&&(n.__v=null,n.__k=n.__k&&n.__k.map(function(n){return A(n,t,e)}),n.__c&&n.__c.__P===t&&(n.__e&&e.appendChild(n.__e),n.__c.__e=!0,n.__c.__P=e)),n}function D(){this.__u=0,this.t=null,this.__b=null}function L(n){var t=n.__.__c;return t&&t.__a&&t.__a(n)}function O(n){var e,r,u;function o(o){if(e||(e=n()).then(function(n){r=n.default||n},function(n){u=n}),u)throw u;if(!r)throw e;return t(r,o)}return o.displayName="Lazy",o.__f=!0,o}function F(){this.u=null,this.o=null}e.unmount=function(n){var t=n.__c;t&&t.__R&&t.__R(),t&&32&n.__u&&(n.type=null),M&&M(n)},(D.prototype=new n).__c=function(n,t){var e=t.__c,r=this;null==r.t&&(r.t=[]),r.t.push(e);var u=L(r.__v),o=!1,i=function(){o||(o=!0,e.__R=null,u?u(c):c())};e.__R=i;var c=function(){if(!--r.__u){if(r.state.__a){var n=r.state.__a;r.__v.__k[0]=A(n,n.__c.__P,n.__c.__O)}var t;for(r.setState({__a:r.__b=null});t=r.t.pop();)t.forceUpdate()}};r.__u++||32&t.__u||r.setState({__a:r.__b=r.__v.__k[0]}),n.then(i,i)},D.prototype.componentWillUnmount=function(){this.t=[]},D.prototype.render=function(n,e){if(this.__b){if(this.__v.__k){var r=document.createElement("div"),o=this.__v.__k[0].__c;this.__v.__k[0]=T(this.__b,r,o.__O=o.__P)}this.__b=null}var i=e.__a&&t(u,null,n.fallback);return i&&(i.__u&=-33),[t(u,null,e.__a?null:n.children),i]};var U=function(n,t,e){if(++e[1]===e[0]&&n.o.delete(t),n.props.revealOrder&&("t"!==n.props.revealOrder[0]||!n.o.size))for(e=n.u;e;){for(;e.length>3;)e.pop()();if(e[1]<e[0])break;n.u=e=e[2]}};function V(n){return this.getChildContext=function(){return n.context},n.children}function W(n){var e=this,r=n.i;e.componentWillUnmount=function(){o(null,e.l),e.l=null,e.i=null},e.i&&e.i!==r&&e.componentWillUnmount(),e.l||(e.i=r,e.l={nodeType:1,parentNode:r,childNodes:[],contains:function(){return!0},appendChild:function(n){this.childNodes.push(n),e.i.appendChild(n)},insertBefore:function(n,t){this.childNodes.push(n),e.i.appendChild(n)},removeChild:function(n){this.childNodes.splice(this.childNodes.indexOf(n)>>>1,1),e.i.removeChild(n)}}),o(t(V,{context:e.context},n.__v),e.l)}function P(n,e){var r=t(W,{__v:n,i:e});return r.containerInfo=e,r}(F.prototype=new n).__a=function(n){var t=this,e=L(t.__v),r=t.o.get(n);return r[0]++,function(u){var o=function(){t.props.revealOrder?(r.push(u),U(t,n,r)):u()};e?e(o):o()}},F.prototype.render=function(n){this.u=null,this.o=new Map;var t=r(n.children);n.revealOrder&&"b"===n.revealOrder[0]&&t.reverse();for(var e=t.length;e--;)this.o.set(t[e],this.u=[1,0,this.u]);return n.children},F.prototype.componentDidUpdate=F.prototype.componentDidMount=function(){var n=this;this.o.forEach(function(t,e){U(n,e,t)})};var j="undefined"!=typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103,z=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|image(!S)|letter|lighting|marker(?!H|W|U)|overline|paint|pointer|shape|stop|strikethrough|stroke|text(?!L)|transform|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,B=/^on(Ani|Tra|Tou|BeforeInp|Compo)/,H=/[A-Z0-9]/g,Z="undefined"!=typeof document,Y=function(n){return("undefined"!=typeof Symbol&&"symbol"==typeof Symbol()?/fil|che|rad/:/fil|che|ra/).test(n)};function $(n,t,e){return null==t.__k&&(t.textContent=""),o(n,t),"function"==typeof e&&e(),n?n.__c:null}function q(n,t,e){return i(n,t),"function"==typeof e&&e(),n?n.__c:null}n.prototype.isReactComponent={},["componentWillMount","componentWillReceiveProps","componentWillUpdate"].forEach(function(t){Object.defineProperty(n.prototype,t,{configurable:!0,get:function(){return this["UNSAFE_"+t]},set:function(n){Object.defineProperty(this,t,{configurable:!0,writable:!0,value:n})}})});var G=e.event;function J(){}function K(){return this.cancelBubble}function Q(){return this.defaultPrevented}e.event=function(n){return G&&(n=G(n)),n.persist=J,n.isPropagationStopped=K,n.isDefaultPrevented=Q,n.nativeEvent=n};var X,nn={enumerable:!1,configurable:!0,get:function(){return this.class}},tn=e.vnode;e.vnode=function(n){"string"==typeof n.type&&function(n){var t=n.props,e=n.type,u={},o=-1===e.indexOf("-");for(var i in t){var c=t[i];if(!("value"===i&&"defaultValue"in t&&null==c||Z&&"children"===i&&"noscript"===e||"class"===i||"className"===i)){var f=i.toLowerCase();"defaultValue"===i&&"value"in t&&null==t.value?i="value":"download"===i&&!0===c?c="":"translate"===f&&"no"===c?c=!1:"o"===f[0]&&"n"===f[1]?"ondoubleclick"===f?i="ondblclick":"onchange"!==f||"input"!==e&&"textarea"!==e||Y(t.type)?"onfocus"===f?i="onfocusin":"onblur"===f?i="onfocusout":B.test(i)&&(i=f):f=i="oninput":o&&z.test(i)?i=i.replace(H,"-$&").toLowerCase():null===c&&(c=void 0),"oninput"===f&&u[i=f]&&(i="oninputCapture"),u[i]=c}}"select"==e&&u.multiple&&Array.isArray(u.value)&&(u.value=r(t.children).forEach(function(n){n.props.selected=-1!=u.value.indexOf(n.props.value)})),"select"==e&&null!=u.defaultValue&&(u.value=r(t.children).forEach(function(n){n.props.selected=u.multiple?-1!=u.defaultValue.indexOf(n.props.value):u.defaultValue==n.props.value})),t.class&&!t.className?(u.class=t.class,Object.defineProperty(u,"className",nn)):(t.className&&!t.class||t.class&&t.className)&&(u.class=u.className=t.className),n.props=u}(n),n.$$typeof=j,tn&&tn(n)};var en=e.__r;e.__r=function(n){en&&en(n),X=n.__c};var rn=e.diffed;e.diffed=function(n){rn&&rn(n);var t=n.props,e=n.__e;null!=e&&"textarea"===n.type&&"value"in t&&t.value!==e.value&&(e.value=null==t.value?"":t.value),X=null};var un={ReactCurrentDispatcher:{current:{readContext:function(n){return X.__n[n.__c].props.value},useCallback:a,useContext:s,useDebugValue:h,useDeferredValue:_n,useEffect:v,useId:d,useImperativeHandle:p,useInsertionEffect:Sn,useLayoutEffect:m,useMemo:y,useReducer:_,useRef:b,useState:S,useSyncExternalStore:En,useTransition:bn}}},on="18.3.1";function cn(n){return t.bind(null,n)}function fn(n){return!!n&&n.$$typeof===j}function ln(n){return fn(n)&&n.type===u}function an(n){return!!n&&!!n.displayName&&("string"==typeof n.displayName||n.displayName instanceof String)&&n.displayName.startsWith("Memo(")}function sn(n){return fn(n)?l.apply(null,arguments):n}function hn(n){return!!n.__k&&(o(null,n),!0)}function vn(n){return n&&(n.base||1===n.nodeType&&n)||null}var dn=function(n,t){return n(t)},pn=function(n,t){return n(t)},mn=u;function yn(n){n()}function _n(n){return n}function bn(){return[!1,yn]}var Sn=m,gn=fn;function En(n,t){var e=t(),r=S({h:{__:e,v:t}}),u=r[0].h,o=r[1];return m(function(){u.__=e,u.v=t,Cn(u)&&o({h:u})},[n,e,t]),v(function(){return Cn(u)&&o({h:u}),n(function(){Cn(u)&&o({h:u})})},[n]),e}function Cn(n){var t,e,r=n.v,u=n.__;try{var o=r();return!((t=u)===(e=o)&&(0!==t||1/t==1/e)||t!=t&&e!=e)}catch(n){return!0}}var xn={useState:S,useId:d,useReducer:_,useEffect:v,useLayoutEffect:m,useInsertionEffect:Sn,useTransition:bn,useDeferredValue:_n,useSyncExternalStore:En,startTransition:yn,useRef:b,useImperativeHandle:p,useMemo:y,useCallback:a,useContext:s,useDebugValue:h,version:"18.3.1",Children:I,render:$,hydrate:q,unmountComponentAtNode:hn,createPortal:P,createElement:t,createContext:c,createFactory:cn,cloneElement:sn,createRef:f,Fragment:u,isValidElement:fn,isElement:gn,isFragment:ln,isMemo:an,findDOMNode:vn,Component:n,PureComponent:E,memo:C,forwardRef:w,flushSync:pn,unstable_batchedUpdates:dn,StrictMode:mn,Suspense:D,SuspenseList:F,lazy:O,__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:un};export{I as Children,E as PureComponent,mn as StrictMode,D as Suspense,F as SuspenseList,un as __SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,sn as cloneElement,cn as createFactory,P as createPortal,xn as default,vn as findDOMNode,pn as flushSync,w as forwardRef,q as hydrate,gn as isElement,ln as isFragment,an as isMemo,fn as isValidElement,O as lazy,C as memo,$ as render,yn as startTransition,hn as unmountComponentAtNode,dn as unstable_batchedUpdates,_n as useDeferredValue,Sn as useInsertionEffect,En as useSyncExternalStore,bn as useTransition,on as version};
//# sourceMappingURL=compat.module.js.map
