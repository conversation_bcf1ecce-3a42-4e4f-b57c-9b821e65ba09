const mongoose = require('mongoose')

const friendRequestSchema = new mongoose.Schema({
  from: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  to: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  status: {
    type: String,
    enum: ['pending', 'accepted', 'rejected'],
    default: 'pending'
  },
  message: {
    type: String,
    maxlength: 500,
    default: ''
  },
  respondedAt: {
    type: Date,
    default: null
  }
}, {
  timestamps: true
})

// Indexes
friendRequestSchema.index({ from: 1, to: 1 }, { unique: true })
friendRequestSchema.index({ to: 1, status: 1 })
friendRequestSchema.index({ from: 1, status: 1 })
friendRequestSchema.index({ createdAt: 1 })

// Static method to check if request exists
friendRequestSchema.statics.requestExists = function(fromId, toId) {
  return this.findOne({
    $or: [
      { from: fromId, to: toId },
      { from: toId, to: fromId }
    ]
  })
}

// Static method to get pending requests for user
friendRequestSchema.statics.getPendingRequests = function(userId) {
  return this.find({
    to: userId,
    status: 'pending'
  })
  .populate('from', 'name email image avatar')
  .sort({ createdAt: -1 })
}

// Static method to get sent requests
friendRequestSchema.statics.getSentRequests = function(userId) {
  return this.find({
    from: userId,
    status: 'pending'
  })
  .populate('to', 'name email image avatar')
  .sort({ createdAt: -1 })
}

module.exports = mongoose.model('FriendRequest', friendRequestSchema)
