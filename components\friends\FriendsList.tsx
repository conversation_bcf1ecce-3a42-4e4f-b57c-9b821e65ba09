'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  Search,
  UserPlus,
  MapPin,
  MessageCircle,
  MoreVertical,
  Users,
  Clock
} from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { useAuthStore } from '@/store/authStore.demo'
import { useLocationStore } from '@/store/locationStore.demo'
import { getInitials, getTimeAgo } from '@/lib/utils'
import { AddFriendModal } from './AddFriendModal'

export function FriendsList() {
  const { user } = useAuthStore()
  const { friendLocations } = useLocationStore()
  const [searchQuery, setSearchQuery] = useState('')
  const [showAddFriend, setShowAddFriend] = useState(false)
  const [activeTab, setActiveTab] = useState<'online' | 'all'>('online')

  // Mock friends data (replace with actual API call)
  const [friends, setFriends] = useState([
    {
      id: '1',
      name: '<PERSON>',
      email: '<EMAIL>',
      image: null,
      isOnline: true,
      lastSeen: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
    },
    {
      id: '2',
      name: '<PERSON> Chen',
      email: '<EMAIL>',
      image: null,
      isOnline: false,
      lastSeen: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    },
    {
      id: '3',
      name: '<PERSON>',
      email: '<EMAIL>',
      image: null,
      isOnline: true,
      lastSeen: new Date(Date.now() - 1 * 60 * 1000), // 1 minute ago
    }
  ])

  const filteredFriends = friends.filter(friend => {
    const matchesSearch = friend.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      friend.email.toLowerCase().includes(searchQuery.toLowerCase())
    const matchesTab = activeTab === 'all' || (activeTab === 'online' && friend.isOnline)
    return matchesSearch && matchesTab
  })

  const onlineFriendsCount = friends.filter(f => f.isOnline).length

  const getFriendLocation = (friendId: string) => {
    return friendLocations.find(fl => fl.friend.id === friendId)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Friends</h2>
          <p className="text-gray-600">
            {onlineFriendsCount} of {friends.length} friends online
          </p>
        </div>
        <Button onClick={() => setShowAddFriend(true)}>
          <UserPlus className="w-4 h-4 mr-2" />
          Add Friend
        </Button>
      </div>

      {/* Search */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
        <input
          type="text"
          placeholder="Search friends..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
        />
      </div>

      {/* Tabs */}
      <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
        <button
          onClick={() => setActiveTab('online')}
          className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'online'
            ? 'bg-white text-gray-900 shadow-sm'
            : 'text-gray-600 hover:text-gray-900'
            }`}
        >
          Online ({onlineFriendsCount})
        </button>
        <button
          onClick={() => setActiveTab('all')}
          className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${activeTab === 'all'
            ? 'bg-white text-gray-900 shadow-sm'
            : 'text-gray-600 hover:text-gray-900'
            }`}
        >
          All ({friends.length})
        </button>
      </div>

      {/* Friends List */}
      <div className="space-y-3">
        {filteredFriends.length === 0 ? (
          <div className="text-center py-12">
            <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchQuery ? 'No friends found' : 'No friends yet'}
            </h3>
            <p className="text-gray-600 mb-4">
              {searchQuery
                ? 'Try adjusting your search terms'
                : 'Start building your campus network by adding friends'
              }
            </p>
            {!searchQuery && (
              <Button onClick={() => setShowAddFriend(true)}>
                <UserPlus className="w-4 h-4 mr-2" />
                Add Your First Friend
              </Button>
            )}
          </div>
        ) : (
          filteredFriends.map((friend, index) => {
            const friendLocation = getFriendLocation(friend.id)

            return (
              <motion.div
                key={friend.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {/* Avatar */}
                    <div className="relative">
                      <div className="w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center">
                        {friend.image ? (
                          <img
                            src={friend.image}
                            alt={friend.name}
                            className="w-12 h-12 rounded-full object-cover"
                          />
                        ) : (
                          <span className="text-lg font-medium text-primary-600">
                            {getInitials(friend.name)}
                          </span>
                        )}
                      </div>
                      {/* Online indicator */}
                      <div className={`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white ${friend.isOnline ? 'bg-green-500' : 'bg-gray-400'
                        }`} />
                    </div>

                    {/* Friend Info */}
                    <div>
                      <h3 className="font-medium text-gray-900">{friend.name}</h3>
                      <p className="text-sm text-gray-600">{friend.email}</p>

                      {/* Status */}
                      <div className="flex items-center space-x-2 mt-1">
                        {friend.isOnline ? (
                          <>
                            <div className="w-2 h-2 bg-green-500 rounded-full" />
                            <span className="text-xs text-green-600">Online</span>
                            {friendLocation && (
                              <>
                                <span className="text-xs text-gray-400">•</span>
                                <MapPin className="w-3 h-3 text-gray-400" />
                                <span className="text-xs text-gray-600">
                                  {friendLocation.coordinates.building} Building, Floor {friendLocation.coordinates.floor}
                                </span>
                              </>
                            )}
                          </>
                        ) : (
                          <>
                            <Clock className="w-3 h-3 text-gray-400" />
                            <span className="text-xs text-gray-600">
                              Last seen {getTimeAgo(friend.lastSeen)}
                            </span>
                          </>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center space-x-2">
                    {friendLocation && (
                      <Button variant="outline" size="sm">
                        <MapPin className="w-4 h-4 mr-1" />
                        Locate
                      </Button>
                    )}
                    <Button variant="outline" size="sm">
                      <MessageCircle className="w-4 h-4 mr-1" />
                      Message
                    </Button>
                    <Button variant="ghost" size="icon">
                      <MoreVertical className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </motion.div>
            )
          })
        )}
      </div>

      {/* Add Friend Modal */}
      <AddFriendModal
        isOpen={showAddFriend}
        onClose={() => setShowAddFriend(false)}
      />
    </div>
  )
}
