const express = require('express')
const { authenticateToken } = require('../middleware/auth')

const router = express.Router()

// All routes require authentication
router.use(authenticateToken)

// Sample map data (replace with actual SYK floor plans)
const sampleMapData = {
  buildings: [
    {
      id: 'main',
      name: 'Main Building',
      code: 'MB',
      floors: [1, 2, 3, 4],
      coordinates: { lat: 60.1699, lng: 24.9384 }, // Helsinki coordinates as example
      address: 'SYK Campus, Main Building'
    },
    {
      id: 'science',
      name: 'Science Building',
      code: 'SB',
      floors: [1, 2, 3],
      coordinates: { lat: 60.1701, lng: 24.9386 },
      address: 'SYK Campus, Science Building'
    }
  ],
  floors: {
    'main-1': {
      id: 'main-1',
      building: 'main',
      floor: 1,
      name: 'Main Building - Ground Floor',
      svgData: '', // SVG data would go here
      bounds: {
        width: 800,
        height: 600,
        minX: 0,
        minY: 0,
        maxX: 800,
        maxY: 600
      },
      rooms: [
        {
          id: 'mb-101',
          name: 'Reception',
          type: 'office',
          coordinates: { x: 50, y: 50, width: 100, height: 80 },
          floor: 1,
          building: 'main'
        },
        {
          id: 'mb-102',
          name: 'Classroom A',
          type: 'classroom',
          coordinates: { x: 200, y: 50, width: 120, height: 100 },
          floor: 1,
          building: 'main'
        },
        {
          id: 'mb-103',
          name: 'Library',
          type: 'library',
          coordinates: { x: 350, y: 50, width: 150, height: 120 },
          floor: 1,
          building: 'main'
        },
        {
          id: 'mb-104',
          name: 'Cafeteria',
          type: 'cafeteria',
          coordinates: { x: 50, y: 200, width: 200, height: 100 },
          floor: 1,
          building: 'main'
        }
      ],
      zones: [
        {
          id: 'academic-zone-1',
          name: 'Academic Wing',
          type: 'academic',
          coordinates: { x: 200, y: 50, width: 300, height: 200 },
          floor: 1,
          building: 'main',
          color: '#e3f2fd'
        },
        {
          id: 'service-zone-1',
          name: 'Service Area',
          type: 'service',
          coordinates: { x: 50, y: 200, width: 200, height: 150 },
          floor: 1,
          building: 'main',
          color: '#f3e5f5'
        }
      ]
    }
  }
}

// Get all buildings
router.get('/buildings', async (req, res) => {
  try {
    res.json({
      success: true,
      data: sampleMapData.buildings
    })
  } catch (error) {
    console.error('Get buildings error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to get buildings'
    })
  }
})

// Get map data for a specific building and floor
router.get('/:building', async (req, res) => {
  try {
    const { building } = req.params
    const { floor = 1 } = req.query

    const mapKey = `${building}-${floor}`
    const mapData = sampleMapData.floors[mapKey]

    if (!mapData) {
      return res.status(404).json({
        success: false,
        message: 'Map data not found for this building and floor'
      })
    }

    res.json({
      success: true,
      data: mapData
    })
  } catch (error) {
    console.error('Get map data error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to get map data'
    })
  }
})

// Get rooms for a building/floor
router.get('/rooms/:building', async (req, res) => {
  try {
    const { building } = req.params
    const { floor } = req.query

    let rooms = []
    
    // Get rooms from all floors of the building or specific floor
    Object.values(sampleMapData.floors).forEach(floorData => {
      if (floorData.building === building) {
        if (!floor || floorData.floor === parseInt(floor)) {
          rooms = rooms.concat(floorData.rooms)
        }
      }
    })

    res.json({
      success: true,
      data: rooms
    })
  } catch (error) {
    console.error('Get rooms error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to get rooms'
    })
  }
})

// Get zones for a building/floor
router.get('/zones/:building', async (req, res) => {
  try {
    const { building } = req.params
    const { floor } = req.query

    let zones = []
    
    // Get zones from all floors of the building or specific floor
    Object.values(sampleMapData.floors).forEach(floorData => {
      if (floorData.building === building) {
        if (!floor || floorData.floor === parseInt(floor)) {
          zones = zones.concat(floorData.zones)
        }
      }
    })

    res.json({
      success: true,
      data: zones
    })
  } catch (error) {
    console.error('Get zones error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to get zones'
    })
  }
})

// Search rooms
router.get('/search/rooms', async (req, res) => {
  try {
    const { query, building, floor, type } = req.query

    if (!query || query.trim().length < 2) {
      return res.status(400).json({
        success: false,
        message: 'Search query must be at least 2 characters'
      })
    }

    let allRooms = []
    
    // Collect all rooms
    Object.values(sampleMapData.floors).forEach(floorData => {
      allRooms = allRooms.concat(floorData.rooms)
    })

    // Filter rooms based on criteria
    let filteredRooms = allRooms.filter(room => {
      const matchesQuery = room.name.toLowerCase().includes(query.toLowerCase()) ||
                          room.id.toLowerCase().includes(query.toLowerCase())
      const matchesBuilding = !building || room.building === building
      const matchesFloor = !floor || room.floor === parseInt(floor)
      const matchesType = !type || room.type === type

      return matchesQuery && matchesBuilding && matchesFloor && matchesType
    })

    res.json({
      success: true,
      data: filteredRooms
    })
  } catch (error) {
    console.error('Search rooms error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to search rooms'
    })
  }
})

// Get navigation path between two points
router.post('/navigate', async (req, res) => {
  try {
    const { from, to } = req.body

    if (!from || !to) {
      return res.status(400).json({
        success: false,
        message: 'From and to coordinates are required'
      })
    }

    // Simple pathfinding (in a real app, you'd use A* or similar algorithm)
    const path = [
      from,
      to // Direct path for now
    ]

    const distance = Math.sqrt(
      Math.pow(to.x - from.x, 2) + Math.pow(to.y - from.y, 2)
    )

    res.json({
      success: true,
      data: {
        path,
        distance: Math.round(distance),
        estimatedTime: Math.ceil(distance / 50), // Assuming 50 units per minute walking speed
        instructions: [
          `Head ${to.x > from.x ? 'east' : 'west'} towards your destination`,
          'You have arrived at your destination'
        ]
      }
    })
  } catch (error) {
    console.error('Navigation error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to calculate navigation'
    })
  }
})

// Get floor plan SVG (placeholder)
router.get('/:building/:floor/svg', async (req, res) => {
  try {
    const { building, floor } = req.params
    
    // In a real app, you'd serve the actual SVG file
    const svgContent = `
      <svg width="800" height="600" xmlns="http://www.w3.org/2000/svg">
        <rect width="800" height="600" fill="#f8fafc" stroke="#e2e8f0" stroke-width="2"/>
        <text x="400" y="300" text-anchor="middle" font-size="24" fill="#64748b">
          ${building.toUpperCase()} Building - Floor ${floor}
        </text>
        <text x="400" y="330" text-anchor="middle" font-size="16" fill="#94a3b8">
          SVG floor plan will be loaded here
        </text>
      </svg>
    `

    res.setHeader('Content-Type', 'image/svg+xml')
    res.send(svgContent)
  } catch (error) {
    console.error('Get SVG error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to get floor plan SVG'
    })
  }
})

module.exports = router
