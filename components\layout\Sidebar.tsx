'use client'

import { motion, AnimatePresence } from 'framer-motion'
import { 
  MapPin, 
  Users, 
  Settings, 
  X, 
  Home,
  Bell,
  HelpCircle,
  Shield
} from 'lucide-react'
import { Button } from '@/components/ui/Button'

interface SidebarProps {
  isOpen: boolean
  onClose: () => void
  activeView: 'map' | 'friends' | 'settings'
  onViewChange: (view: 'map' | 'friends' | 'settings') => void
}

const navigationItems = [
  {
    id: 'map' as const,
    label: 'Campus Map',
    icon: MapPin,
    description: 'Interactive campus map'
  },
  {
    id: 'friends' as const,
    label: 'Friends',
    icon: Users,
    description: 'Manage your friends'
  },
  {
    id: 'settings' as const,
    label: 'Settings',
    icon: Settings,
    description: 'App preferences'
  }
]

const secondaryItems = [
  {
    id: 'notifications',
    label: 'Notifications',
    icon: Bell,
    description: 'Notification settings'
  },
  {
    id: 'privacy',
    label: 'Privacy',
    icon: Shield,
    description: 'Privacy controls'
  },
  {
    id: 'help',
    label: 'Help & Support',
    icon: HelpCircle,
    description: 'Get help'
  }
]

export function Sidebar({ isOpen, onClose, activeView, onViewChange }: SidebarProps) {
  const handleItemClick = (viewId: 'map' | 'friends' | 'settings') => {
    onViewChange(viewId)
    onClose() // Close sidebar on mobile after selection
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Sidebar */}
          <motion.aside
            initial={{ x: -300 }}
            animate={{ x: 0 }}
            exit={{ x: -300 }}
            transition={{ type: 'spring', damping: 25, stiffness: 200 }}
            className="fixed lg:relative inset-y-0 left-0 z-50 w-72 bg-white border-r border-gray-200 flex flex-col"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                  <MapPin className="w-5 h-5 text-white" />
                </div>
                <span className="text-lg font-semibold text-gray-900">Menu</span>
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={onClose}
                className="lg:hidden"
              >
                <X className="w-5 h-5" />
              </Button>
            </div>

            {/* Navigation */}
            <nav className="flex-1 p-4">
              {/* Primary Navigation */}
              <div className="space-y-2">
                <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">
                  Main
                </h3>
                {navigationItems.map((item) => {
                  const Icon = item.icon
                  const isActive = activeView === item.id
                  
                  return (
                    <button
                      key={item.id}
                      onClick={() => handleItemClick(item.id)}
                      className={`w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-left transition-colors ${
                        isActive
                          ? 'bg-primary-50 text-primary-700 border border-primary-200'
                          : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      <Icon className={`w-5 h-5 ${isActive ? 'text-primary-600' : 'text-gray-500'}`} />
                      <div>
                        <p className="font-medium">{item.label}</p>
                        <p className="text-xs text-gray-500">{item.description}</p>
                      </div>
                    </button>
                  )
                })}
              </div>

              {/* Secondary Navigation */}
              <div className="mt-8 space-y-2">
                <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3">
                  More
                </h3>
                {secondaryItems.map((item) => {
                  const Icon = item.icon
                  
                  return (
                    <button
                      key={item.id}
                      className="w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-left text-gray-700 hover:bg-gray-100 transition-colors"
                    >
                      <Icon className="w-5 h-5 text-gray-500" />
                      <div>
                        <p className="font-medium">{item.label}</p>
                        <p className="text-xs text-gray-500">{item.description}</p>
                      </div>
                    </button>
                  )
                })}
              </div>
            </nav>

            {/* Footer */}
            <div className="p-4 border-t">
              <div className="bg-primary-50 rounded-lg p-3">
                <div className="flex items-center space-x-2 mb-2">
                  <div className="w-6 h-6 bg-primary-600 rounded-full flex items-center justify-center">
                    <MapPin className="w-3 h-3 text-white" />
                  </div>
                  <span className="text-sm font-medium text-primary-900">
                    SYK SchoolFinder
                  </span>
                </div>
                <p className="text-xs text-primary-700">
                  Stay connected with your campus community
                </p>
              </div>
            </div>
          </motion.aside>
        </>
      )}
    </AnimatePresence>
  )
}
