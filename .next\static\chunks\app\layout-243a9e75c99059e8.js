(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[185],{4710:function(e,t,i){Promise.resolve().then(i.bind(i,4646)),Promise.resolve().then(i.t.bind(i,3445,23)),Promise.resolve().then(i.t.bind(i,2445,23)),Promise.resolve().then(i.bind(i,5925))},4646:function(e,t,i){"use strict";i.r(t),i.d(t,{Providers:function(){return f}});var r=i(7437),n=i(2749),a=i(2265),o=i(7700),s=i(4642),l=i(4337),d=i(4660),u=i(4810);let c=i(4829).Z.create({baseURL:"http://localhost:3001/api",timeout:1e4,headers:{"Content-Type":"application/json"}});c.interceptors.request.use(async e=>{try{let t=await (0,n.getSession)();(null==t?void 0:t.accessToken)&&(e.headers.Authorization="Bearer ".concat(t.accessToken))}catch(e){console.error("Error getting session:",e)}return e},e=>Promise.reject(e)),c.interceptors.response.use(e=>e,e=>{var t;return(null===(t=e.response)||void 0===t?void 0:t.status)===401&&(window.location.href="/auth/signin"),Promise.reject(e)});let g=(0,d.Ue)()((0,u.tJ)((e,t)=>({user:null,isAuthenticated:!1,isLoading:!1,error:null,setUser:t=>{e({user:t,isAuthenticated:!!t,error:null})},setLoading:t=>{e({isLoading:t})},setError:t=>{e({error:t})},login:async(t,i)=>{e({isLoading:!0,error:null});try{let r=await c.post("/auth/login",{email:t,password:i});if(!r.data.success)return e({error:r.data.message||"Login failed",isLoading:!1}),!1;{let t=r.data.data.user;return e({user:t,isAuthenticated:!0,isLoading:!1,error:null}),!0}}catch(t){var r,n;return e({error:(null===(n=t.response)||void 0===n?void 0:null===(r=n.data)||void 0===r?void 0:r.message)||"Login failed",isLoading:!1}),!1}},register:async(t,i,r)=>{e({isLoading:!0,error:null});try{let n=await c.post("/auth/register",{name:t,email:i,password:r});if(!n.data.success)return e({error:n.data.message||"Registration failed",isLoading:!1}),!1;{let t=n.data.data.user;return e({user:t,isAuthenticated:!0,isLoading:!1,error:null}),!0}}catch(t){var n,a;return e({error:(null===(a=t.response)||void 0===a?void 0:null===(n=a.data)||void 0===n?void 0:n.message)||"Registration failed",isLoading:!1}),!1}},logout:()=>{e({user:null,isAuthenticated:!1,error:null}),localStorage.removeItem("auth-storage")},updateProfile:async i=>{let{user:r}=t();if(!r)return!1;e({isLoading:!0,error:null});try{let t=await c.put("/auth/profile",i);if(t.data.success)return e({user:{...r,...t.data.data},isLoading:!1,error:null}),!0;return e({error:t.data.message||"Profile update failed",isLoading:!1}),!1}catch(t){var n,a;return e({error:(null===(a=t.response)||void 0===a?void 0:null===(n=a.data)||void 0===n?void 0:n.message)||"Profile update failed",isLoading:!1}),!1}},updatePrivacySettings:async i=>{let{user:r}=t();if(!r)return!1;e({isLoading:!0,error:null});try{let t=await c.put("/auth/privacy",{privacySettings:i});if(t.data.success)return e({user:{...r,privacySettings:i},isLoading:!1,error:null}),!0;return e({error:t.data.message||"Privacy settings update failed",isLoading:!1}),!1}catch(t){var n,a;return e({error:(null===(a=t.response)||void 0===a?void 0:null===(n=a.data)||void 0===n?void 0:n.message)||"Privacy settings update failed",isLoading:!1}),!1}},initialize:()=>{let t=localStorage.getItem("auth-storage");if(t)try{var i;let r=JSON.parse(t);(null===(i=r.state)||void 0===i?void 0:i.user)&&e({user:r.state.user,isAuthenticated:!0})}catch(e){console.error("Failed to restore auth state:",e)}}}),{name:"auth-storage",partialize:e=>({user:e.user,isAuthenticated:e.isAuthenticated})})),L={zoom:1,center:{x:0,y:0},currentFloor:1,currentBuilding:"main"},v=(0,d.Ue)((e,t)=>({currentLocation:null,friendLocations:[],mapViewState:L,isLocationEnabled:!1,isLoading:!1,error:null,lastUpdate:null,setCurrentLocation:t=>{e({currentLocation:t,lastUpdate:new Date,error:null})},setFriendLocations:t=>{e({friendLocations:t})},updateFriendLocation:i=>{let{friendLocations:r}=t(),n=r.findIndex(e=>e.friend.id===i.friend.id);if(n>=0){let t=[...r];t[n]=i,e({friendLocations:t})}else e({friendLocations:[...r,i]})},removeFriendLocation:i=>{let{friendLocations:r}=t();e({friendLocations:r.filter(e=>e.friend.id!==i)})},setMapViewState:i=>{let{mapViewState:r}=t();e({mapViewState:{...r,...i}})},setLocationEnabled:t=>{e({isLocationEnabled:t})},setLoading:t=>{e({isLoading:t})},setError:t=>{e({error:t})},requestLocationPermission:async()=>{if(!navigator.geolocation)return e({error:"Geolocation is not supported by this browser"}),!1;try{let t=await navigator.permissions.query({name:"geolocation"});if("granted"===t.state)return e({isLocationEnabled:!0,error:null}),!0;if("prompt"===t.state)return!0;return e({error:"Location permission denied. Please enable location access in your browser settings.",isLocationEnabled:!1}),!1}catch(e){return console.error("Error checking location permission:",e),!0}},getCurrentPosition:async()=>new Promise(t=>{if(!navigator.geolocation){e({error:"Geolocation is not supported"}),t(null);return}navigator.geolocation.getCurrentPosition(i=>{e({isLocationEnabled:!0,error:null}),t(i)},i=>{let r="Failed to get location";switch(i.code){case i.PERMISSION_DENIED:r="Location access denied by user";break;case i.POSITION_UNAVAILABLE:r="Location information unavailable";break;case i.TIMEOUT:r="Location request timed out"}e({error:r,isLocationEnabled:!1}),t(null)},{enableHighAccuracy:!0,timeout:1e4,maximumAge:6e4})}),updateLocation:async t=>{e({isLoading:!0,error:null});try{let i=await c.post("/location/update",{coordinates:t,timestamp:new Date().toISOString()});if(!i.data.success)return e({error:i.data.message||"Failed to update location",isLoading:!1}),!1;{let t=i.data.data;return e({currentLocation:t,isLoading:!1,lastUpdate:new Date,error:null}),!0}}catch(t){var i,r;return e({error:(null===(r=t.response)||void 0===r?void 0:null===(i=r.data)||void 0===i?void 0:i.message)||"Failed to update location",isLoading:!1}),!1}},shareLocation:async t=>{e({isLoading:!0,error:null});try{let i=await c.post("/location/share",{enabled:t});if(i.data.success)return e({isLoading:!1,error:null}),!0;return e({error:i.data.message||"Failed to update location sharing",isLoading:!1}),!1}catch(t){var i,r;return e({error:(null===(r=t.response)||void 0===r?void 0:null===(i=r.data)||void 0===i?void 0:i.message)||"Failed to update location sharing",isLoading:!1}),!1}},getFriendLocations:async()=>{e({isLoading:!0,error:null});try{let t=await c.get("/location/friends");t.data.success?e({friendLocations:t.data.data,isLoading:!1,error:null}):e({error:t.data.message||"Failed to get friend locations",isLoading:!1})}catch(r){var t,i;e({error:(null===(i=r.response)||void 0===i?void 0:null===(t=i.data)||void 0===t?void 0:t.message)||"Failed to get friend locations",isLoading:!1})}},initialize:()=>{let{requestLocationPermission:e}=t();e()}})),m=(0,a.createContext)({socket:null,isConnected:!1});function p(e){let{children:t}=e,[i,o]=(0,a.useState)(null),[s,d]=(0,a.useState)(!1),{data:u}=(0,n.useSession)(),{user:c}=g(),{updateFriendLocation:L,removeFriendLocation:p}=v();return(0,a.useEffect)(()=>{var e;if(!c&&!u)return;let t=(0,l.io)("http://localhost:3001",{auth:{userId:(null==c?void 0:c.id)||(null==u?void 0:null===(e=u.user)||void 0===e?void 0:e.id),token:null==u?void 0:u.accessToken},transports:["websocket","polling"]});return t.on("connect",()=>{console.log("Connected to server"),d(!0)}),t.on("disconnect",()=>{console.log("Disconnected from server"),d(!1)}),t.on("connect_error",e=>{console.error("Connection error:",e),d(!1)}),t.on("location:update",e=>{console.log("Friend location update:",e)}),t.on("friend:online",e=>{console.log("Friend online status:",e)}),t.on("friend:request",e=>{console.log("New friend request:",e)}),t.on("friend:accepted",e=>{console.log("Friend request accepted:",e)}),t.on("error",e=>{console.error("Socket error:",e)}),o(t),()=>{t.disconnect(),o(null),d(!1)}},[c,u,L,p]),(0,r.jsx)(m.Provider,{value:{socket:i,isConnected:s},children:t})}function f(e){let{children:t}=e,i=(0,o.t)(e=>e.initialize),l=(0,s.o)(e=>e.initialize);return(0,a.useEffect)(()=>{i(),l()},[i,l]),(0,r.jsx)(n.SessionProvider,{children:(0,r.jsx)(p,{children:t})})}},7700:function(e,t,i){"use strict";i.d(t,{t:function(){return a}});var r=i(4660),n=i(4810);let a=(0,r.Ue)()((0,n.tJ)((e,t)=>({user:null,isAuthenticated:!1,isLoading:!1,error:null,setUser:t=>{e({user:t,isAuthenticated:!!t,error:null})},setLoading:t=>{e({isLoading:t})},setError:t=>{e({error:t})},login:async(t,i)=>(e({isLoading:!0,error:null}),await new Promise(e=>setTimeout(e,1e3)),t&&i)?(e({user:{id:"1",email:t,name:t.split("@")[0],image:void 0,avatar:void 0,isOnline:!0,lastSeen:new Date,privacySettings:{shareLocation:!0,invisibleMode:!1,allowFriendRequests:!0,showOnlineStatus:!0},createdAt:new Date,updatedAt:new Date},isAuthenticated:!0,isLoading:!1,error:null}),!0):(e({error:"Please enter email and password",isLoading:!1}),!1),register:async(t,i,r)=>(e({isLoading:!0,error:null}),await new Promise(e=>setTimeout(e,1e3)),t&&i&&r)?(e({user:{id:"1",email:i,name:t,image:void 0,avatar:void 0,isOnline:!0,lastSeen:new Date,privacySettings:{shareLocation:!0,invisibleMode:!1,allowFriendRequests:!0,showOnlineStatus:!0},createdAt:new Date,updatedAt:new Date},isAuthenticated:!0,isLoading:!1,error:null}),!0):(e({error:"Please fill in all fields",isLoading:!1}),!1),logout:()=>{e({user:null,isAuthenticated:!1,error:null}),localStorage.removeItem("auth-storage")},updateProfile:async i=>{let{user:r}=t();return!!r&&(e({isLoading:!0,error:null}),await new Promise(e=>setTimeout(e,500)),e({user:{...r,...i},isLoading:!1,error:null}),!0)},updatePrivacySettings:async i=>{let{user:r}=t();return!!r&&(e({isLoading:!0,error:null}),await new Promise(e=>setTimeout(e,500)),e({user:{...r,privacySettings:i},isLoading:!1,error:null}),!0)},initialize:()=>{let t=localStorage.getItem("auth-storage");if(t)try{var i;let r=JSON.parse(t);(null===(i=r.state)||void 0===i?void 0:i.user)&&e({user:r.state.user,isAuthenticated:!0})}catch(e){console.error("Failed to restore auth state:",e)}}}),{name:"auth-storage",partialize:e=>({user:e.user,isAuthenticated:e.isAuthenticated})}))},4642:function(e,t,i){"use strict";i.d(t,{o:function(){return o}});var r=i(4660);let n={zoom:1,center:{x:0,y:0},currentFloor:1,currentBuilding:"main"},a=[{id:"1",userId:"2",coordinates:{x:150,y:100,floor:1,building:"main"},timestamp:new Date(Date.now()-3e5),isActive:!0,friend:{id:"2",email:"<EMAIL>",name:"Emma Johnson",image:void 0,avatar:void 0,isOnline:!0,lastSeen:new Date(Date.now()-12e4),privacySettings:{shareLocation:!0,invisibleMode:!1,allowFriendRequests:!0,showOnlineStatus:!0},createdAt:new Date,updatedAt:new Date}},{id:"2",userId:"3",coordinates:{x:400,y:200,floor:1,building:"main"},timestamp:new Date(Date.now()-6e4),isActive:!0,friend:{id:"3",email:"<EMAIL>",name:"Alex Chen",image:void 0,avatar:void 0,isOnline:!0,lastSeen:new Date(Date.now()-3e4),privacySettings:{shareLocation:!0,invisibleMode:!1,allowFriendRequests:!0,showOnlineStatus:!0},createdAt:new Date,updatedAt:new Date}}],o=(0,r.Ue)((e,t)=>({currentLocation:{id:"current",userId:"1",coordinates:{x:250,y:150,floor:1,building:"main"},timestamp:new Date,isActive:!0},friendLocations:a,mapViewState:n,isLocationEnabled:!0,isLoading:!1,error:null,lastUpdate:new Date,setCurrentLocation:t=>{e({currentLocation:t,lastUpdate:new Date,error:null})},setFriendLocations:t=>{e({friendLocations:t})},updateFriendLocation:i=>{let{friendLocations:r}=t(),n=r.findIndex(e=>e.friend.id===i.friend.id);if(n>=0){let t=[...r];t[n]=i,e({friendLocations:t})}else e({friendLocations:[...r,i]})},removeFriendLocation:i=>{let{friendLocations:r}=t();e({friendLocations:r.filter(e=>e.friend.id!==i)})},setMapViewState:i=>{let{mapViewState:r}=t();e({mapViewState:{...r,...i}})},setLocationEnabled:t=>{e({isLocationEnabled:t})},setLoading:t=>{e({isLoading:t})},setError:t=>{e({error:t})},requestLocationPermission:async()=>(e({isLoading:!0,error:null}),await new Promise(e=>setTimeout(e,1e3)),e({isLocationEnabled:!0,isLoading:!1,error:null}),!0),getCurrentPosition:async()=>(await new Promise(e=>setTimeout(e,500)),{coords:{latitude:60.1699,longitude:24.9384,accuracy:10,altitude:null,altitudeAccuracy:null,heading:null,speed:null},timestamp:Date.now()}),updateLocation:async t=>(e({isLoading:!0,error:null}),await new Promise(e=>setTimeout(e,500)),e({currentLocation:{id:"current",userId:"1",coordinates:t,timestamp:new Date,isActive:!0},isLoading:!1,lastUpdate:new Date,error:null}),!0),shareLocation:async t=>(e({isLoading:!0,error:null}),await new Promise(e=>setTimeout(e,500)),e({isLoading:!1,error:null}),!0),getFriendLocations:async()=>{e({isLoading:!0,error:null}),await new Promise(e=>setTimeout(e,500)),e({friendLocations:a,isLoading:!1,error:null})},initialize:()=>{e({isLocationEnabled:!0,friendLocations:a})}}))},2445:function(){}},function(e){e.O(0,[375,821,971,938,744],function(){return e(e.s=4710)}),_N_E=e.O()}]);