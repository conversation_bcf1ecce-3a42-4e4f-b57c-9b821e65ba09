exports.id=129,exports.ids=[129],exports.modules={69224:(t,e,r)=>{"use strict";r.d(e,{Z:()=>o});var i=r(3729),n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),o=(t,e)=>{let r=(0,i.forwardRef)(({color:r="currentColor",size:o=24,strokeWidth:a=2,absoluteStrokeWidth:l,className:u="",children:h,...c},d)=>(0,i.createElement)("svg",{ref:d,...n,width:o,height:o,stroke:r,strokeWidth:l?24*Number(a)/Number(o):a,className:["lucide",`lucide-${s(t)}`,u].join(" "),...c},[...e.map(([t,e])=>(0,i.createElement)(t,e)),...Array.isArray(h)?h:[h]]));return r.displayName=`${t}`,r}},1222:(t,e,r)=>{"use strict";r.d(e,{Z:()=>i});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,r(69224).Z)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},53148:(t,e,r)=>{"use strict";r.d(e,{Z:()=>i});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,r(69224).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},71206:(t,e,r)=>{"use strict";r.d(e,{Z:()=>i});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,r(69224).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},80508:(t,e,r)=>{"use strict";r.d(e,{Z:()=>i});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,r(69224).Z)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},23485:(t,e,r)=>{"use strict";r.d(e,{Z:()=>i});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,r(69224).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},18822:(t,e,r)=>{"use strict";r.d(e,{Z:()=>i});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,r(69224).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},89895:(t,e,r)=>{"use strict";r.d(e,{Z:()=>i});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,r(69224).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},14513:(t,e,r)=>{"use strict";r.d(e,{Z:()=>i});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,r(69224).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},22254:(t,e,r)=>{t.exports=r(14767)},56815:(t,e,r)=>{"use strict";function i(){for(var t,e,r=0,i="",n=arguments.length;r<n;r++)(t=arguments[r])&&(e=function t(e){var r,i,n="";if("string"==typeof e||"number"==typeof e)n+=e;else if("object"==typeof e){if(Array.isArray(e)){var s=e.length;for(r=0;r<s;r++)e[r]&&(i=t(e[r]))&&(n&&(n+=" "),n+=i)}else for(i in e)e[i]&&(n&&(n+=" "),n+=i)}return n}(t))&&(i&&(i+=" "),i+=e);return i}r.d(e,{W:()=>i})},73644:(t,e,r)=>{"use strict";r.d(e,{M:()=>g});var i=r(3729),n=r(19038);function s(){let t=(0,i.useRef)(!1);return(0,n.L)(()=>(t.current=!0,()=>{t.current=!1}),[]),t}var o=r(80228),a=r(35986),l=r(40207);class u extends i.Component{getSnapshotBeforeUpdate(t){let e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){let t=this.props.sizeRef.current;t.height=e.offsetHeight||0,t.width=e.offsetWidth||0,t.top=e.offsetTop,t.left=e.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function h({children:t,isPresent:e}){let r=(0,i.useId)(),n=(0,i.useRef)(null),s=(0,i.useRef)({width:0,height:0,top:0,left:0});return(0,i.useInsertionEffect)(()=>{let{width:t,height:i,top:o,left:a}=s.current;if(e||!n.current||!t||!i)return;n.current.dataset.motionPopId=r;let l=document.createElement("style");return document.head.appendChild(l),l.sheet&&l.sheet.insertRule(`
          [data-motion-pop-id="${r}"] {
            position: absolute !important;
            width: ${t}px !important;
            height: ${i}px !important;
            top: ${o}px !important;
            left: ${a}px !important;
          }
        `),()=>{document.head.removeChild(l)}},[e]),i.createElement(u,{isPresent:e,childRef:n,sizeRef:s},i.cloneElement(t,{ref:n}))}let c=({children:t,initial:e,isPresent:r,onExitComplete:n,custom:s,presenceAffectsLayout:o,mode:u})=>{let c=(0,l.h)(d),p=(0,i.useId)(),m=(0,i.useMemo)(()=>({id:p,initial:e,isPresent:r,custom:s,onExitComplete:t=>{for(let e of(c.set(t,!0),c.values()))if(!e)return;n&&n()},register:t=>(c.set(t,!1),()=>c.delete(t))}),o?void 0:[r]);return(0,i.useMemo)(()=>{c.forEach((t,e)=>c.set(e,!1))},[r]),i.useEffect(()=>{r||c.size||!n||n()},[r]),"popLayout"===u&&(t=i.createElement(h,{isPresent:r},t)),i.createElement(a.O.Provider,{value:m},t)};function d(){return new Map}var p=r(66828),m=r(87222);let f=t=>t.key||"",g=({children:t,custom:e,initial:r=!0,onExitComplete:a,exitBeforeEnter:l,presenceAffectsLayout:u=!0,mode:h="sync"})=>{var d;(0,m.k)(!l,"Replace exitBeforeEnter with mode='wait'");let g=(0,i.useContext)(p.p).forceRender||function(){let t=s(),[e,r]=(0,i.useState)(0),n=(0,i.useCallback)(()=>{t.current&&r(e+1)},[e]);return[(0,i.useCallback)(()=>o.Wi.postRender(n),[n]),e]}()[0],y=s(),v=function(t){let e=[];return i.Children.forEach(t,t=>{(0,i.isValidElement)(t)&&e.push(t)}),e}(t),x=v,b=(0,i.useRef)(new Map).current,w=(0,i.useRef)(x),k=(0,i.useRef)(new Map).current,P=(0,i.useRef)(!0);if((0,n.L)(()=>{P.current=!1,function(t,e){t.forEach(t=>{let r=f(t);e.set(r,t)})}(v,k),w.current=x}),d=()=>{P.current=!0,k.clear(),b.clear()},(0,i.useEffect)(()=>()=>d(),[]),P.current)return i.createElement(i.Fragment,null,x.map(t=>i.createElement(c,{key:f(t),isPresent:!0,initial:!!r&&void 0,presenceAffectsLayout:u,mode:h},t)));x=[...x];let A=w.current.map(f),T=v.map(f),S=A.length;for(let t=0;t<S;t++){let e=A[t];-1!==T.indexOf(e)||b.has(e)||b.set(e,void 0)}return"wait"===h&&b.size&&(x=[]),b.forEach((t,r)=>{if(-1!==T.indexOf(r))return;let n=k.get(r);if(!n)return;let s=A.indexOf(r),o=t;o||(o=i.createElement(c,{key:f(n),isPresent:!1,onExitComplete:()=>{b.delete(r);let t=Array.from(k.keys()).filter(t=>!T.includes(t));if(t.forEach(t=>k.delete(t)),w.current=v.filter(e=>{let i=f(e);return i===r||t.includes(i)}),!b.size){if(!1===y.current)return;g(),a&&a()}},custom:e,presenceAffectsLayout:u,mode:h},n),b.set(r,o)),x.splice(s,0,o)}),x=x.map(t=>{let e=t.key;return b.has(e)?t:i.createElement(c,{key:f(t),isPresent:!0,presenceAffectsLayout:u,mode:h},t)}),i.createElement(i.Fragment,null,b.size?x:x.map(t=>(0,i.cloneElement)(t)))}},66828:(t,e,r)=>{"use strict";r.d(e,{p:()=>i});let i=(0,r(3729).createContext)({})},35986:(t,e,r)=>{"use strict";r.d(e,{O:()=>i});let i=(0,r(3729).createContext)(null)},80228:(t,e,r)=>{"use strict";r.d(e,{Pn:()=>a,Wi:()=>o,frameData:()=>l,S6:()=>u});var i=r(30254);class n{constructor(){this.order=[],this.scheduled=new Set}add(t){if(!this.scheduled.has(t))return this.scheduled.add(t),this.order.push(t),!0}remove(t){let e=this.order.indexOf(t);-1!==e&&(this.order.splice(e,1),this.scheduled.delete(t))}clear(){this.order.length=0,this.scheduled.clear()}}let s=["prepare","read","update","preRender","render","postRender"],{schedule:o,cancel:a,state:l,steps:u}=function(t,e){let r=!1,i=!0,o={delta:0,timestamp:0,isProcessing:!1},a=s.reduce((t,e)=>(t[e]=function(t){let e=new n,r=new n,i=0,s=!1,o=!1,a=new WeakSet,l={schedule:(t,n=!1,o=!1)=>{let l=o&&s,u=l?e:r;return n&&a.add(t),u.add(t)&&l&&s&&(i=e.order.length),t},cancel:t=>{r.remove(t),a.delete(t)},process:n=>{if(s){o=!0;return}if(s=!0,[e,r]=[r,e],r.clear(),i=e.order.length)for(let r=0;r<i;r++){let i=e.order[r];i(n),a.has(i)&&(l.schedule(i),t())}s=!1,o&&(o=!1,l.process(n))}};return l}(()=>r=!0),t),{}),l=t=>a[t].process(o),u=()=>{let n=performance.now();r=!1,o.delta=i?1e3/60:Math.max(Math.min(n-o.timestamp,40),1),o.timestamp=n,o.isProcessing=!0,s.forEach(l),o.isProcessing=!1,r&&e&&(i=!1,t(u))},h=()=>{r=!0,i=!0,o.isProcessing||t(u)};return{schedule:s.reduce((t,e)=>{let i=a[e];return t[e]=(t,e=!1,n=!1)=>(r||h(),i.schedule(t,e,n)),t},{}),cancel:t=>s.forEach(e=>a[e].cancel(t)),state:o,steps:a}}("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:i.Z,!0)},57916:(t,e,r)=>{"use strict";r.d(e,{E:()=>n$});var i=r(3729);let n=(0,i.createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),s=(0,i.createContext)({});var o=r(35986),a=r(19038);let l=(0,i.createContext)({strict:!1}),u=t=>t.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),h="data-"+u("framerAppearId");function c(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}function d(t){return"string"==typeof t||Array.isArray(t)}function p(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}let m=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],f=["initial",...m];function g(t){return p(t.animate)||f.some(e=>d(t[e]))}function y(t){return!!(g(t)||t.variants)}function v(t){return Array.isArray(t)?t.join(" "):t}let x={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},b={};for(let t in x)b[t]={isEnabled:e=>x[t].some(t=>!!e[t])};var w=r(79398),k=r(66828);let P=(0,i.createContext)({}),A=Symbol.for("motionComponentSymbol"),T=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function S(t){if("string"!=typeof t||t.includes("-"));else if(T.indexOf(t)>-1||/[A-Z]/.test(t))return!0;return!1}let E={},V=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],M=new Set(V);function C(t,{layout:e,layoutId:r}){return M.has(t)||t.startsWith("origin")||(e||void 0!==r)&&(!!E[t]||"opacity"===t)}let D=t=>!!(t&&t.getVelocity),R={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},j=V.length,L=t=>e=>"string"==typeof e&&e.startsWith(t),F=L("--"),B=L("var(--"),z=(t,e)=>e&&"number"==typeof t?e.transform(t):t,O=(t,e,r)=>Math.min(Math.max(r,t),e),I={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},W={...I,transform:t=>O(0,1,t)},U={...I,default:1},$=t=>Math.round(1e5*t)/1e5,N=/(-)?([\d]*\.?[\d])+/g,Z=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,H=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function G(t){return"string"==typeof t}let Y=t=>({test:e=>G(e)&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),X=Y("deg"),q=Y("%"),K=Y("px"),_=Y("vh"),J=Y("vw"),Q={...q,parse:t=>q.parse(t)/100,transform:t=>q.transform(100*t)},tt={...I,transform:Math.round},te={borderWidth:K,borderTopWidth:K,borderRightWidth:K,borderBottomWidth:K,borderLeftWidth:K,borderRadius:K,radius:K,borderTopLeftRadius:K,borderTopRightRadius:K,borderBottomRightRadius:K,borderBottomLeftRadius:K,width:K,maxWidth:K,height:K,maxHeight:K,size:K,top:K,right:K,bottom:K,left:K,padding:K,paddingTop:K,paddingRight:K,paddingBottom:K,paddingLeft:K,margin:K,marginTop:K,marginRight:K,marginBottom:K,marginLeft:K,rotate:X,rotateX:X,rotateY:X,rotateZ:X,scale:U,scaleX:U,scaleY:U,scaleZ:U,skew:X,skewX:X,skewY:X,distance:K,translateX:K,translateY:K,translateZ:K,x:K,y:K,z:K,perspective:K,transformPerspective:K,opacity:W,originX:Q,originY:Q,originZ:K,zIndex:tt,fillOpacity:W,strokeOpacity:W,numOctaves:tt};function tr(t,e,r,i){let{style:n,vars:s,transform:o,transformOrigin:a}=t,l=!1,u=!1,h=!0;for(let t in e){let r=e[t];if(F(t)){s[t]=r;continue}let i=te[t],c=z(r,i);if(M.has(t)){if(l=!0,o[t]=c,!h)continue;r!==(i.default||0)&&(h=!1)}else t.startsWith("origin")?(u=!0,a[t]=c):n[t]=c}if(!e.transform&&(l||i?n.transform=function(t,{enableHardwareAcceleration:e=!0,allowTransformNone:r=!0},i,n){let s="";for(let e=0;e<j;e++){let r=V[e];if(void 0!==t[r]){let e=R[r]||r;s+=`${e}(${t[r]}) `}}return e&&!t.z&&(s+="translateZ(0)"),s=s.trim(),n?s=n(t,i?"":s):r&&i&&(s="none"),s}(t.transform,r,h,i):n.transform&&(n.transform="none")),u){let{originX:t="50%",originY:e="50%",originZ:r=0}=a;n.transformOrigin=`${t} ${e} ${r}`}}let ti=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function tn(t,e,r){for(let i in e)D(e[i])||C(i,r)||(t[i]=e[i])}let ts=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function to(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||ts.has(t)}let ta=t=>!to(t);try{!function(t){t&&(ta=e=>e.startsWith("on")?!to(e):t(e))}(require("@emotion/is-prop-valid").default)}catch(t){}function tl(t,e,r){return"string"==typeof t?t:K.transform(e+r*t)}let tu={offset:"stroke-dashoffset",array:"stroke-dasharray"},th={offset:"strokeDashoffset",array:"strokeDasharray"};function tc(t,{attrX:e,attrY:r,attrScale:i,originX:n,originY:s,pathLength:o,pathSpacing:a=1,pathOffset:l=0,...u},h,c,d){if(tr(t,u,h,d),c){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:p,style:m,dimensions:f}=t;p.transform&&(f&&(m.transform=p.transform),delete p.transform),f&&(void 0!==n||void 0!==s||m.transform)&&(m.transformOrigin=function(t,e,r){let i=tl(e,t.x,t.width),n=tl(r,t.y,t.height);return`${i} ${n}`}(f,void 0!==n?n:.5,void 0!==s?s:.5)),void 0!==e&&(p.x=e),void 0!==r&&(p.y=r),void 0!==i&&(p.scale=i),void 0!==o&&function(t,e,r=1,i=0,n=!0){t.pathLength=1;let s=n?tu:th;t[s.offset]=K.transform(-i);let o=K.transform(e),a=K.transform(r);t[s.array]=`${o} ${a}`}(p,o,a,l,!1)}let td=()=>({...ti(),attrs:{}}),tp=t=>"string"==typeof t&&"svg"===t.toLowerCase();function tm(t,{style:e,vars:r},i,n){for(let s in Object.assign(t.style,e,n&&n.getProjectionStyles(i)),r)t.style.setProperty(s,r[s])}let tf=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function tg(t,e,r,i){for(let r in tm(t,e,void 0,i),e.attrs)t.setAttribute(tf.has(r)?r:u(r),e.attrs[r])}function ty(t,e){let{style:r}=t,i={};for(let n in r)(D(r[n])||e.style&&D(e.style[n])||C(n,t))&&(i[n]=r[n]);return i}function tv(t,e){let r=ty(t,e);for(let i in t)(D(t[i])||D(e[i]))&&(r[-1!==V.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return r}function tx(t,e,r,i={},n={}){return"function"==typeof e&&(e=e(void 0!==r?r:t.custom,i,n)),"string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e&&(e=e(void 0!==r?r:t.custom,i,n)),e}var tb=r(40207);let tw=t=>Array.isArray(t),tk=t=>!!(t&&"object"==typeof t&&t.mix&&t.toValue),tP=t=>tw(t)?t[t.length-1]||0:t;function tA(t){let e=D(t)?t.get():t;return tk(e)?e.toValue():e}let tT=t=>(e,r)=>{let n=(0,i.useContext)(s),a=(0,i.useContext)(o.O),l=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e,onMount:r},i,n,s){let o={latestValues:function(t,e,r,i){let n={},s=i(t,{});for(let t in s)n[t]=tA(s[t]);let{initial:o,animate:a}=t,l=g(t),u=y(t);e&&u&&!l&&!1!==t.inherit&&(void 0===o&&(o=e.initial),void 0===a&&(a=e.animate));let h=!!r&&!1===r.initial,c=(h=h||!1===o)?a:o;return c&&"boolean"!=typeof c&&!p(c)&&(Array.isArray(c)?c:[c]).forEach(e=>{let r=tx(t,e);if(!r)return;let{transitionEnd:i,transition:s,...o}=r;for(let t in o){let e=o[t];if(Array.isArray(e)){let t=h?e.length-1:0;e=e[t]}null!==e&&(n[t]=e)}for(let t in i)n[t]=i[t]}),n}(i,n,s,t),renderState:e()};return r&&(o.mount=t=>r(i,t,o)),o})(t,e,n,a);return r?l():(0,tb.h)(l)};var tS=r(80228);let tE={useVisualState:tT({scrapeMotionValuesFromProps:tv,createRenderState:td,onMount:(t,e,{renderState:r,latestValues:i})=>{tS.Wi.read(()=>{try{r.dimensions="function"==typeof e.getBBox?e.getBBox():e.getBoundingClientRect()}catch(t){r.dimensions={x:0,y:0,width:0,height:0}}}),tS.Wi.render(()=>{tc(r,i,{enableHardwareAcceleration:!1},tp(e.tagName),t.transformTemplate),tg(e,r)})}})},tV={useVisualState:tT({scrapeMotionValuesFromProps:ty,createRenderState:ti})};function tM(t,e,r,i={passive:!0}){return t.addEventListener(e,r,i),()=>t.removeEventListener(e,r)}let tC=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function tD(t,e="page"){return{point:{x:t[e+"X"],y:t[e+"Y"]}}}let tR=t=>e=>tC(e)&&t(e,tD(e));function tj(t,e,r,i){return tM(t,e,tR(r),i)}let tL=(t,e)=>r=>e(t(r)),tF=(...t)=>t.reduce(tL);function tB(t){let e=null;return()=>null===e&&(e=t,()=>{e=null})}let tz=tB("dragHorizontal"),tO=tB("dragVertical");function tI(t){let e=!1;if("y"===t)e=tO();else if("x"===t)e=tz();else{let t=tz(),r=tO();t&&r?e=()=>{t(),r()}:(t&&t(),r&&r())}return e}function tW(){let t=tI(!0);return!t||(t(),!1)}class tU{constructor(t){this.isMounted=!1,this.node=t}update(){}}function t$(t,e){let r="onHover"+(e?"Start":"End");return tj(t.current,"pointer"+(e?"enter":"leave"),(i,n)=>{if("touch"===i.pointerType||tW())return;let s=t.getProps();t.animationState&&s.whileHover&&t.animationState.setActive("whileHover",e),s[r]&&tS.Wi.update(()=>s[r](i,n))},{passive:!t.getProps()[r]})}class tN extends tU{mount(){this.unmount=tF(t$(this.node,!0),t$(this.node,!1))}unmount(){}}class tZ extends tU{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=tF(tM(this.node.current,"focus",()=>this.onFocus()),tM(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let tH=(t,e)=>!!e&&(t===e||tH(t,e.parentElement));var tG=r(30254);function tY(t,e){if(!e)return;let r=new PointerEvent("pointer"+t);e(r,tD(r))}class tX extends tU{constructor(){super(...arguments),this.removeStartListeners=tG.Z,this.removeEndListeners=tG.Z,this.removeAccessibleListeners=tG.Z,this.startPointerPress=(t,e)=>{if(this.isPressing)return;this.removeEndListeners();let r=this.node.getProps(),i=tj(window,"pointerup",(t,e)=>{if(!this.checkPressEnd())return;let{onTap:r,onTapCancel:i,globalTapTarget:n}=this.node.getProps();tS.Wi.update(()=>{n||tH(this.node.current,t.target)?r&&r(t,e):i&&i(t,e)})},{passive:!(r.onTap||r.onPointerUp)}),n=tj(window,"pointercancel",(t,e)=>this.cancelPress(t,e),{passive:!(r.onTapCancel||r.onPointerCancel)});this.removeEndListeners=tF(i,n),this.startPress(t,e)},this.startAccessiblePress=()=>{let t=tM(this.node.current,"keydown",t=>{"Enter"!==t.key||this.isPressing||(this.removeEndListeners(),this.removeEndListeners=tM(this.node.current,"keyup",t=>{"Enter"===t.key&&this.checkPressEnd()&&tY("up",(t,e)=>{let{onTap:r}=this.node.getProps();r&&tS.Wi.update(()=>r(t,e))})}),tY("down",(t,e)=>{this.startPress(t,e)}))}),e=tM(this.node.current,"blur",()=>{this.isPressing&&tY("cancel",(t,e)=>this.cancelPress(t,e))});this.removeAccessibleListeners=tF(t,e)}}startPress(t,e){this.isPressing=!0;let{onTapStart:r,whileTap:i}=this.node.getProps();i&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),r&&tS.Wi.update(()=>r(t,e))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!tW()}cancelPress(t,e){if(!this.checkPressEnd())return;let{onTapCancel:r}=this.node.getProps();r&&tS.Wi.update(()=>r(t,e))}mount(){let t=this.node.getProps(),e=tj(t.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(t.onTapStart||t.onPointerStart)}),r=tM(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=tF(e,r)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}let tq=new WeakMap,tK=new WeakMap,t_=t=>{let e=tq.get(t.target);e&&e(t)},tJ=t=>{t.forEach(t_)},tQ={some:0,all:1};class t0 extends tU{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:r,amount:i="some",once:n}=t,s={root:e?e.current:void 0,rootMargin:r,threshold:"number"==typeof i?i:tQ[i]};return function(t,e,r){let i=function({root:t,...e}){let r=t||document;tK.has(r)||tK.set(r,{});let i=tK.get(r),n=JSON.stringify(e);return i[n]||(i[n]=new IntersectionObserver(tJ,{root:t,...e})),i[n]}(e);return tq.set(t,r),i.observe(t),()=>{tq.delete(t),i.unobserve(t)}}(this.node.current,s,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,n&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:r,onViewportLeave:i}=this.node.getProps(),s=e?r:i;s&&s(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return r=>t[r]!==e[r]}(t,e))&&this.startObserver()}unmount(){}}function t1(t,e){if(!Array.isArray(e))return!1;let r=e.length;if(r!==t.length)return!1;for(let i=0;i<r;i++)if(e[i]!==t[i])return!1;return!0}function t2(t,e,r){let i=t.getProps();return tx(i,e,void 0!==r?r:i.custom,function(t){let e={};return t.values.forEach((t,r)=>e[r]=t.get()),e}(t),function(t){let e={};return t.values.forEach((t,r)=>e[r]=t.getVelocity()),e}(t))}var t3=r(87222);let t5=t=>1e3*t,t4=t=>t/1e3,t9={current:!1},t6=t=>Array.isArray(t)&&"number"==typeof t[0],t8=([t,e,r,i])=>`cubic-bezier(${t}, ${e}, ${r}, ${i})`,t7={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:t8([0,.65,.55,1]),circOut:t8([.55,0,1,.45]),backIn:t8([.31,.01,.66,-.59]),backOut:t8([.33,1.53,.69,.99])},et=(t,e,r)=>(((1-3*r+3*e)*t+(3*r-6*e))*t+3*e)*t;function ee(t,e,r,i){if(t===e&&r===i)return tG.Z;let n=e=>(function(t,e,r,i,n){let s,o;let a=0;do(s=et(o=e+(r-e)/2,i,n)-t)>0?r=o:e=o;while(Math.abs(s)>1e-7&&++a<12);return o})(e,0,1,t,r);return t=>0===t||1===t?t:et(n(t),e,i)}let er=ee(.42,0,1,1),ei=ee(0,0,.58,1),en=ee(.42,0,.58,1),es=t=>Array.isArray(t)&&"number"!=typeof t[0],eo=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,ea=t=>e=>1-t(1-e),el=t=>1-Math.sin(Math.acos(t)),eu=ea(el),eh=eo(el),ec=ee(.33,1.53,.69,.99),ed=ea(ec),ep=eo(ed),em={linear:tG.Z,easeIn:er,easeInOut:en,easeOut:ei,circIn:el,circInOut:eh,circOut:eu,backIn:ed,backInOut:ep,backOut:ec,anticipate:t=>(t*=2)<1?.5*ed(t):.5*(2-Math.pow(2,-10*(t-1)))},ef=t=>{if(Array.isArray(t)){(0,t3.k)(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,r,i,n]=t;return ee(e,r,i,n)}return"string"==typeof t?((0,t3.k)(void 0!==em[t],`Invalid easing type '${t}'`),em[t]):t},eg=(t,e)=>r=>!!(G(r)&&H.test(r)&&r.startsWith(t)||e&&Object.prototype.hasOwnProperty.call(r,e)),ey=(t,e,r)=>i=>{if(!G(i))return i;let[n,s,o,a]=i.match(N);return{[t]:parseFloat(n),[e]:parseFloat(s),[r]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},ev=t=>O(0,255,t),ex={...I,transform:t=>Math.round(ev(t))},eb={test:eg("rgb","red"),parse:ey("red","green","blue"),transform:({red:t,green:e,blue:r,alpha:i=1})=>"rgba("+ex.transform(t)+", "+ex.transform(e)+", "+ex.transform(r)+", "+$(W.transform(i))+")"},ew={test:eg("#"),parse:function(t){let e="",r="",i="",n="";return t.length>5?(e=t.substring(1,3),r=t.substring(3,5),i=t.substring(5,7),n=t.substring(7,9)):(e=t.substring(1,2),r=t.substring(2,3),i=t.substring(3,4),n=t.substring(4,5),e+=e,r+=r,i+=i,n+=n),{red:parseInt(e,16),green:parseInt(r,16),blue:parseInt(i,16),alpha:n?parseInt(n,16)/255:1}},transform:eb.transform},ek={test:eg("hsl","hue"),parse:ey("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:r,alpha:i=1})=>"hsla("+Math.round(t)+", "+q.transform($(e))+", "+q.transform($(r))+", "+$(W.transform(i))+")"},eP={test:t=>eb.test(t)||ew.test(t)||ek.test(t),parse:t=>eb.test(t)?eb.parse(t):ek.test(t)?ek.parse(t):ew.parse(t),transform:t=>G(t)?t:t.hasOwnProperty("red")?eb.transform(t):ek.transform(t)},eA=(t,e,r)=>-r*t+r*e+t;function eT(t,e,r){return(r<0&&(r+=1),r>1&&(r-=1),r<1/6)?t+(e-t)*6*r:r<.5?e:r<2/3?t+(e-t)*(2/3-r)*6:t}let eS=(t,e,r)=>{let i=t*t;return Math.sqrt(Math.max(0,r*(e*e-i)+i))},eE=[ew,eb,ek],eV=t=>eE.find(e=>e.test(t));function eM(t){let e=eV(t);(0,t3.k)(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`);let r=e.parse(t);return e===ek&&(r=function({hue:t,saturation:e,lightness:r,alpha:i}){t/=360,r/=100;let n=0,s=0,o=0;if(e/=100){let i=r<.5?r*(1+e):r+e-r*e,a=2*r-i;n=eT(a,i,t+1/3),s=eT(a,i,t),o=eT(a,i,t-1/3)}else n=s=o=r;return{red:Math.round(255*n),green:Math.round(255*s),blue:Math.round(255*o),alpha:i}}(r)),r}let eC=(t,e)=>{let r=eM(t),i=eM(e),n={...r};return t=>(n.red=eS(r.red,i.red,t),n.green=eS(r.green,i.green,t),n.blue=eS(r.blue,i.blue,t),n.alpha=eA(r.alpha,i.alpha,t),eb.transform(n))},eD={regex:/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,countKey:"Vars",token:"${v}",parse:tG.Z},eR={regex:Z,countKey:"Colors",token:"${c}",parse:eP.parse},ej={regex:N,countKey:"Numbers",token:"${n}",parse:I.parse};function eL(t,{regex:e,countKey:r,token:i,parse:n}){let s=t.tokenised.match(e);s&&(t["num"+r]=s.length,t.tokenised=t.tokenised.replace(e,i),t.values.push(...s.map(n)))}function eF(t){let e=t.toString(),r={value:e,tokenised:e,values:[],numVars:0,numColors:0,numNumbers:0};return r.value.includes("var(--")&&eL(r,eD),eL(r,eR),eL(r,ej),r}function eB(t){return eF(t).values}function ez(t){let{values:e,numColors:r,numVars:i,tokenised:n}=eF(t),s=e.length;return t=>{let e=n;for(let n=0;n<s;n++)e=n<i?e.replace(eD.token,t[n]):n<i+r?e.replace(eR.token,eP.transform(t[n])):e.replace(ej.token,$(t[n]));return e}}let eO=t=>"number"==typeof t?0:t,eI={test:function(t){var e,r;return isNaN(t)&&G(t)&&((null===(e=t.match(N))||void 0===e?void 0:e.length)||0)+((null===(r=t.match(Z))||void 0===r?void 0:r.length)||0)>0},parse:eB,createTransformer:ez,getAnimatableNone:function(t){let e=eB(t);return ez(t)(e.map(eO))}},eW=(t,e)=>r=>`${r>0?e:t}`;function eU(t,e){return"number"==typeof t?r=>eA(t,e,r):eP.test(t)?eC(t,e):t.startsWith("var(")?eW(t,e):eZ(t,e)}let e$=(t,e)=>{let r=[...t],i=r.length,n=t.map((t,r)=>eU(t,e[r]));return t=>{for(let e=0;e<i;e++)r[e]=n[e](t);return r}},eN=(t,e)=>{let r={...t,...e},i={};for(let n in r)void 0!==t[n]&&void 0!==e[n]&&(i[n]=eU(t[n],e[n]));return t=>{for(let e in i)r[e]=i[e](t);return r}},eZ=(t,e)=>{let r=eI.createTransformer(e),i=eF(t),n=eF(e);return i.numVars===n.numVars&&i.numColors===n.numColors&&i.numNumbers>=n.numNumbers?tF(e$(i.values,n.values),r):((0,t3.K)(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),eW(t,e))},eH=(t,e,r)=>{let i=e-t;return 0===i?1:(r-t)/i},eG=(t,e)=>r=>eA(t,e,r);function eY(t,e,{clamp:r=!0,ease:i,mixer:n}={}){let s=t.length;if((0,t3.k)(s===e.length,"Both input and output ranges must be the same length"),1===s)return()=>e[0];t[0]>t[s-1]&&(t=[...t].reverse(),e=[...e].reverse());let o=function(t,e,r){let i=[],n=r||function(t){if("number"==typeof t);else if("string"==typeof t)return eP.test(t)?eC:eZ;else if(Array.isArray(t))return e$;else if("object"==typeof t)return eN;return eG}(t[0]),s=t.length-1;for(let r=0;r<s;r++){let s=n(t[r],t[r+1]);e&&(s=tF(Array.isArray(e)?e[r]||tG.Z:e,s)),i.push(s)}return i}(e,i,n),a=o.length,l=e=>{let r=0;if(a>1)for(;r<t.length-2&&!(e<t[r+1]);r++);let i=eH(t[r],t[r+1],e);return o[r](i)};return r?e=>l(O(t[0],t[s-1],e)):l}function eX({duration:t=300,keyframes:e,times:r,ease:i="easeInOut"}){let n=es(i)?i.map(ef):ef(i),s={done:!1,value:e[0]},o=eY((r&&r.length===e.length?r:function(t){let e=[0];return function(t,e){let r=t[t.length-1];for(let i=1;i<=e;i++){let n=eH(0,e,i);t.push(eA(r,1,n))}}(e,t.length-1),e}(e)).map(e=>e*t),e,{ease:Array.isArray(n)?n:e.map(()=>n||en).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(s.value=o(e),s.done=e>=t,s)}}function eq(t,e,r){var i,n;let s=Math.max(e-5,0);return i=r-t(s),(n=e-s)?1e3/n*i:0}function eK(t,e){return t*Math.sqrt(1-e*e)}let e_=["duration","bounce"],eJ=["stiffness","damping","mass"];function eQ(t,e){return e.some(e=>void 0!==t[e])}function e0({keyframes:t,restDelta:e,restSpeed:r,...i}){let n;let s=t[0],o=t[t.length-1],a={done:!1,value:s},{stiffness:l,damping:u,mass:h,duration:c,velocity:d,isResolvedFromDuration:p}=function(t){let e={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...t};if(!eQ(t,eJ)&&eQ(t,e_)){let r=function({duration:t=800,bounce:e=.25,velocity:r=0,mass:i=1}){let n,s;(0,t3.K)(t<=t5(10),"Spring duration must be 10 seconds or less");let o=1-e;o=O(.05,1,o),t=O(.01,10,t4(t)),o<1?(n=e=>{let i=e*o,n=i*t;return .001-(i-r)/eK(e,o)*Math.exp(-n)},s=e=>{let i=e*o*t,s=Math.pow(o,2)*Math.pow(e,2)*t,a=eK(Math.pow(e,2),o);return(i*r+r-s)*Math.exp(-i)*(-n(e)+.001>0?-1:1)/a}):(n=e=>-.001+Math.exp(-e*t)*((e-r)*t+1),s=e=>t*t*(r-e)*Math.exp(-e*t));let a=function(t,e,r){let i=r;for(let r=1;r<12;r++)i-=t(i)/e(i);return i}(n,s,5/t);if(t=t5(t),isNaN(a))return{stiffness:100,damping:10,duration:t};{let e=Math.pow(a,2)*i;return{stiffness:e,damping:2*o*Math.sqrt(i*e),duration:t}}}(t);(e={...e,...r,mass:1}).isResolvedFromDuration=!0}return e}({...i,velocity:-t4(i.velocity||0)}),m=d||0,f=u/(2*Math.sqrt(l*h)),g=o-s,y=t4(Math.sqrt(l/h)),v=5>Math.abs(g);if(r||(r=v?.01:2),e||(e=v?.005:.5),f<1){let t=eK(y,f);n=e=>o-Math.exp(-f*y*e)*((m+f*y*g)/t*Math.sin(t*e)+g*Math.cos(t*e))}else if(1===f)n=t=>o-Math.exp(-y*t)*(g+(m+y*g)*t);else{let t=y*Math.sqrt(f*f-1);n=e=>{let r=Math.exp(-f*y*e),i=Math.min(t*e,300);return o-r*((m+f*y*g)*Math.sinh(i)+t*g*Math.cosh(i))/t}}return{calculatedDuration:p&&c||null,next:t=>{let i=n(t);if(p)a.done=t>=c;else{let s=m;0!==t&&(s=f<1?eq(n,t,i):0);let l=Math.abs(s)<=r,u=Math.abs(o-i)<=e;a.done=l&&u}return a.value=a.done?o:i,a}}}function e1({keyframes:t,velocity:e=0,power:r=.8,timeConstant:i=325,bounceDamping:n=10,bounceStiffness:s=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:h}){let c,d;let p=t[0],m={done:!1,value:p},f=t=>void 0!==a&&t<a||void 0!==l&&t>l,g=t=>void 0===a?l:void 0===l?a:Math.abs(a-t)<Math.abs(l-t)?a:l,y=r*e,v=p+y,x=void 0===o?v:o(v);x!==v&&(y=x-p);let b=t=>-y*Math.exp(-t/i),w=t=>x+b(t),k=t=>{let e=b(t),r=w(t);m.done=Math.abs(e)<=u,m.value=m.done?x:r},P=t=>{f(m.value)&&(c=t,d=e0({keyframes:[m.value,g(m.value)],velocity:eq(w,t,m.value),damping:n,stiffness:s,restDelta:u,restSpeed:h}))};return P(0),{calculatedDuration:null,next:t=>{let e=!1;return(d||void 0!==c||(e=!0,k(t),P(t)),void 0!==c&&t>c)?d.next(t-c):(e||k(t),m)}}}let e2=t=>{let e=({timestamp:e})=>t(e);return{start:()=>tS.Wi.update(e,!0),stop:()=>(0,tS.Pn)(e),now:()=>tS.frameData.isProcessing?tS.frameData.timestamp:performance.now()}};function e3(t){let e=0,r=t.next(e);for(;!r.done&&e<2e4;)e+=50,r=t.next(e);return e>=2e4?1/0:e}let e5={decay:e1,inertia:e1,tween:eX,keyframes:eX,spring:e0};function e4({autoplay:t=!0,delay:e=0,driver:r=e2,keyframes:i,type:n="keyframes",repeat:s=0,repeatDelay:o=0,repeatType:a="loop",onPlay:l,onStop:u,onComplete:h,onUpdate:c,...d}){let p,m,f,g,y,v=1,x=!1,b=()=>{m=new Promise(t=>{p=t})};b();let w=e5[n]||eX;w!==eX&&"number"!=typeof i[0]&&(g=eY([0,100],i,{clamp:!1}),i=[0,100]);let k=w({...d,keyframes:i});"mirror"===a&&(y=w({...d,keyframes:[...i].reverse(),velocity:-(d.velocity||0)}));let P="idle",A=null,T=null,S=null;null===k.calculatedDuration&&s&&(k.calculatedDuration=e3(k));let{calculatedDuration:E}=k,V=1/0,M=1/0;null!==E&&(M=(V=E+o)*(s+1)-o);let C=0,D=t=>{if(null===T)return;v>0&&(T=Math.min(T,t)),v<0&&(T=Math.min(t-M/v,T));let r=(C=null!==A?A:Math.round(t-T)*v)-e*(v>=0?1:-1),n=v>=0?r<0:r>M;C=Math.max(r,0),"finished"===P&&null===A&&(C=M);let l=C,u=k;if(s){let t=Math.min(C,M)/V,e=Math.floor(t),r=t%1;!r&&t>=1&&(r=1),1===r&&e--,(e=Math.min(e,s+1))%2&&("reverse"===a?(r=1-r,o&&(r-=o/V)):"mirror"===a&&(u=y)),l=O(0,1,r)*V}let h=n?{done:!1,value:i[0]}:u.next(l);g&&(h.value=g(h.value));let{done:d}=h;n||null===E||(d=v>=0?C>=M:C<=0);let p=null===A&&("finished"===P||"running"===P&&d);return c&&c(h.value),p&&L(),h},R=()=>{f&&f.stop(),f=void 0},j=()=>{P="idle",R(),p(),b(),T=S=null},L=()=>{P="finished",h&&h(),R(),p()},F=()=>{if(x)return;f||(f=r(D));let t=f.now();l&&l(),null!==A?T=t-A:T&&"finished"!==P||(T=t),"finished"===P&&b(),S=T,A=null,P="running",f.start()};t&&F();let B={then:(t,e)=>m.then(t,e),get time(){return t4(C)},set time(newTime){C=newTime=t5(newTime),null===A&&f&&0!==v?T=f.now()-newTime/v:A=newTime},get duration(){return t4(null===k.calculatedDuration?e3(k):k.calculatedDuration)},get speed(){return v},set speed(newSpeed){if(newSpeed===v||!f)return;v=newSpeed,B.time=t4(C)},get state(){return P},play:F,pause:()=>{P="paused",A=C},stop:()=>{x=!0,"idle"!==P&&(P="idle",u&&u(),j())},cancel:()=>{null!==S&&D(S),j()},complete:()=>{P="finished"},sample:t=>(T=0,D(t))};return B}let e9=function(t){let e;return()=>(void 0===e&&(e=t()),e)}(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),e6=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),e8=(t,e)=>"spring"===e.type||"backgroundColor"===t||!function t(e){return!!(!e||"string"==typeof e&&t7[e]||t6(e)||Array.isArray(e)&&e.every(t))}(e.ease),e7={type:"spring",stiffness:500,damping:25,restSpeed:10},rt=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),re={type:"keyframes",duration:.8},rr={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},ri=(t,{keyframes:e})=>e.length>2?re:M.has(t)?t.startsWith("scale")?rt(e[1]):e7:rr,rn=(t,e)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(eI.test(e)||"0"===e)&&!e.startsWith("url(")),rs=new Set(["brightness","contrast","saturate","opacity"]);function ro(t){let[e,r]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[i]=r.match(N)||[];if(!i)return t;let n=r.replace(i,""),s=rs.has(e)?1:0;return i!==r&&(s*=100),e+"("+s+n+")"}let ra=/([a-z-]*)\(.*?\)/g,rl={...eI,getAnimatableNone:t=>{let e=t.match(ra);return e?e.map(ro).join(" "):t}},ru={...te,color:eP,backgroundColor:eP,outlineColor:eP,fill:eP,stroke:eP,borderColor:eP,borderTopColor:eP,borderRightColor:eP,borderBottomColor:eP,borderLeftColor:eP,filter:rl,WebkitFilter:rl},rh=t=>ru[t];function rc(t,e){let r=rh(t);return r!==rl&&(r=eI),r.getAnimatableNone?r.getAnimatableNone(e):void 0}let rd=t=>/^0[^.\s]+$/.test(t);function rp(t,e){return t[e]||t.default||t}let rm={skipAnimations:!1},rf=(t,e,r,i={})=>n=>{let s=rp(i,t)||{},o=s.delay||i.delay||0,{elapsed:a=0}=i;a-=t5(o);let l=function(t,e,r,i){let n,s;let o=rn(e,r);n=Array.isArray(r)?[...r]:[null,r];let a=void 0!==i.from?i.from:t.get(),l=[];for(let t=0;t<n.length;t++){var u;null===n[t]&&(n[t]=0===t?a:n[t-1]),("number"==typeof(u=n[t])?0===u:null!==u?"none"===u||"0"===u||rd(u):void 0)&&l.push(t),"string"==typeof n[t]&&"none"!==n[t]&&"0"!==n[t]&&(s=n[t])}if(o&&l.length&&s)for(let t=0;t<l.length;t++)n[l[t]]=rc(e,s);return n}(e,t,r,s),u=l[0],h=l[l.length-1],c=rn(t,u),d=rn(t,h);(0,t3.K)(c===d,`You are trying to animate ${t} from "${u}" to "${h}". ${u} is not an animatable value - to enable this animation set ${u} to a value animatable to ${h} via the \`style\` property.`);let p={keyframes:l,velocity:e.getVelocity(),ease:"easeOut",...s,delay:-a,onUpdate:t=>{e.set(t),s.onUpdate&&s.onUpdate(t)},onComplete:()=>{n(),s.onComplete&&s.onComplete()}};if(!function({when:t,delay:e,delayChildren:r,staggerChildren:i,staggerDirection:n,repeat:s,repeatType:o,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(s)&&(p={...p,...ri(t,p)}),p.duration&&(p.duration=t5(p.duration)),p.repeatDelay&&(p.repeatDelay=t5(p.repeatDelay)),!c||!d||t9.current||!1===s.type||rm.skipAnimations)return function({keyframes:t,delay:e,onUpdate:r,onComplete:i}){let n=()=>(r&&r(t[t.length-1]),i&&i(),{time:0,speed:1,duration:0,play:tG.Z,pause:tG.Z,stop:tG.Z,then:t=>(t(),Promise.resolve()),cancel:tG.Z,complete:tG.Z});return e?e4({keyframes:[0,1],duration:0,delay:e,onComplete:n}):n()}(t9.current?{...p,delay:0}:p);if(!i.isHandoff&&e.owner&&e.owner.current instanceof HTMLElement&&!e.owner.getProps().onUpdate){let r=function(t,e,{onUpdate:r,onComplete:i,...n}){let s,o;if(!(e9()&&e6.has(e)&&!n.repeatDelay&&"mirror"!==n.repeatType&&0!==n.damping&&"inertia"!==n.type))return!1;let a=!1,l=!1,u=()=>{o=new Promise(t=>{s=t})};u();let{keyframes:h,duration:c=300,ease:d,times:p}=n;if(e8(e,n)){let t=e4({...n,repeat:0,delay:0}),e={done:!1,value:h[0]},r=[],i=0;for(;!e.done&&i<2e4;)e=t.sample(i),r.push(e.value),i+=10;p=void 0,h=r,c=i-10,d="linear"}let m=function(t,e,r,{delay:i=0,duration:n,repeat:s=0,repeatType:o="loop",ease:a,times:l}={}){let u={[e]:r};l&&(u.offset=l);let h=function t(e){if(e)return t6(e)?t8(e):Array.isArray(e)?e.map(t):t7[e]}(a);return Array.isArray(h)&&(u.easing=h),t.animate(u,{delay:i,duration:n,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:s+1,direction:"reverse"===o?"alternate":"normal"})}(t.owner.current,e,h,{...n,duration:c,ease:d,times:p}),f=()=>{l=!1,m.cancel()},g=()=>{l=!0,tS.Wi.update(f),s(),u()};return m.onfinish=()=>{l||(t.set(function(t,{repeat:e,repeatType:r="loop"}){let i=e&&"loop"!==r&&e%2==1?0:t.length-1;return t[i]}(h,n)),i&&i(),g())},{then:(t,e)=>o.then(t,e),attachTimeline:t=>(m.timeline=t,m.onfinish=null,tG.Z),get time(){return t4(m.currentTime||0)},set time(newTime){m.currentTime=t5(newTime)},get speed(){return m.playbackRate},set speed(newSpeed){m.playbackRate=newSpeed},get duration(){return t4(c)},play:()=>{a||(m.play(),(0,tS.Pn)(f))},pause:()=>m.pause(),stop:()=>{if(a=!0,"idle"===m.playState)return;let{currentTime:e}=m;if(e){let r=e4({...n,autoplay:!1});t.setWithVelocity(r.sample(e-10).value,r.sample(e).value,10)}g()},complete:()=>{l||m.finish()},cancel:g}}(e,t,p);if(r)return r}return e4(p)};function rg(t){return!!(D(t)&&t.add)}let ry=t=>/^\-?\d*\.?\d+$/.test(t);function rv(t,e){-1===t.indexOf(e)&&t.push(e)}function rx(t,e){let r=t.indexOf(e);r>-1&&t.splice(r,1)}class rb{constructor(){this.subscriptions=[]}add(t){return rv(this.subscriptions,t),()=>rx(this.subscriptions,t)}notify(t,e,r){let i=this.subscriptions.length;if(i){if(1===i)this.subscriptions[0](t,e,r);else for(let n=0;n<i;n++){let i=this.subscriptions[n];i&&i(t,e,r)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let rw=t=>!isNaN(parseFloat(t)),rk={current:void 0};class rP{constructor(t,e={}){this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(t,e=!0)=>{this.prev=this.current,this.current=t;let{delta:r,timestamp:i}=tS.frameData;this.lastUpdated!==i&&(this.timeDelta=r,this.lastUpdated=i,tS.Wi.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),e&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>tS.Wi.postRender(this.velocityCheck),this.velocityCheck=({timestamp:t})=>{t!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=t,this.canTrackVelocity=rw(this.current),this.owner=e.owner}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new rb);let r=this.events[t].add(e);return"change"===t?()=>{r(),tS.Wi.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,r){this.set(e),this.prev=t,this.timeDelta=r}jump(t){this.updateAndNotify(t),this.prev=t,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return rk.current&&rk.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t,e;return this.canTrackVelocity?(t=parseFloat(this.current)-parseFloat(this.prev),(e=this.timeDelta)?1e3/e*t:0):0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function rA(t,e){return new rP(t,e)}let rT=t=>e=>e.test(t),rS=[I,K,q,X,J,_,{test:t=>"auto"===t,parse:t=>t}],rE=t=>rS.find(rT(t)),rV=[...rS,eP,eI],rM=t=>rV.find(rT(t));function rC(t,e,{delay:r=0,transitionOverride:i,type:n}={}){let{transition:s=t.getDefaultTransition(),transitionEnd:o,...a}=t.makeTargetAnimatable(e),l=t.getValue("willChange");i&&(s=i);let u=[],c=n&&t.animationState&&t.animationState.getState()[n];for(let e in a){let i=t.getValue(e),n=a[e];if(!i||void 0===n||c&&function({protectedKeys:t,needsAnimating:e},r){let i=t.hasOwnProperty(r)&&!0!==e[r];return e[r]=!1,i}(c,e))continue;let o={delay:r,elapsed:0,...rp(s||{},e)};if(window.HandoffAppearAnimations){let r=t.getProps()[h];if(r){let t=window.HandoffAppearAnimations(r,e,i,tS.Wi);null!==t&&(o.elapsed=t,o.isHandoff=!0)}}let d=!o.isHandoff&&!function(t,e){let r=t.get();if(!Array.isArray(e))return r!==e;for(let t=0;t<e.length;t++)if(e[t]!==r)return!0}(i,n);if("spring"===o.type&&(i.getVelocity()||o.velocity)&&(d=!1),i.animation&&(d=!1),d)continue;i.start(rf(e,i,n,t.shouldReduceMotion&&M.has(e)?{type:!1}:o));let p=i.animation;rg(l)&&(l.add(e),p.then(()=>l.remove(e))),u.push(p)}return o&&Promise.all(u).then(()=>{o&&function(t,e){let r=t2(t,e),{transitionEnd:i={},transition:n={},...s}=r?t.makeTargetAnimatable(r,!1):{};for(let e in s={...s,...i}){let r=tP(s[e]);t.hasValue(e)?t.getValue(e).set(r):t.addValue(e,rA(r))}}(t,o)}),u}function rD(t,e,r={}){let i=t2(t,e,r.custom),{transition:n=t.getDefaultTransition()||{}}=i||{};r.transitionOverride&&(n=r.transitionOverride);let s=i?()=>Promise.all(rC(t,i,r)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(i=0)=>{let{delayChildren:s=0,staggerChildren:o,staggerDirection:a}=n;return function(t,e,r=0,i=0,n=1,s){let o=[],a=(t.variantChildren.size-1)*i,l=1===n?(t=0)=>t*i:(t=0)=>a-t*i;return Array.from(t.variantChildren).sort(rR).forEach((t,i)=>{t.notify("AnimationStart",e),o.push(rD(t,e,{...s,delay:r+l(i)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(o)}(t,e,s+i,o,a,r)}:()=>Promise.resolve(),{when:a}=n;if(!a)return Promise.all([s(),o(r.delay)]);{let[t,e]="beforeChildren"===a?[s,o]:[o,s];return t().then(()=>e())}}function rR(t,e){return t.sortNodePosition(e)}let rj=[...m].reverse(),rL=m.length;function rF(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}class rB extends tU{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:r})=>(function(t,e,r={}){let i;if(t.notify("AnimationStart",e),Array.isArray(e))i=Promise.all(e.map(e=>rD(t,e,r)));else if("string"==typeof e)i=rD(t,e,r);else{let n="function"==typeof e?t2(t,e,r.custom):e;i=Promise.all(rC(t,n,r))}return i.then(()=>t.notify("AnimationComplete",e))})(t,e,r))),r={animate:rF(!0),whileInView:rF(),whileHover:rF(),whileTap:rF(),whileDrag:rF(),whileFocus:rF(),exit:rF()},i=!0,n=(e,r)=>{let i=t2(t,r);if(i){let{transition:t,transitionEnd:r,...n}=i;e={...e,...n,...r}}return e};function s(s,o){let a=t.getProps(),l=t.getVariantContext(!0)||{},u=[],h=new Set,c={},m=1/0;for(let e=0;e<rL;e++){var f;let g=rj[e],y=r[g],v=void 0!==a[g]?a[g]:l[g],x=d(v),b=g===o?y.isActive:null;!1===b&&(m=e);let w=v===l[g]&&v!==a[g]&&x;if(w&&i&&t.manuallyAnimateOnMount&&(w=!1),y.protectedKeys={...c},!y.isActive&&null===b||!v&&!y.prevProp||p(v)||"boolean"==typeof v)continue;let k=(f=y.prevProp,("string"==typeof v?v!==f:!!Array.isArray(v)&&!t1(v,f))||g===o&&y.isActive&&!w&&x||e>m&&x),P=!1,A=Array.isArray(v)?v:[v],T=A.reduce(n,{});!1===b&&(T={});let{prevResolvedValues:S={}}=y,E={...S,...T},V=t=>{k=!0,h.has(t)&&(P=!0,h.delete(t)),y.needsAnimating[t]=!0};for(let t in E){let e=T[t],r=S[t];if(!c.hasOwnProperty(t))(tw(e)&&tw(r)?t1(e,r):e===r)?void 0!==e&&h.has(t)?V(t):y.protectedKeys[t]=!0:void 0!==e?V(t):h.add(t)}y.prevProp=v,y.prevResolvedValues=T,y.isActive&&(c={...c,...T}),i&&t.blockInitialAnimation&&(k=!1),k&&(!w||P)&&u.push(...A.map(t=>({animation:t,options:{type:g,...s}})))}if(h.size){let e={};h.forEach(r=>{let i=t.getBaseTarget(r);void 0!==i&&(e[r]=i)}),u.push({animation:e})}let g=!!u.length;return i&&(!1===a.initial||a.initial===a.animate)&&!t.manuallyAnimateOnMount&&(g=!1),i=!1,g?e(u):Promise.resolve()}return{animateChanges:s,setActive:function(e,i,n){var o;if(r[e].isActive===i)return Promise.resolve();null===(o=t.variantChildren)||void 0===o||o.forEach(t=>{var r;return null===(r=t.animationState)||void 0===r?void 0:r.setActive(e,i)}),r[e].isActive=i;let a=s(n,e);for(let t in r)r[t].protectedKeys={};return a},setAnimateFunction:function(r){e=r(t)},getState:()=>r}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();this.unmount(),p(t)&&(this.unmount=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){}}let rz=0;class rO extends tU{constructor(){super(...arguments),this.id=rz++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e,custom:r}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let n=this.node.animationState.setActive("exit",!t,{custom:null!=r?r:this.node.getProps().custom});e&&!t&&n.then(()=>e(this.id))}mount(){let{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}let rI=(t,e)=>Math.abs(t-e);class rW{constructor(t,e,{transformPagePoint:r,contextWindow:i,dragSnapToOrigin:n=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=rN(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,r=function(t,e){return Math.sqrt(rI(t.x,e.x)**2+rI(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!r)return;let{point:i}=t,{timestamp:n}=tS.frameData;this.history.push({...i,timestamp:n});let{onStart:s,onMove:o}=this.handlers;e||(s&&s(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),o&&o(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=rU(e,this.transformPagePoint),tS.Wi.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:r,onSessionEnd:i,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=rN("pointercancel"===t.type?this.lastMoveEventInfo:rU(e,this.transformPagePoint),this.history);this.startEvent&&r&&r(t,s),i&&i(t,s)},!tC(t))return;this.dragSnapToOrigin=n,this.handlers=e,this.transformPagePoint=r,this.contextWindow=i||window;let s=rU(tD(t),this.transformPagePoint),{point:o}=s,{timestamp:a}=tS.frameData;this.history=[{...o,timestamp:a}];let{onSessionStart:l}=e;l&&l(t,rN(s,this.history)),this.removeListeners=tF(tj(this.contextWindow,"pointermove",this.handlePointerMove),tj(this.contextWindow,"pointerup",this.handlePointerUp),tj(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),(0,tS.Pn)(this.updatePoint)}}function rU(t,e){return e?{point:e(t.point)}:t}function r$(t,e){return{x:t.x-e.x,y:t.y-e.y}}function rN({point:t},e){return{point:t,delta:r$(t,rZ(e)),offset:r$(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let r=t.length-1,i=null,n=rZ(t);for(;r>=0&&(i=t[r],!(n.timestamp-i.timestamp>t5(.1)));)r--;if(!i)return{x:0,y:0};let s=t4(n.timestamp-i.timestamp);if(0===s)return{x:0,y:0};let o={x:(n.x-i.x)/s,y:(n.y-i.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,0)}}function rZ(t){return t[t.length-1]}function rH(t){return t.max-t.min}function rG(t,e=0,r=.01){return Math.abs(t-e)<=r}function rY(t,e,r,i=.5){t.origin=i,t.originPoint=eA(e.min,e.max,t.origin),t.scale=rH(r)/rH(e),(rG(t.scale,1,1e-4)||isNaN(t.scale))&&(t.scale=1),t.translate=eA(r.min,r.max,t.origin)-t.originPoint,(rG(t.translate)||isNaN(t.translate))&&(t.translate=0)}function rX(t,e,r,i){rY(t.x,e.x,r.x,i?i.originX:void 0),rY(t.y,e.y,r.y,i?i.originY:void 0)}function rq(t,e,r){t.min=r.min+e.min,t.max=t.min+rH(e)}function rK(t,e,r){t.min=e.min-r.min,t.max=t.min+rH(e)}function r_(t,e,r){rK(t.x,e.x,r.x),rK(t.y,e.y,r.y)}function rJ(t,e,r){return{min:void 0!==e?t.min+e:void 0,max:void 0!==r?t.max+r-(t.max-t.min):void 0}}function rQ(t,e){let r=e.min-t.min,i=e.max-t.max;return e.max-e.min<t.max-t.min&&([r,i]=[i,r]),{min:r,max:i}}function r0(t,e,r){return{min:r1(t,e),max:r1(t,r)}}function r1(t,e){return"number"==typeof t?t:t[e]||0}let r2=()=>({translate:0,scale:1,origin:0,originPoint:0}),r3=()=>({x:r2(),y:r2()}),r5=()=>({min:0,max:0}),r4=()=>({x:r5(),y:r5()});function r9(t){return[t("x"),t("y")]}function r6({top:t,left:e,right:r,bottom:i}){return{x:{min:e,max:r},y:{min:t,max:i}}}function r8(t){return void 0===t||1===t}function r7({scale:t,scaleX:e,scaleY:r}){return!r8(t)||!r8(e)||!r8(r)}function it(t){return r7(t)||ie(t)||t.z||t.rotate||t.rotateX||t.rotateY}function ie(t){var e,r;return(e=t.x)&&"0%"!==e||(r=t.y)&&"0%"!==r}function ir(t,e,r,i,n){return void 0!==n&&(t=i+n*(t-i)),i+r*(t-i)+e}function ii(t,e=0,r=1,i,n){t.min=ir(t.min,e,r,i,n),t.max=ir(t.max,e,r,i,n)}function is(t,{x:e,y:r}){ii(t.x,e.translate,e.scale,e.originPoint),ii(t.y,r.translate,r.scale,r.originPoint)}function io(t){return Number.isInteger(t)?t:t>1.0000000000001||t<.999999999999?t:1}function ia(t,e){t.min=t.min+e,t.max=t.max+e}function il(t,e,[r,i,n]){let s=void 0!==e[n]?e[n]:.5,o=eA(t.min,t.max,s);ii(t,e[r],e[i],o,e.scale)}let iu=["x","scaleX","originX"],ih=["y","scaleY","originY"];function ic(t,e){il(t.x,e,iu),il(t.y,e,ih)}function id(t,e){return r6(function(t,e){if(!e)return t;let r=e({x:t.left,y:t.top}),i=e({x:t.right,y:t.bottom});return{top:r.y,left:r.x,bottom:i.y,right:i.x}}(t.getBoundingClientRect(),e))}let ip=({current:t})=>t?t.ownerDocument.defaultView:null,im=new WeakMap;class ig{constructor(t){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=r4(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:r}=this.visualElement;if(r&&!1===r.isPresent)return;let{dragSnapToOrigin:i}=this.getProps();this.panSession=new rW(t,{onSessionStart:t=>{let{dragSnapToOrigin:r}=this.getProps();r?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(tD(t,"page").point)},onStart:(t,e)=>{let{drag:r,dragPropagation:i,onDragStart:n}=this.getProps();if(r&&!i&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=tI(r),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),r9(t=>{let e=this.getAxisMotionValue(t).get()||0;if(q.test(e)){let{projection:r}=this.visualElement;if(r&&r.layout){let i=r.layout.layoutBox[t];if(i){let t=rH(i);e=parseFloat(e)/100*t}}}this.originPoint[t]=e}),n&&tS.Wi.update(()=>n(t,e),!1,!0);let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:r,dragDirectionLock:i,onDirectionLock:n,onDrag:s}=this.getProps();if(!r&&!this.openGlobalLock)return;let{offset:o}=e;if(i&&null===this.currentDirection){this.currentDirection=function(t,e=10){let r=null;return Math.abs(t.y)>e?r="y":Math.abs(t.x)>e&&(r="x"),r}(o),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),s&&s(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>r9(t=>{var e;return"paused"===this.getAnimationState(t)&&(null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,contextWindow:ip(this.visualElement)})}stop(t,e){let r=this.isDragging;if(this.cancel(),!r)return;let{velocity:i}=e;this.startAnimation(i);let{onDragEnd:n}=this.getProps();n&&tS.Wi.update(()=>n(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:r}=this.getProps();!r&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,r){let{drag:i}=this.getProps();if(!r||!iy(t,i,this.currentDirection))return;let n=this.getAxisMotionValue(t),s=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(s=function(t,{min:e,max:r},i){return void 0!==e&&t<e?t=i?eA(e,t,i.min):Math.max(t,e):void 0!==r&&t>r&&(t=i?eA(r,t,i.max):Math.min(t,r)),t}(s,this.constraints[t],this.elastic[t])),n.set(s)}resolveConstraints(){var t;let{dragConstraints:e,dragElastic:r}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(t=this.visualElement.projection)||void 0===t?void 0:t.layout,n=this.constraints;e&&c(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&i?this.constraints=function(t,{top:e,left:r,bottom:i,right:n}){return{x:rJ(t.x,r,n),y:rJ(t.y,e,i)}}(i.layoutBox,e):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:r0(t,"left","right"),y:r0(t,"top","bottom")}}(r),n!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&r9(t=>{this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let r={};return void 0!==e.min&&(r.min=e.min-t.min),void 0!==e.max&&(r.max=e.max-t.min),r}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:r}=this.getProps();if(!e||!c(e))return!1;let i=e.current;(0,t3.k)(null!==i,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let s=function(t,e,r){let i=id(t,r),{scroll:n}=e;return n&&(ia(i.x,n.offset.x),ia(i.y,n.offset.y)),i}(i,n.root,this.visualElement.getTransformPagePoint()),o={x:rQ((t=n.layout.layoutBox).x,s.x),y:rQ(t.y,s.y)};if(r){let t=r(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=r6(t))}return o}startAnimation(t){let{drag:e,dragMomentum:r,dragElastic:i,dragTransition:n,dragSnapToOrigin:s,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(r9(o=>{if(!iy(o,e,this.currentDirection))return;let l=a&&a[o]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:r?t[o]:0,bounceStiffness:i?200:1e6,bounceDamping:i?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let r=this.getAxisMotionValue(t);return r.start(rf(t,r,0,e))}stopAnimation(){r9(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){r9(t=>{var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.pause()})}getAnimationState(t){var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.state}getAxisMotionValue(t){let e="_drag"+t.toUpperCase(),r=this.visualElement.getProps();return r[e]||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){r9(e=>{let{drag:r}=this.getProps();if(!iy(e,r,this.currentDirection))return;let{projection:i}=this.visualElement,n=this.getAxisMotionValue(e);if(i&&i.layout){let{min:r,max:s}=i.layout.layoutBox[e];n.set(t[e]-eA(r,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:r}=this.visualElement;if(!c(e)||!r||!this.constraints)return;this.stopAnimation();let i={x:0,y:0};r9(t=>{let e=this.getAxisMotionValue(t);if(e){let r=e.get();i[t]=function(t,e){let r=.5,i=rH(t),n=rH(e);return n>i?r=eH(e.min,e.max-i,t.min):i>n&&(r=eH(t.min,t.max-n,e.min)),O(0,1,r)}({min:r,max:r},this.constraints[t])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),r9(e=>{if(!iy(e,t,null))return;let r=this.getAxisMotionValue(e),{min:n,max:s}=this.constraints[e];r.set(eA(n,s,i[e]))})}addListeners(){if(!this.visualElement.current)return;im.set(this.visualElement,this);let t=tj(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:r=!0}=this.getProps();e&&r&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();c(t)&&(this.constraints=this.resolveRefConstraints())},{projection:r}=this.visualElement,i=r.addEventListener("measure",e);r&&!r.layout&&(r.root&&r.root.updateScroll(),r.updateLayout()),e();let n=tM(window,"resize",()=>this.scalePositionWithinConstraints()),s=r.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(r9(e=>{let r=this.getAxisMotionValue(e);r&&(this.originPoint[e]+=t[e].translate,r.set(r.get()+t[e].translate))}),this.visualElement.render())});return()=>{n(),t(),i(),s&&s()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:r=!1,dragPropagation:i=!1,dragConstraints:n=!1,dragElastic:s=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:r,dragPropagation:i,dragConstraints:n,dragElastic:s,dragMomentum:o}}}function iy(t,e,r){return(!0===e||e===t)&&(null===r||r===t)}class iv extends tU{constructor(t){super(t),this.removeGroupControls=tG.Z,this.removeListeners=tG.Z,this.controls=new ig(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||tG.Z}unmount(){this.removeGroupControls(),this.removeListeners()}}let ix=t=>(e,r)=>{t&&tS.Wi.update(()=>t(e,r))};class ib extends tU{constructor(){super(...arguments),this.removePointerDownListener=tG.Z}onPointerDown(t){this.session=new rW(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:ip(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:r,onPanEnd:i}=this.node.getProps();return{onSessionStart:ix(t),onStart:ix(e),onMove:r,onEnd:(t,e)=>{delete this.session,i&&tS.Wi.update(()=>i(t,e))}}}mount(){this.removePointerDownListener=tj(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let iw={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function ik(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let iP={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!K.test(t))return t;t=parseFloat(t)}let r=ik(t,e.target.x),i=ik(t,e.target.y);return`${r}% ${i}%`}};class iA extends i.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:r,layoutId:i}=this.props,{projection:n}=t;Object.assign(E,iS),n&&(e.group&&e.group.add(n),r&&r.register&&i&&r.register(n),n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),iw.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:r,drag:i,isPresent:n}=this.props,s=r.projection;return s&&(s.isPresent=n,i||t.layoutDependency!==e||void 0===e?s.willUpdate():this.safeToRemove(),t.isPresent===n||(n?s.promote():s.relegate()||tS.Wi.postRender(()=>{let t=s.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),queueMicrotask(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:r}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(i),r&&r.deregister&&r.deregister(i))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function iT(t){let[e,r]=function(){let t=(0,i.useContext)(o.O);if(null===t)return[!0,null];let{isPresent:e,onExitComplete:r,register:n}=t,s=(0,i.useId)();return(0,i.useEffect)(()=>n(s),[]),!e&&r?[!1,()=>r&&r(s)]:[!0]}(),n=(0,i.useContext)(k.p);return i.createElement(iA,{...t,layoutGroup:n,switchLayoutGroup:(0,i.useContext)(P),isPresent:e,safeToRemove:r})}let iS={borderRadius:{...iP,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:iP,borderTopRightRadius:iP,borderBottomLeftRadius:iP,borderBottomRightRadius:iP,boxShadow:{correct:(t,{treeScale:e,projectionDelta:r})=>{let i=eI.parse(t);if(i.length>5)return t;let n=eI.createTransformer(t),s="number"!=typeof i[0]?1:0,o=r.x.scale*e.x,a=r.y.scale*e.y;i[0+s]/=o,i[1+s]/=a;let l=eA(o,a,.5);return"number"==typeof i[2+s]&&(i[2+s]/=l),"number"==typeof i[3+s]&&(i[3+s]/=l),n(i)}}},iE=["TopLeft","TopRight","BottomLeft","BottomRight"],iV=iE.length,iM=t=>"string"==typeof t?parseFloat(t):t,iC=t=>"number"==typeof t||K.test(t);function iD(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let iR=iL(0,.5,eu),ij=iL(.5,.95,tG.Z);function iL(t,e,r){return i=>i<t?0:i>e?1:r(eH(t,e,i))}function iF(t,e){t.min=e.min,t.max=e.max}function iB(t,e){iF(t.x,e.x),iF(t.y,e.y)}function iz(t,e,r,i,n){return t-=e,t=i+1/r*(t-i),void 0!==n&&(t=i+1/n*(t-i)),t}function iO(t,e,[r,i,n],s,o){!function(t,e=0,r=1,i=.5,n,s=t,o=t){if(q.test(e)&&(e=parseFloat(e),e=eA(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=eA(s.min,s.max,i);t===s&&(a-=e),t.min=iz(t.min,e,r,a,n),t.max=iz(t.max,e,r,a,n)}(t,e[r],e[i],e[n],e.scale,s,o)}let iI=["x","scaleX","originX"],iW=["y","scaleY","originY"];function iU(t,e,r,i){iO(t.x,e,iI,r?r.x:void 0,i?i.x:void 0),iO(t.y,e,iW,r?r.y:void 0,i?i.y:void 0)}function i$(t){return 0===t.translate&&1===t.scale}function iN(t){return i$(t.x)&&i$(t.y)}function iZ(t,e){return Math.round(t.x.min)===Math.round(e.x.min)&&Math.round(t.x.max)===Math.round(e.x.max)&&Math.round(t.y.min)===Math.round(e.y.min)&&Math.round(t.y.max)===Math.round(e.y.max)}function iH(t){return rH(t.x)/rH(t.y)}class iG{constructor(){this.members=[]}add(t){rv(this.members,t),t.scheduleRender()}remove(t){if(rx(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e;let r=this.members.findIndex(e=>t===e);if(0===r)return!1;for(let t=r;t>=0;t--){let r=this.members[t];if(!1!==r.isPresent){e=r;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,e&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:i}=t.options;!1===i&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:r}=t;e.onExitComplete&&e.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function iY(t,e,r){let i="",n=t.x.translate/e.x,s=t.y.translate/e.y;if((n||s)&&(i=`translate3d(${n}px, ${s}px, 0) `),(1!==e.x||1!==e.y)&&(i+=`scale(${1/e.x}, ${1/e.y}) `),r){let{rotate:t,rotateX:e,rotateY:n}=r;t&&(i+=`rotate(${t}deg) `),e&&(i+=`rotateX(${e}deg) `),n&&(i+=`rotateY(${n}deg) `)}let o=t.x.scale*e.x,a=t.y.scale*e.y;return(1!==o||1!==a)&&(i+=`scale(${o}, ${a})`),i||"none"}let iX=(t,e)=>t.depth-e.depth;class iq{constructor(){this.children=[],this.isDirty=!1}add(t){rv(this.children,t),this.isDirty=!0}remove(t){rx(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(iX),this.isDirty=!1,this.children.forEach(t)}}let iK=["","X","Y","Z"],i_={visibility:"hidden"},iJ=0,iQ={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function i0({attachResizeListener:t,defaultParent:e,measureScroll:r,checkIsScrollRoot:i,resetTransform:n}){return class{constructor(t={},r=null==e?void 0:e()){this.id=iJ++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,iQ.totalNodes=iQ.resolvedTargetDeltas=iQ.recalculatedProjection=0,this.nodes.forEach(i3),this.nodes.forEach(nt),this.nodes.forEach(ne),this.nodes.forEach(i5),window.MotionDebug&&window.MotionDebug.record(iQ)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=r?r.root||r:this,this.path=r?[...r.path,r]:[],this.parent=r,this.depth=r?r.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new iq)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new rb),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let r=this.eventHandlers.get(t);r&&r.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e,r=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=e instanceof SVGElement&&"svg"!==e.tagName,this.instance=e;let{layoutId:i,layout:n,visualElement:s}=this.options;if(s&&!s.current&&s.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),r&&(n||i)&&(this.isLayoutDirty=!0),t){let r;let i=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,r&&r(),r=function(t,e){let r=performance.now(),i=({timestamp:n})=>{let s=n-r;s>=e&&((0,tS.Pn)(i),t(s-e))};return tS.Wi.read(i,!0),()=>(0,tS.Pn)(i)}(i,250),iw.hasAnimatedSinceResize&&(iw.hasAnimatedSinceResize=!1,this.nodes.forEach(i7))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&s&&(i||n)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeTargetChanged:r,layout:i})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let n=this.options.transition||s.getDefaultTransition()||na,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=s.getProps(),l=!this.targetLayout||!iZ(this.targetLayout,i)||r,u=!e&&r;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,u);let e={...rp(n,"layout"),onPlay:o,onComplete:a};(s.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||i7(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=i})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,(0,tS.Pn)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(nr),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:r}=this.options;if(void 0===e&&!r)return;let i=this.getTransformTemplate();this.prevTransformTemplateValue=i?i(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(i9);return}this.isUpdating||this.nodes.forEach(i6),this.isUpdating=!1,this.nodes.forEach(i8),this.nodes.forEach(i1),this.nodes.forEach(i2),this.clearAllSnapshots();let t=performance.now();tS.frameData.delta=O(0,1e3/60,t-tS.frameData.timestamp),tS.frameData.timestamp=t,tS.frameData.isProcessing=!0,tS.S6.update.process(tS.frameData),tS.S6.preRender.process(tS.frameData),tS.S6.render.process(tS.frameData),tS.frameData.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(i4),this.sharedNodes.forEach(ni)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,tS.Wi.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){tS.Wi.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=r4(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&(this.scroll={animationId:this.root.animationId,phase:t,isRoot:i(this.instance),offset:r(this.instance)})}resetTransform(){if(!n)return;let t=this.isLayoutDirty||this.shouldResetTransform,e=this.projectionDelta&&!iN(this.projectionDelta),r=this.getTransformTemplate(),i=r?r(this.latestValues,""):void 0,s=i!==this.prevTransformTemplateValue;t&&(e||it(this.latestValues)||s)&&(n(this.instance,i),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let r=this.measurePageBox(),i=this.removeElementScroll(r);return t&&(i=this.removeTransform(i)),nh((e=i).x),nh(e.y),{animationId:this.root.animationId,measuredBox:r,layoutBox:i,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return r4();let e=t.measureViewportBox(),{scroll:r}=this.root;return r&&(ia(e.x,r.offset.x),ia(e.y,r.offset.y)),e}removeElementScroll(t){let e=r4();iB(e,t);for(let r=0;r<this.path.length;r++){let i=this.path[r],{scroll:n,options:s}=i;if(i!==this.root&&n&&s.layoutScroll){if(n.isRoot){iB(e,t);let{scroll:r}=this.root;r&&(ia(e.x,-r.offset.x),ia(e.y,-r.offset.y))}ia(e.x,n.offset.x),ia(e.y,n.offset.y)}}return e}applyTransform(t,e=!1){let r=r4();iB(r,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];!e&&i.options.layoutScroll&&i.scroll&&i!==i.root&&ic(r,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),it(i.latestValues)&&ic(r,i.latestValues)}return it(this.latestValues)&&ic(r,this.latestValues),r}removeTransform(t){let e=r4();iB(e,t);for(let t=0;t<this.path.length;t++){let r=this.path[t];if(!r.instance||!it(r.latestValues))continue;r7(r.latestValues)&&r.updateSnapshot();let i=r4();iB(i,r.measurePageBox()),iU(e,r.latestValues,r.snapshot?r.snapshot.layoutBox:void 0,i)}return it(this.latestValues)&&iU(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==tS.frameData.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){var e,r,i,n;let s=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=s.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=s.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=s.isSharedProjectionDirty);let o=!!this.resumingFrom||this!==s;if(!(t||o&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty)||this.attemptToResolveRelativeTarget))return;let{layout:a,layoutId:l}=this.options;if(this.layout&&(a||l)){if(this.resolvedRelativeTargetAt=tS.frameData.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=r4(),this.relativeTargetOrigin=r4(),r_(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),iB(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=r4(),this.targetWithTransforms=r4()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),r=this.target,i=this.relativeTarget,n=this.relativeParent.target,rq(r.x,i.x,n.x),rq(r.y,i.y,n.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):iB(this.target,this.layout.layoutBox),is(this.target,this.targetDelta)):iB(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=r4(),this.relativeTargetOrigin=r4(),r_(this.relativeTargetOrigin,this.target,t.target),iB(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}iQ.resolvedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||r7(this.parent.latestValues)||ie(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var t;let e=this.getLead(),r=!!this.resumingFrom||this!==e,i=!0;if((this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty))&&(i=!1),r&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===tS.frameData.timestamp&&(i=!1),i)return;let{layout:n,layoutId:s}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||s))return;iB(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,a=this.treeScale.y;(function(t,e,r,i=!1){let n,s;let o=r.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){s=(n=r[a]).projectionDelta;let o=n.instance;(!o||!o.style||"contents"!==o.style.display)&&(i&&n.options.layoutScroll&&n.scroll&&n!==n.root&&ic(t,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),s&&(e.x*=s.x.scale,e.y*=s.y.scale,is(t,s)),i&&it(n.latestValues)&&ic(t,n.latestValues))}e.x=io(e.x),e.y=io(e.y)}})(this.layoutCorrected,this.treeScale,this.path,r),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox);let{target:l}=e;if(!l){this.projectionTransform&&(this.projectionDelta=r3(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=r3(),this.projectionDeltaWithTransform=r3());let u=this.projectionTransform;rX(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.projectionTransform=iY(this.projectionDelta,this.treeScale),(this.projectionTransform!==u||this.treeScale.x!==o||this.treeScale.y!==a)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),iQ.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(t,e=!1){let r;let i=this.snapshot,n=i?i.latestValues:{},s={...this.latestValues},o=r3();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=r4(),l=(i?i.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,c=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(no));this.animationProgress=0,this.mixTargetDelta=e=>{let i=e/1e3;if(nn(o.x,t.x,i),nn(o.y,t.y,i),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,d,p,m;r_(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,ns(p.x,m.x,a.x,i),ns(p.y,m.y,a.y,i),r&&(u=this.relativeTarget,d=r,u.x.min===d.x.min&&u.x.max===d.x.max&&u.y.min===d.y.min&&u.y.max===d.y.max)&&(this.isProjectionDirty=!1),r||(r=r4()),iB(r,this.relativeTarget)}l&&(this.animationValues=s,function(t,e,r,i,n,s){n?(t.opacity=eA(0,void 0!==r.opacity?r.opacity:1,iR(i)),t.opacityExit=eA(void 0!==e.opacity?e.opacity:1,0,ij(i))):s&&(t.opacity=eA(void 0!==e.opacity?e.opacity:1,void 0!==r.opacity?r.opacity:1,i));for(let n=0;n<iV;n++){let s=`border${iE[n]}Radius`,o=iD(e,s),a=iD(r,s);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||iC(o)===iC(a)?(t[s]=Math.max(eA(iM(o),iM(a),i),0),(q.test(a)||q.test(o))&&(t[s]+="%")):t[s]=a)}(e.rotate||r.rotate)&&(t.rotate=eA(e.rotate||0,r.rotate||0,i))}(s,n,this.latestValues,i,c,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=i},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&((0,tS.Pn)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=tS.Wi.update(()=>{iw.hasAnimatedSinceResize=!0,this.currentAnimation=function(t,e,r){let i=D(t)?t:rA(t);return i.start(rf("",i,1e3,r)),i.animation}(0,0,{...t,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:r,layout:i,latestValues:n}=t;if(e&&r&&i){if(this!==t&&this.layout&&i&&nc(this.options.animationType,this.layout.layoutBox,i.layoutBox)){r=this.target||r4();let e=rH(this.layout.layoutBox.x);r.x.min=t.target.x.min,r.x.max=r.x.min+e;let i=rH(this.layout.layoutBox.y);r.y.min=t.target.y.min,r.y.max=r.y.min+i}iB(e,r),ic(e,n),rX(this.projectionDeltaWithTransform,this.layoutCorrected,e,n)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new iG),this.sharedNodes.get(t).add(e);let r=e.options.initialPromotionConfig;e.promote({transition:r?r.transition:void 0,preserveFollowOpacity:r&&r.shouldPreserveFollowOpacity?r.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){var t;let{layoutId:e}=this.options;return e&&(null===(t=this.getStack())||void 0===t?void 0:t.lead)||this}getPrevLead(){var t;let{layoutId:e}=this.options;return e?null===(t=this.getStack())||void 0===t?void 0:t.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:r}={}){let i=this.getStack();i&&i.promote(this,r),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:r}=t;if((r.rotate||r.rotateX||r.rotateY||r.rotateZ)&&(e=!0),!e)return;let i={};for(let e=0;e<iK.length;e++){let n="rotate"+iK[e];r[n]&&(i[n]=r[n],t.setStaticValue(n,0))}for(let e in t.render(),i)t.setStaticValue(e,i[e]);t.scheduleRender()}getProjectionStyles(t){var e,r;if(!this.instance||this.isSVG)return;if(!this.isVisible)return i_;let i={visibility:""},n=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,i.opacity="",i.pointerEvents=tA(null==t?void 0:t.pointerEvents)||"",i.transform=n?n(this.latestValues,""):"none",i;let s=this.getLead();if(!this.projectionDelta||!this.layout||!s.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=tA(null==t?void 0:t.pointerEvents)||""),this.hasProjected&&!it(this.latestValues)&&(e.transform=n?n({},""):"none",this.hasProjected=!1),e}let o=s.animationValues||s.latestValues;this.applyTransformsToTarget(),i.transform=iY(this.projectionDeltaWithTransform,this.treeScale,o),n&&(i.transform=n(o,i.transform));let{x:a,y:l}=this.projectionDelta;for(let t in i.transformOrigin=`${100*a.origin}% ${100*l.origin}% 0`,s.animationValues?i.opacity=s===this?null!==(r=null!==(e=o.opacity)&&void 0!==e?e:this.latestValues.opacity)&&void 0!==r?r:1:this.preserveOpacity?this.latestValues.opacity:o.opacityExit:i.opacity=s===this?void 0!==o.opacity?o.opacity:"":void 0!==o.opacityExit?o.opacityExit:0,E){if(void 0===o[t])continue;let{correct:e,applyTo:r}=E[t],n="none"===i.transform?o[t]:e(o[t],s);if(r){let t=r.length;for(let e=0;e<t;e++)i[r[e]]=n}else i[t]=n}return this.options.layoutId&&(i.pointerEvents=s===this?tA(null==t?void 0:t.pointerEvents)||"":"none"),i}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>{var e;return null===(e=t.currentAnimation)||void 0===e?void 0:e.stop()}),this.root.nodes.forEach(i9),this.root.sharedNodes.clear()}}}function i1(t){t.updateLayout()}function i2(t){var e;let r=(null===(e=t.resumeFrom)||void 0===e?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&r&&t.hasListeners("didUpdate")){let{layoutBox:e,measuredBox:i}=t.layout,{animationType:n}=t.options,s=r.source!==t.layout.source;"size"===n?r9(t=>{let i=s?r.measuredBox[t]:r.layoutBox[t],n=rH(i);i.min=e[t].min,i.max=i.min+n}):nc(n,r.layoutBox,e)&&r9(i=>{let n=s?r.measuredBox[i]:r.layoutBox[i],o=rH(e[i]);n.max=n.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[i].max=t.relativeTarget[i].min+o)});let o=r3();rX(o,e,r.layoutBox);let a=r3();s?rX(a,t.applyTransform(i,!0),r.measuredBox):rX(a,e,r.layoutBox);let l=!iN(o),u=!1;if(!t.resumeFrom){let i=t.getClosestProjectingParent();if(i&&!i.resumeFrom){let{snapshot:n,layout:s}=i;if(n&&s){let o=r4();r_(o,r.layoutBox,n.layoutBox);let a=r4();r_(a,e,s.layoutBox),iZ(o,a)||(u=!0),i.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=i)}}}t.notifyListeners("didUpdate",{layout:e,snapshot:r,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function i3(t){iQ.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function i5(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function i4(t){t.clearSnapshot()}function i9(t){t.clearMeasurements()}function i6(t){t.isLayoutDirty=!1}function i8(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function i7(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function nt(t){t.resolveTargetDelta()}function ne(t){t.calcProjection()}function nr(t){t.resetRotation()}function ni(t){t.removeLeadSnapshot()}function nn(t,e,r){t.translate=eA(e.translate,0,r),t.scale=eA(e.scale,1,r),t.origin=e.origin,t.originPoint=e.originPoint}function ns(t,e,r,i){t.min=eA(e.min,r.min,i),t.max=eA(e.max,r.max,i)}function no(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let na={duration:.45,ease:[.4,0,.1,1]},nl=t=>"undefined"!=typeof navigator&&navigator.userAgent.toLowerCase().includes(t),nu=nl("applewebkit/")&&!nl("chrome/")?Math.round:tG.Z;function nh(t){t.min=nu(t.min),t.max=nu(t.max)}function nc(t,e,r){return"position"===t||"preserve-aspect"===t&&!rG(iH(e),iH(r),.2)}let nd=i0({attachResizeListener:(t,e)=>tM(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),np={current:void 0},nm=i0({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!np.current){let t=new nd({});t.mount(window),t.setOptions({layoutScroll:!0}),np.current=t}return np.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position}),nf=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function ng(t,e,r=1){(0,t3.k)(r<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[i,n]=function(t){let e=nf.exec(t);if(!e)return[,];let[,r,i]=e;return[r,i]}(t);if(!i)return;let s=window.getComputedStyle(e).getPropertyValue(i);if(s){let t=s.trim();return ry(t)?parseFloat(t):t}return B(n)?ng(n,e,r+1):n}let ny=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),nv=t=>ny.has(t),nx=t=>Object.keys(t).some(nv),nb=t=>t===I||t===K,nw=(t,e)=>parseFloat(t.split(", ")[e]),nk=(t,e)=>(r,{transform:i})=>{if("none"===i||!i)return 0;let n=i.match(/^matrix3d\((.+)\)$/);if(n)return nw(n[1],e);{let e=i.match(/^matrix\((.+)\)$/);return e?nw(e[1],t):0}},nP=new Set(["x","y","z"]),nA=V.filter(t=>!nP.has(t)),nT={width:({x:t},{paddingLeft:e="0",paddingRight:r="0"})=>t.max-t.min-parseFloat(e)-parseFloat(r),height:({y:t},{paddingTop:e="0",paddingBottom:r="0"})=>t.max-t.min-parseFloat(e)-parseFloat(r),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:nk(4,13),y:nk(5,14)};nT.translateX=nT.x,nT.translateY=nT.y;let nS=(t,e,r)=>{let i=e.measureViewportBox(),n=e.current,s=getComputedStyle(n),{display:o}=s,a={};"none"===o&&e.setStaticValue("display",t.display||"block"),r.forEach(t=>{a[t]=nT[t](i,s)}),e.render();let l=e.measureViewportBox();return r.forEach(r=>{let i=e.getValue(r);i&&i.jump(a[r]),t[r]=nT[r](l,s)}),t},nE=(t,e,r={},i={})=>{e={...e},i={...i};let n=Object.keys(e).filter(nv),s=[],o=!1,a=[];if(n.forEach(n=>{let l;let u=t.getValue(n);if(!t.hasValue(n))return;let h=r[n],c=rE(h),d=e[n];if(tw(d)){let t=d.length,e=null===d[0]?1:0;c=rE(h=d[e]);for(let r=e;r<t&&null!==d[r];r++)l?(0,t3.k)(rE(d[r])===l,"All keyframes must be of the same type"):(l=rE(d[r]),(0,t3.k)(l===c||nb(c)&&nb(l),"Keyframes must be of the same dimension as the current value"))}else l=rE(d);if(c!==l){if(nb(c)&&nb(l)){let t=u.get();"string"==typeof t&&u.set(parseFloat(t)),"string"==typeof d?e[n]=parseFloat(d):Array.isArray(d)&&l===K&&(e[n]=d.map(parseFloat))}else(null==c?void 0:c.transform)&&(null==l?void 0:l.transform)&&(0===h||0===d)?0===h?u.set(l.transform(h)):e[n]=c.transform(d):(o||(s=function(t){let e=[];return nA.forEach(r=>{let i=t.getValue(r);void 0!==i&&(e.push([r,i.get()]),i.set(r.startsWith("scale")?1:0))}),e.length&&t.render(),e}(t),o=!0),a.push(n),i[n]=void 0!==i[n]?i[n]:e[n],u.jump(d))}}),!a.length)return{target:e,transitionEnd:i};{let r=a.indexOf("height")>=0?window.pageYOffset:null,n=nS(e,t,a);return s.length&&s.forEach(([e,r])=>{t.getValue(e).set(r)}),t.render(),w.j&&null!==r&&window.scrollTo({top:r}),{target:n,transitionEnd:i}}},nV=(t,e,r,i)=>{let n=function(t,{...e},r){let i=t.current;if(!(i instanceof Element))return{target:e,transitionEnd:r};for(let n in r&&(r={...r}),t.values.forEach(t=>{let e=t.get();if(!B(e))return;let r=ng(e,i);r&&t.set(r)}),e){let t=e[n];if(!B(t))continue;let s=ng(t,i);s&&(e[n]=s,r||(r={}),void 0===r[n]&&(r[n]=t))}return{target:e,transitionEnd:r}}(t,e,i);return function(t,e,r,i){return nx(e)?nE(t,e,r,i):{target:e,transitionEnd:i}}(t,e=n.target,r,i=n.transitionEnd)},nM={current:null},nC={current:!1},nD=new WeakMap,nR=Object.keys(b),nj=nR.length,nL=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],nF=f.length;class nB{constructor({parent:t,props:e,presenceContext:r,reducedMotionConfig:i,visualState:n},s={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>tS.Wi.render(this.render,!1,!0);let{latestValues:o,renderState:a}=n;this.latestValues=o,this.baseTarget={...o},this.initialValues=e.initial?{...o}:{},this.renderState=a,this.parent=t,this.props=e,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=s,this.isControllingVariants=g(e),this.isVariantNode=y(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:l,...u}=this.scrapeMotionValuesFromProps(e,{});for(let t in u){let e=u[t];void 0!==o[t]&&D(e)&&(e.set(o[t],!1),rg(l)&&l.add(t))}}scrapeMotionValuesFromProps(t,e){return{}}mount(t){this.current=t,nD.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),nC.current||function(){if(nC.current=!0,w.j){if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>nM.current=t.matches;t.addListener(e),e()}else nM.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||nM.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in nD.delete(this.current),this.projection&&this.projection.unmount(),(0,tS.Pn)(this.notifyUpdate),(0,tS.Pn)(this.render),this.valueSubscriptions.forEach(t=>t()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features)this.features[t].unmount();this.current=null}bindToMotionValue(t,e){let r=M.has(t),i=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&tS.Wi.update(this.notifyUpdate,!1,!0),r&&this.projection&&(this.projection.isTransformDirty=!0)}),n=e.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(t,()=>{i(),n()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}loadFeatures({children:t,...e},r,i,n){let s,o;for(let t=0;t<nj;t++){let r=nR[t],{isEnabled:i,Feature:n,ProjectionNode:a,MeasureLayout:l}=b[r];a&&(s=a),i(e)&&(!this.features[r]&&n&&(this.features[r]=new n(this)),l&&(o=l))}if(("html"===this.type||"svg"===this.type)&&!this.projection&&s){this.projection=new s(this.latestValues,this.parent&&this.parent.projection);let{layoutId:t,layout:r,drag:i,dragConstraints:o,layoutScroll:a,layoutRoot:l}=e;this.projection.setOptions({layoutId:t,layout:r,alwaysMeasureLayout:!!i||o&&c(o),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:"string"==typeof r?r:"both",initialPromotionConfig:n,layoutScroll:a,layoutRoot:l})}return o}updateFeatures(){for(let t in this.features){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):r4()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}makeTargetAnimatable(t,e=!0){return this.makeTargetAnimatableFromInstance(t,this.props,e)}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<nL.length;e++){let r=nL[e];this.propEventSubscriptions[r]&&(this.propEventSubscriptions[r](),delete this.propEventSubscriptions[r]);let i=t["on"+r];i&&(this.propEventSubscriptions[r]=this.on(r,i))}this.prevMotionValues=function(t,e,r){let{willChange:i}=e;for(let n in e){let s=e[n],o=r[n];if(D(s))t.addValue(n,s),rg(i)&&i.add(n);else if(D(o))t.addValue(n,rA(s,{owner:t})),rg(i)&&i.remove(n);else if(o!==s){if(t.hasValue(n)){let e=t.getValue(n);e.hasAnimated||e.set(s)}else{let e=t.getStaticValue(n);t.addValue(n,rA(void 0!==e?e:s,{owner:t}))}}}for(let i in r)void 0===e[i]&&t.removeValue(i);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(t=!1){if(t)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){let t=this.parent&&this.parent.getVariantContext()||{};return void 0!==this.props.initial&&(t.initial=this.props.initial),t}let e={};for(let t=0;t<nF;t++){let r=f[t],i=this.props[r];(d(i)||!1===i)&&(e[r]=i)}return e}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){e!==this.values.get(t)&&(this.removeValue(t),this.bindToMotionValue(t,e)),this.values.set(t,e),this.latestValues[t]=e.get()}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return void 0===r&&void 0!==e&&(r=rA(e,{owner:this}),this.addValue(t,r)),r}readValue(t){var e;return void 0===this.latestValues[t]&&this.current?null!==(e=this.getBaseTargetFromProps(this.props,t))&&void 0!==e?e:this.readValueFromInstance(this.current,t,this.options):this.latestValues[t]}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){var e;let{initial:r}=this.props,i="string"==typeof r||"object"==typeof r?null===(e=tx(this.props,r))||void 0===e?void 0:e[t]:void 0;if(r&&void 0!==i)return i;let n=this.getBaseTargetFromProps(this.props,t);return void 0===n||D(n)?void 0!==this.initialValues[t]&&void 0===i?void 0:this.baseTarget[t]:n}on(t,e){return this.events[t]||(this.events[t]=new rb),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class nz extends nB{sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:r}){delete e[t],delete r[t]}makeTargetAnimatableFromInstance({transition:t,transitionEnd:e,...r},{transformValues:i},n){let s=function(t,e,r){let i={};for(let n in t){let t=function(t,e){if(e)return(e[t]||e.default||e).from}(n,e);if(void 0!==t)i[n]=t;else{let t=r.getValue(n);t&&(i[n]=t.get())}}return i}(r,t||{},this);if(i&&(e&&(e=i(e)),r&&(r=i(r)),s&&(s=i(s))),n){!function(t,e,r){var i,n;let s=Object.keys(e).filter(e=>!t.hasValue(e)),o=s.length;if(o)for(let a=0;a<o;a++){let o=s[a],l=e[o],u=null;Array.isArray(l)&&(u=l[0]),null===u&&(u=null!==(n=null!==(i=r[o])&&void 0!==i?i:t.readValue(o))&&void 0!==n?n:e[o]),null!=u&&("string"==typeof u&&(ry(u)||rd(u))?u=parseFloat(u):!rM(u)&&eI.test(l)&&(u=rc(o,l)),t.addValue(o,rA(u,{owner:t})),void 0===r[o]&&(r[o]=u),null!==u&&t.setBaseTarget(o,u))}}(this,r,s);let t=nV(this,r,s,e);e=t.transitionEnd,r=t.target}return{transition:t,transitionEnd:e,...r}}}class nO extends nz{constructor(){super(...arguments),this.type="html"}readValueFromInstance(t,e){if(M.has(e)){let t=rh(e);return t&&t.default||0}{let r=window.getComputedStyle(t),i=(F(e)?r.getPropertyValue(e):r[e])||0;return"string"==typeof i?i.trim():i}}measureInstanceViewportBox(t,{transformPagePoint:e}){return id(t,e)}build(t,e,r,i){tr(t,e,r,i.transformTemplate)}scrapeMotionValuesFromProps(t,e){return ty(t,e)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;D(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}renderInstance(t,e,r,i){tm(t,e,r,i)}}class nI extends nz{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(M.has(e)){let t=rh(e);return t&&t.default||0}return e=tf.has(e)?e:u(e),t.getAttribute(e)}measureInstanceViewportBox(){return r4()}scrapeMotionValuesFromProps(t,e){return tv(t,e)}build(t,e,r,i){tc(t,e,r,this.isSVGTag,i.transformTemplate)}renderInstance(t,e,r,i){tg(t,e,r,i)}mount(t){this.isSVGTag=tp(t.tagName),super.mount(t)}}let nW=(t,e)=>S(t)?new nI(e,{enableHardwareAcceleration:!1}):new nO(e,{enableHardwareAcceleration:!0}),nU={animation:{Feature:rB},exit:{Feature:rO},inView:{Feature:t0},tap:{Feature:tX},focus:{Feature:tZ},hover:{Feature:tN},pan:{Feature:ib},drag:{Feature:iv,ProjectionNode:nm,MeasureLayout:iT},layout:{ProjectionNode:nm,MeasureLayout:iT}},n$=function(t){function e(e,r={}){return function({preloadedFeatures:t,createVisualElement:e,useRender:r,useVisualState:u,Component:p}){t&&function(t){for(let e in t)b[e]={...b[e],...t[e]}}(t);let m=(0,i.forwardRef)(function(m,f){var y;let x;let b={...(0,i.useContext)(n),...m,layoutId:function({layoutId:t}){let e=(0,i.useContext)(k.p).id;return e&&void 0!==t?e+"-"+t:t}(m)},{isStatic:A}=b,T=function(t){let{initial:e,animate:r}=function(t,e){if(g(t)){let{initial:e,animate:r}=t;return{initial:!1===e||d(e)?e:void 0,animate:d(r)?r:void 0}}return!1!==t.inherit?e:{}}(t,(0,i.useContext)(s));return(0,i.useMemo)(()=>({initial:e,animate:r}),[v(e),v(r)])}(m),S=u(m,A);if(!A&&w.j){T.visualElement=function(t,e,r,u){let{visualElement:c}=(0,i.useContext)(s),d=(0,i.useContext)(l),p=(0,i.useContext)(o.O),m=(0,i.useContext)(n).reducedMotion,f=(0,i.useRef)();u=u||d.renderer,!f.current&&u&&(f.current=u(t,{visualState:e,parent:c,props:r,presenceContext:p,blockInitialAnimation:!!p&&!1===p.initial,reducedMotionConfig:m}));let g=f.current;(0,i.useInsertionEffect)(()=>{g&&g.update(r,p)});let y=(0,i.useRef)(!!(r[h]&&!window.HandoffComplete));return(0,a.L)(()=>{g&&(g.render(),y.current&&g.animationState&&g.animationState.animateChanges())}),(0,i.useEffect)(()=>{g&&(g.updateFeatures(),!y.current&&g.animationState&&g.animationState.animateChanges(),y.current&&(y.current=!1,window.HandoffComplete=!0))}),g}(p,S,b,e);let r=(0,i.useContext)(P),u=(0,i.useContext)(l).strict;T.visualElement&&(x=T.visualElement.loadFeatures(b,u,t,r))}return i.createElement(s.Provider,{value:T},x&&T.visualElement?i.createElement(x,{visualElement:T.visualElement,...b}):null,r(p,m,(y=T.visualElement,(0,i.useCallback)(t=>{t&&S.mount&&S.mount(t),y&&(t?y.mount(t):y.unmount()),f&&("function"==typeof f?f(t):c(f)&&(f.current=t))},[y])),S,A,T.visualElement))});return m[A]=p,m}(t(e,r))}if("undefined"==typeof Proxy)return e;let r=new Map;return new Proxy(e,{get:(t,i)=>(r.has(i)||r.set(i,e(i)),r.get(i))})}((t,e)=>(function(t,{forwardMotionProps:e=!1},r,n){return{...S(t)?tE:tV,preloadedFeatures:r,useRender:function(t=!1){return(e,r,n,{latestValues:s},o)=>{let a=(S(e)?function(t,e,r,n){let s=(0,i.useMemo)(()=>{let r=td();return tc(r,e,{enableHardwareAcceleration:!1},tp(n),t.transformTemplate),{...r.attrs,style:{...r.style}}},[e]);if(t.style){let e={};tn(e,t.style,t),s.style={...e,...s.style}}return s}:function(t,e,r){let n={},s=function(t,e,r){let n=t.style||{},s={};return tn(s,n,t),Object.assign(s,function({transformTemplate:t},e,r){return(0,i.useMemo)(()=>{let i=ti();return tr(i,e,{enableHardwareAcceleration:!r},t),Object.assign({},i.vars,i.style)},[e])}(t,e,r)),t.transformValues?t.transformValues(s):s}(t,e,r);return t.drag&&!1!==t.dragListener&&(n.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(n.tabIndex=0),n.style=s,n})(r,s,o,e),l={...function(t,e,r){let i={};for(let n in t)("values"!==n||"object"!=typeof t.values)&&(ta(n)||!0===r&&to(n)||!e&&!to(n)||t.draggable&&n.startsWith("onDrag"))&&(i[n]=t[n]);return i}(r,"string"==typeof e,t),...a,ref:n},{children:u}=r,h=(0,i.useMemo)(()=>D(u)?u.get():u,[u]);return(0,i.createElement)(e,{...l,children:h})}}(e),createVisualElement:n,Component:t}})(t,e,nU,nW))},87222:(t,e,r)=>{"use strict";r.d(e,{K:()=>n,k:()=>s});var i=r(30254);let n=i.Z,s=i.Z},79398:(t,e,r)=>{"use strict";r.d(e,{j:()=>i});let i="undefined"!=typeof document},30254:(t,e,r)=>{"use strict";r.d(e,{Z:()=>i});let i=t=>t},40207:(t,e,r)=>{"use strict";r.d(e,{h:()=>n});var i=r(3729);function n(t){let e=(0,i.useRef)(null);return null===e.current&&(e.current=t()),e.current}},19038:(t,e,r)=>{"use strict";r.d(e,{L:()=>n});var i=r(3729);let n=r(79398).j?i.useLayoutEffect:i.useEffect},79377:(t,e,r)=>{"use strict";r.d(e,{m6:()=>tu});let i=t=>{let e=a(t),{conflictingClassGroups:r,conflictingClassGroupModifiers:i}=t;return{getClassGroupId:t=>{let r=t.split("-");return""===r[0]&&1!==r.length&&r.shift(),n(r,e)||o(t)},getConflictingClassGroupIds:(t,e)=>{let n=r[t]||[];return e&&i[t]?[...n,...i[t]]:n}}},n=(t,e)=>{if(0===t.length)return e.classGroupId;let r=t[0],i=e.nextPart.get(r),s=i?n(t.slice(1),i):void 0;if(s)return s;if(0===e.validators.length)return;let o=t.join("-");return e.validators.find(({validator:t})=>t(o))?.classGroupId},s=/^\[(.+)\]$/,o=t=>{if(s.test(t)){let e=s.exec(t)[1],r=e?.substring(0,e.indexOf(":"));if(r)return"arbitrary.."+r}},a=t=>{let{theme:e,classGroups:r}=t,i={nextPart:new Map,validators:[]};for(let t in r)l(r[t],i,t,e);return i},l=(t,e,r,i)=>{t.forEach(t=>{if("string"==typeof t){(""===t?e:u(e,t)).classGroupId=r;return}if("function"==typeof t){if(h(t)){l(t(i),e,r,i);return}e.validators.push({validator:t,classGroupId:r});return}Object.entries(t).forEach(([t,n])=>{l(n,u(e,t),r,i)})})},u=(t,e)=>{let r=t;return e.split("-").forEach(t=>{r.nextPart.has(t)||r.nextPart.set(t,{nextPart:new Map,validators:[]}),r=r.nextPart.get(t)}),r},h=t=>t.isThemeGetter,c=t=>{if(t<1)return{get:()=>void 0,set:()=>{}};let e=0,r=new Map,i=new Map,n=(n,s)=>{r.set(n,s),++e>t&&(e=0,i=r,r=new Map)};return{get(t){let e=r.get(t);return void 0!==e?e:void 0!==(e=i.get(t))?(n(t,e),e):void 0},set(t,e){r.has(t)?r.set(t,e):n(t,e)}}},d=t=>{let{prefix:e,experimentalParseClassName:r}=t,i=t=>{let e;let r=[],i=0,n=0,s=0;for(let o=0;o<t.length;o++){let a=t[o];if(0===i&&0===n){if(":"===a){r.push(t.slice(s,o)),s=o+1;continue}if("/"===a){e=o;continue}}"["===a?i++:"]"===a?i--:"("===a?n++:")"===a&&n--}let o=0===r.length?t:t.substring(s),a=p(o);return{modifiers:r,hasImportantModifier:a!==o,baseClassName:a,maybePostfixModifierPosition:e&&e>s?e-s:void 0}};if(e){let t=e+":",r=i;i=e=>e.startsWith(t)?r(e.substring(t.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:e,maybePostfixModifierPosition:void 0}}if(r){let t=i;i=e=>r({className:e,parseClassName:t})}return i},p=t=>t.endsWith("!")?t.substring(0,t.length-1):t.startsWith("!")?t.substring(1):t,m=t=>{let e=Object.fromEntries(t.orderSensitiveModifiers.map(t=>[t,!0]));return t=>{if(t.length<=1)return t;let r=[],i=[];return t.forEach(t=>{"["===t[0]||e[t]?(r.push(...i.sort(),t),i=[]):i.push(t)}),r.push(...i.sort()),r}},f=t=>({cache:c(t.cacheSize),parseClassName:d(t),sortModifiers:m(t),...i(t)}),g=/\s+/,y=(t,e)=>{let{parseClassName:r,getClassGroupId:i,getConflictingClassGroupIds:n,sortModifiers:s}=e,o=[],a=t.trim().split(g),l="";for(let t=a.length-1;t>=0;t-=1){let e=a[t],{isExternal:u,modifiers:h,hasImportantModifier:c,baseClassName:d,maybePostfixModifierPosition:p}=r(e);if(u){l=e+(l.length>0?" "+l:l);continue}let m=!!p,f=i(m?d.substring(0,p):d);if(!f){if(!m||!(f=i(d))){l=e+(l.length>0?" "+l:l);continue}m=!1}let g=s(h).join(":"),y=c?g+"!":g,v=y+f;if(o.includes(v))continue;o.push(v);let x=n(f,m);for(let t=0;t<x.length;++t){let e=x[t];o.push(y+e)}l=e+(l.length>0?" "+l:l)}return l};function v(){let t,e,r=0,i="";for(;r<arguments.length;)(t=arguments[r++])&&(e=x(t))&&(i&&(i+=" "),i+=e);return i}let x=t=>{let e;if("string"==typeof t)return t;let r="";for(let i=0;i<t.length;i++)t[i]&&(e=x(t[i]))&&(r&&(r+=" "),r+=e);return r},b=t=>{let e=e=>e[t]||[];return e.isThemeGetter=!0,e},w=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,k=/^\((?:(\w[\w-]*):)?(.+)\)$/i,P=/^\d+\/\d+$/,A=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,T=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,S=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,E=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,V=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,M=t=>P.test(t),C=t=>!!t&&!Number.isNaN(Number(t)),D=t=>!!t&&Number.isInteger(Number(t)),R=t=>t.endsWith("%")&&C(t.slice(0,-1)),j=t=>A.test(t),L=()=>!0,F=t=>T.test(t)&&!S.test(t),B=()=>!1,z=t=>E.test(t),O=t=>V.test(t),I=t=>!U(t)&&!Y(t),W=t=>tt(t,tn,B),U=t=>w.test(t),$=t=>tt(t,ts,F),N=t=>tt(t,to,C),Z=t=>tt(t,tr,B),H=t=>tt(t,ti,O),G=t=>tt(t,tl,z),Y=t=>k.test(t),X=t=>te(t,ts),q=t=>te(t,ta),K=t=>te(t,tr),_=t=>te(t,tn),J=t=>te(t,ti),Q=t=>te(t,tl,!0),tt=(t,e,r)=>{let i=w.exec(t);return!!i&&(i[1]?e(i[1]):r(i[2]))},te=(t,e,r=!1)=>{let i=k.exec(t);return!!i&&(i[1]?e(i[1]):r)},tr=t=>"position"===t||"percentage"===t,ti=t=>"image"===t||"url"===t,tn=t=>"length"===t||"size"===t||"bg-size"===t,ts=t=>"length"===t,to=t=>"number"===t,ta=t=>"family-name"===t,tl=t=>"shadow"===t;Symbol.toStringTag;let tu=function(t){let e,r,i;let n=function(o){return r=(e=f([].reduce((t,e)=>e(t),t()))).cache.get,i=e.cache.set,n=s,s(o)};function s(t){let n=r(t);if(n)return n;let s=y(t,e);return i(t,s),s}return function(){return n(v.apply(null,arguments))}}(()=>{let t=b("color"),e=b("font"),r=b("text"),i=b("font-weight"),n=b("tracking"),s=b("leading"),o=b("breakpoint"),a=b("container"),l=b("spacing"),u=b("radius"),h=b("shadow"),c=b("inset-shadow"),d=b("text-shadow"),p=b("drop-shadow"),m=b("blur"),f=b("perspective"),g=b("aspect"),y=b("ease"),v=b("animate"),x=()=>["auto","avoid","all","avoid-page","page","left","right","column"],w=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],k=()=>[...w(),Y,U],P=()=>["auto","hidden","clip","visible","scroll"],A=()=>["auto","contain","none"],T=()=>[Y,U,l],S=()=>[M,"full","auto",...T()],E=()=>[D,"none","subgrid",Y,U],V=()=>["auto",{span:["full",D,Y,U]},D,Y,U],F=()=>[D,"auto",Y,U],B=()=>["auto","min","max","fr",Y,U],z=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],O=()=>["start","end","center","stretch","center-safe","end-safe"],tt=()=>["auto",...T()],te=()=>[M,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...T()],tr=()=>[t,Y,U],ti=()=>[...w(),K,Z,{position:[Y,U]}],tn=()=>["no-repeat",{repeat:["","x","y","space","round"]}],ts=()=>["auto","cover","contain",_,W,{size:[Y,U]}],to=()=>[R,X,$],ta=()=>["","none","full",u,Y,U],tl=()=>["",C,X,$],tu=()=>["solid","dashed","dotted","double"],th=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],tc=()=>[C,R,K,Z],td=()=>["","none",m,Y,U],tp=()=>["none",C,Y,U],tm=()=>["none",C,Y,U],tf=()=>[C,Y,U],tg=()=>[M,"full",...T()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[j],breakpoint:[j],color:[L],container:[j],"drop-shadow":[j],ease:["in","out","in-out"],font:[I],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[j],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[j],shadow:[j],spacing:["px",C],text:[j],"text-shadow":[j],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",M,U,Y,g]}],container:["container"],columns:[{columns:[C,U,Y,a]}],"break-after":[{"break-after":x()}],"break-before":[{"break-before":x()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:k()}],overflow:[{overflow:P()}],"overflow-x":[{"overflow-x":P()}],"overflow-y":[{"overflow-y":P()}],overscroll:[{overscroll:A()}],"overscroll-x":[{"overscroll-x":A()}],"overscroll-y":[{"overscroll-y":A()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:S()}],"inset-x":[{"inset-x":S()}],"inset-y":[{"inset-y":S()}],start:[{start:S()}],end:[{end:S()}],top:[{top:S()}],right:[{right:S()}],bottom:[{bottom:S()}],left:[{left:S()}],visibility:["visible","invisible","collapse"],z:[{z:[D,"auto",Y,U]}],basis:[{basis:[M,"full","auto",a,...T()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[C,M,"auto","initial","none",U]}],grow:[{grow:["",C,Y,U]}],shrink:[{shrink:["",C,Y,U]}],order:[{order:[D,"first","last","none",Y,U]}],"grid-cols":[{"grid-cols":E()}],"col-start-end":[{col:V()}],"col-start":[{"col-start":F()}],"col-end":[{"col-end":F()}],"grid-rows":[{"grid-rows":E()}],"row-start-end":[{row:V()}],"row-start":[{"row-start":F()}],"row-end":[{"row-end":F()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":B()}],"auto-rows":[{"auto-rows":B()}],gap:[{gap:T()}],"gap-x":[{"gap-x":T()}],"gap-y":[{"gap-y":T()}],"justify-content":[{justify:[...z(),"normal"]}],"justify-items":[{"justify-items":[...O(),"normal"]}],"justify-self":[{"justify-self":["auto",...O()]}],"align-content":[{content:["normal",...z()]}],"align-items":[{items:[...O(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...O(),{baseline:["","last"]}]}],"place-content":[{"place-content":z()}],"place-items":[{"place-items":[...O(),"baseline"]}],"place-self":[{"place-self":["auto",...O()]}],p:[{p:T()}],px:[{px:T()}],py:[{py:T()}],ps:[{ps:T()}],pe:[{pe:T()}],pt:[{pt:T()}],pr:[{pr:T()}],pb:[{pb:T()}],pl:[{pl:T()}],m:[{m:tt()}],mx:[{mx:tt()}],my:[{my:tt()}],ms:[{ms:tt()}],me:[{me:tt()}],mt:[{mt:tt()}],mr:[{mr:tt()}],mb:[{mb:tt()}],ml:[{ml:tt()}],"space-x":[{"space-x":T()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":T()}],"space-y-reverse":["space-y-reverse"],size:[{size:te()}],w:[{w:[a,"screen",...te()]}],"min-w":[{"min-w":[a,"screen","none",...te()]}],"max-w":[{"max-w":[a,"screen","none","prose",{screen:[o]},...te()]}],h:[{h:["screen","lh",...te()]}],"min-h":[{"min-h":["screen","lh","none",...te()]}],"max-h":[{"max-h":["screen","lh",...te()]}],"font-size":[{text:["base",r,X,$]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[i,Y,N]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",R,U]}],"font-family":[{font:[q,U,e]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[n,Y,U]}],"line-clamp":[{"line-clamp":[C,"none",Y,N]}],leading:[{leading:[s,...T()]}],"list-image":[{"list-image":["none",Y,U]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",Y,U]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:tr()}],"text-color":[{text:tr()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...tu(),"wavy"]}],"text-decoration-thickness":[{decoration:[C,"from-font","auto",Y,$]}],"text-decoration-color":[{decoration:tr()}],"underline-offset":[{"underline-offset":[C,"auto",Y,U]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:T()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",Y,U]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",Y,U]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:ti()}],"bg-repeat":[{bg:tn()}],"bg-size":[{bg:ts()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},D,Y,U],radial:["",Y,U],conic:[D,Y,U]},J,H]}],"bg-color":[{bg:tr()}],"gradient-from-pos":[{from:to()}],"gradient-via-pos":[{via:to()}],"gradient-to-pos":[{to:to()}],"gradient-from":[{from:tr()}],"gradient-via":[{via:tr()}],"gradient-to":[{to:tr()}],rounded:[{rounded:ta()}],"rounded-s":[{"rounded-s":ta()}],"rounded-e":[{"rounded-e":ta()}],"rounded-t":[{"rounded-t":ta()}],"rounded-r":[{"rounded-r":ta()}],"rounded-b":[{"rounded-b":ta()}],"rounded-l":[{"rounded-l":ta()}],"rounded-ss":[{"rounded-ss":ta()}],"rounded-se":[{"rounded-se":ta()}],"rounded-ee":[{"rounded-ee":ta()}],"rounded-es":[{"rounded-es":ta()}],"rounded-tl":[{"rounded-tl":ta()}],"rounded-tr":[{"rounded-tr":ta()}],"rounded-br":[{"rounded-br":ta()}],"rounded-bl":[{"rounded-bl":ta()}],"border-w":[{border:tl()}],"border-w-x":[{"border-x":tl()}],"border-w-y":[{"border-y":tl()}],"border-w-s":[{"border-s":tl()}],"border-w-e":[{"border-e":tl()}],"border-w-t":[{"border-t":tl()}],"border-w-r":[{"border-r":tl()}],"border-w-b":[{"border-b":tl()}],"border-w-l":[{"border-l":tl()}],"divide-x":[{"divide-x":tl()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":tl()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...tu(),"hidden","none"]}],"divide-style":[{divide:[...tu(),"hidden","none"]}],"border-color":[{border:tr()}],"border-color-x":[{"border-x":tr()}],"border-color-y":[{"border-y":tr()}],"border-color-s":[{"border-s":tr()}],"border-color-e":[{"border-e":tr()}],"border-color-t":[{"border-t":tr()}],"border-color-r":[{"border-r":tr()}],"border-color-b":[{"border-b":tr()}],"border-color-l":[{"border-l":tr()}],"divide-color":[{divide:tr()}],"outline-style":[{outline:[...tu(),"none","hidden"]}],"outline-offset":[{"outline-offset":[C,Y,U]}],"outline-w":[{outline:["",C,X,$]}],"outline-color":[{outline:tr()}],shadow:[{shadow:["","none",h,Q,G]}],"shadow-color":[{shadow:tr()}],"inset-shadow":[{"inset-shadow":["none",c,Q,G]}],"inset-shadow-color":[{"inset-shadow":tr()}],"ring-w":[{ring:tl()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:tr()}],"ring-offset-w":[{"ring-offset":[C,$]}],"ring-offset-color":[{"ring-offset":tr()}],"inset-ring-w":[{"inset-ring":tl()}],"inset-ring-color":[{"inset-ring":tr()}],"text-shadow":[{"text-shadow":["none",d,Q,G]}],"text-shadow-color":[{"text-shadow":tr()}],opacity:[{opacity:[C,Y,U]}],"mix-blend":[{"mix-blend":[...th(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":th()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[C]}],"mask-image-linear-from-pos":[{"mask-linear-from":tc()}],"mask-image-linear-to-pos":[{"mask-linear-to":tc()}],"mask-image-linear-from-color":[{"mask-linear-from":tr()}],"mask-image-linear-to-color":[{"mask-linear-to":tr()}],"mask-image-t-from-pos":[{"mask-t-from":tc()}],"mask-image-t-to-pos":[{"mask-t-to":tc()}],"mask-image-t-from-color":[{"mask-t-from":tr()}],"mask-image-t-to-color":[{"mask-t-to":tr()}],"mask-image-r-from-pos":[{"mask-r-from":tc()}],"mask-image-r-to-pos":[{"mask-r-to":tc()}],"mask-image-r-from-color":[{"mask-r-from":tr()}],"mask-image-r-to-color":[{"mask-r-to":tr()}],"mask-image-b-from-pos":[{"mask-b-from":tc()}],"mask-image-b-to-pos":[{"mask-b-to":tc()}],"mask-image-b-from-color":[{"mask-b-from":tr()}],"mask-image-b-to-color":[{"mask-b-to":tr()}],"mask-image-l-from-pos":[{"mask-l-from":tc()}],"mask-image-l-to-pos":[{"mask-l-to":tc()}],"mask-image-l-from-color":[{"mask-l-from":tr()}],"mask-image-l-to-color":[{"mask-l-to":tr()}],"mask-image-x-from-pos":[{"mask-x-from":tc()}],"mask-image-x-to-pos":[{"mask-x-to":tc()}],"mask-image-x-from-color":[{"mask-x-from":tr()}],"mask-image-x-to-color":[{"mask-x-to":tr()}],"mask-image-y-from-pos":[{"mask-y-from":tc()}],"mask-image-y-to-pos":[{"mask-y-to":tc()}],"mask-image-y-from-color":[{"mask-y-from":tr()}],"mask-image-y-to-color":[{"mask-y-to":tr()}],"mask-image-radial":[{"mask-radial":[Y,U]}],"mask-image-radial-from-pos":[{"mask-radial-from":tc()}],"mask-image-radial-to-pos":[{"mask-radial-to":tc()}],"mask-image-radial-from-color":[{"mask-radial-from":tr()}],"mask-image-radial-to-color":[{"mask-radial-to":tr()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":w()}],"mask-image-conic-pos":[{"mask-conic":[C]}],"mask-image-conic-from-pos":[{"mask-conic-from":tc()}],"mask-image-conic-to-pos":[{"mask-conic-to":tc()}],"mask-image-conic-from-color":[{"mask-conic-from":tr()}],"mask-image-conic-to-color":[{"mask-conic-to":tr()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:ti()}],"mask-repeat":[{mask:tn()}],"mask-size":[{mask:ts()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",Y,U]}],filter:[{filter:["","none",Y,U]}],blur:[{blur:td()}],brightness:[{brightness:[C,Y,U]}],contrast:[{contrast:[C,Y,U]}],"drop-shadow":[{"drop-shadow":["","none",p,Q,G]}],"drop-shadow-color":[{"drop-shadow":tr()}],grayscale:[{grayscale:["",C,Y,U]}],"hue-rotate":[{"hue-rotate":[C,Y,U]}],invert:[{invert:["",C,Y,U]}],saturate:[{saturate:[C,Y,U]}],sepia:[{sepia:["",C,Y,U]}],"backdrop-filter":[{"backdrop-filter":["","none",Y,U]}],"backdrop-blur":[{"backdrop-blur":td()}],"backdrop-brightness":[{"backdrop-brightness":[C,Y,U]}],"backdrop-contrast":[{"backdrop-contrast":[C,Y,U]}],"backdrop-grayscale":[{"backdrop-grayscale":["",C,Y,U]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[C,Y,U]}],"backdrop-invert":[{"backdrop-invert":["",C,Y,U]}],"backdrop-opacity":[{"backdrop-opacity":[C,Y,U]}],"backdrop-saturate":[{"backdrop-saturate":[C,Y,U]}],"backdrop-sepia":[{"backdrop-sepia":["",C,Y,U]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":T()}],"border-spacing-x":[{"border-spacing-x":T()}],"border-spacing-y":[{"border-spacing-y":T()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",Y,U]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[C,"initial",Y,U]}],ease:[{ease:["linear","initial",y,Y,U]}],delay:[{delay:[C,Y,U]}],animate:[{animate:["none",v,Y,U]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[f,Y,U]}],"perspective-origin":[{"perspective-origin":k()}],rotate:[{rotate:tp()}],"rotate-x":[{"rotate-x":tp()}],"rotate-y":[{"rotate-y":tp()}],"rotate-z":[{"rotate-z":tp()}],scale:[{scale:tm()}],"scale-x":[{"scale-x":tm()}],"scale-y":[{"scale-y":tm()}],"scale-z":[{"scale-z":tm()}],"scale-3d":["scale-3d"],skew:[{skew:tf()}],"skew-x":[{"skew-x":tf()}],"skew-y":[{"skew-y":tf()}],transform:[{transform:[Y,U,"","none","gpu","cpu"]}],"transform-origin":[{origin:k()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:tg()}],"translate-x":[{"translate-x":tg()}],"translate-y":[{"translate-y":tg()}],"translate-z":[{"translate-z":tg()}],"translate-none":["translate-none"],accent:[{accent:tr()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:tr()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",Y,U]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":T()}],"scroll-mx":[{"scroll-mx":T()}],"scroll-my":[{"scroll-my":T()}],"scroll-ms":[{"scroll-ms":T()}],"scroll-me":[{"scroll-me":T()}],"scroll-mt":[{"scroll-mt":T()}],"scroll-mr":[{"scroll-mr":T()}],"scroll-mb":[{"scroll-mb":T()}],"scroll-ml":[{"scroll-ml":T()}],"scroll-p":[{"scroll-p":T()}],"scroll-px":[{"scroll-px":T()}],"scroll-py":[{"scroll-py":T()}],"scroll-ps":[{"scroll-ps":T()}],"scroll-pe":[{"scroll-pe":T()}],"scroll-pt":[{"scroll-pt":T()}],"scroll-pr":[{"scroll-pr":T()}],"scroll-pb":[{"scroll-pb":T()}],"scroll-pl":[{"scroll-pl":T()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",Y,U]}],fill:[{fill:["none",...tr()]}],"stroke-w":[{stroke:[C,X,$,N]}],stroke:[{stroke:["none",...tr()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})}};