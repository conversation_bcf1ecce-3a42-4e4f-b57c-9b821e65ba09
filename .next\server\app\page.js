(()=>{var e={};e.id=931,e.ids=[931],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},32081:e=>{"use strict";e.exports=require("child_process")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},27789:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>c});var r=s(50482),a=s(69108),i=s(62563),n=s.n(i),l=s(68300),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,70751)),"C:\\Users\\<USER>\\OneDrive - Suomalaisen Yhteiskoulun Osakeyhti\xf6\\Desktop\\Coding\\schoolfinder\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,78062)),"C:\\Users\\<USER>\\OneDrive - Suomalaisen Yhteiskoulun Osakeyhti\xf6\\Desktop\\Coding\\schoolfinder\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,69361,23)),"next/dist/client/components/not-found-error"]}],d=["C:\\Users\\<USER>\\OneDrive - Suomalaisen Yhteiskoulun Osakeyhti\xf6\\Desktop\\Coding\\schoolfinder\\app\\page.tsx"],m="/page",x={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},6253:(e,t,s)=>{Promise.resolve().then(s.bind(s,60470))},60470:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>q});var r=s(95344),a=s(47674),i=s(22254),n=s(3729),l=s(95252),o=s(57916),c=s(80508),d=s(89895),m=s(23485),x=s(69224);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let u=(0,x.Z)("Smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]]),p=(0,x.Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);var h=s(71206),y=s(23511),g=s(73644),f=s(14513),w=s(18822);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let b=(0,x.Z)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);var j=s(1222),v=s(53148),N=s(56413),k=s(91626),C=s(44669);function P({isOpen:e,onClose:t,mode:s,onModeChange:i}){let[l,c]=(0,n.useState)({name:"",email:"",password:"",confirmPassword:""}),[d,m]=(0,n.useState)(!1),[x,u]=(0,n.useState)({}),{login:p,register:P,isLoading:S}=(0,N.t)(),q=(e,t)=>{c(s=>({...s,[e]:t})),x[e]&&u(t=>({...t,[e]:""}))},E=()=>{let e={};if((0,k.oH)(l.email)||(e.email="Please enter a valid email address"),l.password){if("register"===s){let t=(0,k.uo)(l.password);t.isValid||(e.password=t.errors[0])}}else e.password="Password is required";return"register"===s&&(l.name.trim()||(e.name="Name is required"),l.password!==l.confirmPassword&&(e.confirmPassword="Passwords do not match")),u(e),0===Object.keys(e).length},z=async e=>{if(e.preventDefault(),E())try{("login"===s?await p(l.email,l.password):await P(l.name,l.email,l.password))&&(C.default.success("login"===s?"Welcome back!":"Account created successfully!"),t())}catch(e){C.default.error("Something went wrong. Please try again.")}};return e?r.jsx(g.M,{children:(0,r.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:[r.jsx(o.E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},onClick:t,className:"absolute inset-0 bg-black/50 backdrop-blur-sm"}),(0,r.jsxs)(o.E.div,{initial:{opacity:0,scale:.95,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.95,y:20},className:"relative w-full max-w-md bg-white rounded-2xl shadow-2xl",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[r.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"login"===s?"Welcome Back":"Create Account"}),r.jsx("button",{onClick:t,className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:r.jsx(f.Z,{className:"w-5 h-5"})})]}),(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)(y.z,{onClick:()=>{(0,a.signIn)("google",{callbackUrl:"/dashboard"})},variant:"outline",className:"w-full mb-6",disabled:S,children:[r.jsx(h.Z,{className:"w-5 h-5 mr-2"}),"Continue with Google"]}),(0,r.jsxs)("div",{className:"relative mb-6",children:[r.jsx("div",{className:"absolute inset-0 flex items-center",children:r.jsx("div",{className:"w-full border-t border-gray-300"})}),r.jsx("div",{className:"relative flex justify-center text-sm",children:r.jsx("span",{className:"px-2 bg-white text-gray-500",children:"Or continue with email"})})]}),(0,r.jsxs)("form",{onSubmit:z,className:"space-y-4",children:["register"===s&&(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Full Name"}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx(w.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),r.jsx("input",{type:"text",value:l.name,onChange:e=>q("name",e.target.value),className:`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent ${x.name?"border-red-500":"border-gray-300"}`,placeholder:"Enter your full name",disabled:S})]}),x.name&&r.jsx("p",{className:"mt-1 text-sm text-red-600",children:x.name})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email Address"}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx(h.Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),r.jsx("input",{type:"email",value:l.email,onChange:e=>q("email",e.target.value),className:`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent ${x.email?"border-red-500":"border-gray-300"}`,placeholder:"Enter your email",disabled:S})]}),x.email&&r.jsx("p",{className:"mt-1 text-sm text-red-600",children:x.email})]}),(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Password"}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx(b,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),r.jsx("input",{type:d?"text":"password",value:l.password,onChange:e=>q("password",e.target.value),className:`w-full pl-10 pr-12 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent ${x.password?"border-red-500":"border-gray-300"}`,placeholder:"Enter your password",disabled:S}),r.jsx("button",{type:"button",onClick:()=>m(!d),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",children:d?r.jsx(j.Z,{className:"w-5 h-5"}):r.jsx(v.Z,{className:"w-5 h-5"})})]}),x.password&&r.jsx("p",{className:"mt-1 text-sm text-red-600",children:x.password})]}),"register"===s&&(0,r.jsxs)("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Confirm Password"}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx(b,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),r.jsx("input",{type:d?"text":"password",value:l.confirmPassword,onChange:e=>q("confirmPassword",e.target.value),className:`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent ${x.confirmPassword?"border-red-500":"border-gray-300"}`,placeholder:"Confirm your password",disabled:S})]}),x.confirmPassword&&r.jsx("p",{className:"mt-1 text-sm text-red-600",children:x.confirmPassword})]}),r.jsx(y.z,{type:"submit",className:"w-full",disabled:S,children:S?"Please wait...":"login"===s?"Sign In":"Create Account"})]}),r.jsx("div",{className:"mt-6 text-center",children:(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["login"===s?"Don't have an account?":"Already have an account?",r.jsx("button",{onClick:()=>i("login"===s?"register":"login"),className:"ml-1 text-primary-600 hover:text-primary-700 font-medium",disabled:S,children:"login"===s?"Sign up":"Sign in"})]})})]})]})]})}):null}function S(){let[e,t]=(0,n.useState)(!1),[s,i]=(0,n.useState)("login"),l=()=>{i("register"),t(!0)},x=[{icon:c.Z,title:"Real-time Location",description:"See where your friends are on campus in real-time with precise indoor positioning."},{icon:d.Z,title:"Friend Network",description:"Connect with classmates and build your campus social network easily."},{icon:m.Z,title:"Privacy First",description:"Full control over your location sharing with advanced privacy settings."},{icon:u,title:"Mobile Optimized",description:"Works perfectly on all devices with a native app-like experience."}];return(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50",children:[r.jsx("header",{className:"relative z-10 px-4 py-6",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto flex items-center justify-between",children:[(0,r.jsxs)(o.E.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},className:"flex items-center space-x-2",children:[r.jsx("div",{className:"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center",children:r.jsx(c.Z,{className:"w-5 h-5 text-white"})}),r.jsx("span",{className:"text-xl font-bold text-gray-900",children:"SYK SchoolFinder"})]}),(0,r.jsxs)(o.E.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},className:"flex items-center space-x-4",children:[r.jsx(y.z,{variant:"ghost",onClick:()=>{i("login"),t(!0)},className:"text-gray-600 hover:text-gray-900",children:"Sign In"}),r.jsx(y.z,{onClick:l,children:"Get Started"})]})]})}),(0,r.jsxs)("section",{className:"relative px-4 py-20",children:[(0,r.jsxs)("div",{className:"max-w-7xl mx-auto text-center",children:[(0,r.jsxs)(o.E.h1,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},className:"text-5xl md:text-6xl font-bold text-gray-900 mb-6",children:["Find Your Friends",r.jsx("br",{}),r.jsx("span",{className:"text-primary-600",children:"Around Campus"})]}),r.jsx(o.E.p,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},className:"text-xl text-gray-600 mb-8 max-w-3xl mx-auto",children:"Connect with your classmates and see their real-time locations on detailed SYK campus maps. Never miss a study session or lunch meetup again."}),(0,r.jsxs)(o.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.3},className:"flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-4",children:[(0,r.jsxs)(y.z,{size:"lg",onClick:l,className:"w-full sm:w-auto",children:["Get Started Free",r.jsx(p,{className:"ml-2 w-5 h-5"})]}),(0,r.jsxs)(y.z,{variant:"outline",size:"lg",onClick:()=>(0,a.signIn)("google"),className:"w-full sm:w-auto",children:[r.jsx(h.Z,{className:"mr-2 w-5 h-5"}),"Sign in with Google"]})]})]}),r.jsx(o.E.div,{initial:{opacity:0,y:40},animate:{opacity:1,y:0},transition:{delay:.4},className:"max-w-4xl mx-auto mt-16",children:r.jsx("div",{className:"relative",children:r.jsx("div",{className:"bg-white rounded-2xl shadow-2xl p-8 border",children:r.jsx("div",{className:"aspect-video bg-gradient-to-br from-primary-100 to-secondary-100 rounded-lg flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[r.jsx(c.Z,{className:"w-16 h-16 text-primary-600 mx-auto mb-4"}),r.jsx("p",{className:"text-lg font-medium text-gray-700",children:"Interactive Campus Map"}),r.jsx("p",{className:"text-sm text-gray-500 mt-2",children:"Coming Soon"})]})})})})})]}),r.jsx("section",{className:"px-4 py-20 bg-white",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,r.jsxs)(o.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},className:"text-center mb-16",children:[r.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:"Everything You Need"}),r.jsx("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto",children:"Powerful features designed to help you stay connected with your campus community."})]}),r.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8",children:x.map((e,t)=>(0,r.jsxs)(o.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{delay:.1*t},className:"text-center",children:[r.jsx("div",{className:"w-16 h-16 bg-primary-100 rounded-2xl flex items-center justify-center mx-auto mb-6",children:r.jsx(e.icon,{className:"w-8 h-8 text-primary-600"})}),r.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-3",children:e.title}),r.jsx("p",{className:"text-gray-600",children:e.description})]},e.title))})]})}),r.jsx("section",{className:"px-4 py-20 bg-primary-600",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[r.jsx(o.E.h2,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},className:"text-3xl md:text-4xl font-bold text-white mb-6",children:"Ready to Connect?"}),r.jsx(o.E.p,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{delay:.1},className:"text-xl text-primary-100 mb-8",children:"Join your classmates and start exploring SYK campus together."}),r.jsx(o.E.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},viewport:{once:!0},transition:{delay:.2},children:(0,r.jsxs)(y.z,{size:"lg",variant:"secondary",onClick:l,className:"bg-white text-primary-600 hover:bg-gray-50",children:["Get Started Now",r.jsx(p,{className:"ml-2 w-5 h-5"})]})})]})}),r.jsx("footer",{className:"px-4 py-8 bg-gray-900",children:r.jsx("div",{className:"max-w-7xl mx-auto text-center",children:r.jsx("p",{className:"text-gray-400",children:"\xa9 2024 SYK SchoolFinder. Built for the SYK community."})})}),r.jsx(P,{isOpen:e,onClose:()=>t(!1),mode:s,onModeChange:i})]})}function q(){let{data:e,status:t}=(0,a.useSession)(),s=(0,i.useRouter)();return((0,n.useEffect)(()=>{"authenticated"===t&&e&&s.push("/dashboard")},[t,e,s]),"loading"===t||"authenticated"===t)?r.jsx("div",{className:"min-h-screen flex items-center justify-center",children:r.jsx(l.T,{size:"lg"})}):r.jsx(S,{})}},23511:(e,t,s)=>{"use strict";s.d(t,{z:()=>n});var r=s(95344),a=s(3729);let i=(e="default",t="default",s)=>{let r={default:"bg-primary-600 text-white hover:bg-primary-700",destructive:"bg-red-600 text-white hover:bg-red-700",outline:"border border-gray-300 bg-white hover:bg-gray-50 text-gray-700",secondary:"bg-secondary-600 text-white hover:bg-secondary-700",ghost:"hover:bg-gray-100 text-gray-700",link:"underline-offset-4 hover:underline text-primary-600"},a={default:"h-10 py-2 px-4",sm:"h-9 px-3 rounded-md",lg:"h-11 px-8 rounded-lg",icon:"h-10 w-10"};return["inline-flex items-center justify-center rounded-lg text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",r[e]||r.default,a[t]||a.default,s].filter(Boolean).join(" ")},n=(0,a.forwardRef)(({className:e,variant:t="default",size:s="default",...a},n)=>r.jsx("button",{className:i(t,s,e),ref:n,...a}));n.displayName="Button"},95252:(e,t,s)=>{"use strict";s.d(t,{T:()=>i});var r=s(95344),a=s(91626);function i({size:e="md",className:t,color:s="primary"}){return r.jsx("div",{className:(0,a.cn)("flex items-center justify-center",t),children:r.jsx("div",{className:(0,a.cn)("animate-spin rounded-full border-2 border-current border-t-transparent",{sm:"w-4 h-4",md:"w-6 h-6",lg:"w-8 h-8"}[e],{primary:"text-primary-600",secondary:"text-secondary-600",white:"text-white"}[s]),role:"status","aria-label":"Loading",children:r.jsx("span",{className:"sr-only",children:"Loading..."})})})}},91626:(e,t,s)=>{"use strict";s.d(t,{Qm:()=>c,cn:()=>i,oH:()=>l,pi:()=>n,uo:()=>o});var r=s(56815),a=s(79377);function i(...e){return(0,a.m6)((0,r.W)(e))}function n(e){let t=new Date,s=new Date(e),r=Math.floor((t.getTime()-s.getTime())/1e3);if(r<60)return"Just now";let a=Math.floor(r/60);if(a<60)return`${a}m ago`;let i=Math.floor(a/60);if(i<24)return`${i}h ago`;let n=Math.floor(i/24);return n<7?`${n}d ago`:new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})}function l(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}function o(e){let t=[];return e.length<8&&t.push("Password must be at least 8 characters long"),/[A-Z]/.test(e)||t.push("Password must contain at least one uppercase letter"),/[a-z]/.test(e)||t.push("Password must contain at least one lowercase letter"),/\d/.test(e)||t.push("Password must contain at least one number"),{isValid:0===t.length,errors:t}}function c(e){return e.split(" ").map(e=>e.charAt(0).toUpperCase()).join("").slice(0,2)}},70751:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>i,__esModule:()=>a,default:()=>n});let r=(0,s(86843).createProxy)(String.raw`C:\Users\<USER>\OneDrive - Suomalaisen Yhteiskoulun Osakeyhtiö\Desktop\Coding\schoolfinder\app\page.tsx`),{__esModule:a,$$typeof:i}=r,n=r.default}};var t=require("../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[225,340,129,978],()=>s(27789));module.exports=r})();