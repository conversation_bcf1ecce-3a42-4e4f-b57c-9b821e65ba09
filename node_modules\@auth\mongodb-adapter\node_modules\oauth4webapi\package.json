{"name": "oauth4webapi", "version": "3.7.0", "description": "Low-Level OAuth 2 / OpenID Connect Client API for JavaScript Runtimes", "keywords": ["access token", "auth", "authentication", "authorization", "basic", "browser", "bun", "certified", "ciba", "client", "cloudflare", "deno", "edge", "electron", "fapi", "javascript", "jwt", "netlify", "next", "nextjs", "node", "nodejs", "o<PERSON>h", "oauth2", "oidc", "openid-connect", "openid", "vercel", "workerd", "workers"], "homepage": "https://github.com/panva/oauth4webapi", "repository": "panva/oauth4webapi", "funding": {"url": "https://github.com/sponsors/panva"}, "license": "MIT", "author": "<PERSON><PERSON> <<EMAIL>>", "sideEffects": false, "type": "module", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "main": "./build/index.js", "types": "./build/index.d.ts", "files": ["build/index.js", "build/index.js.map", "build/index.d.ts"], "scripts": {"_format": "find src test tap examples conformance -type f -name '*.ts' -o -name '*.mjs' -o -name '*.cjs' | xargs prettier", "build": "npm run generate-build && tsc -p test && tsc -p examples && tsc -p conformance && tsc -p tap && npx --yes jsr publish --dry-run --allow-dirty", "conformance": "bash -c 'source .node_flags.sh && ava --config conformance/ava.config.ts'", "docs": "patch-package && typedoc", "docs-check": "./check-examples.sh", "format": "npm run _format -- --check --write", "format-check": "npm run _format -- --check", "generate-build": "rm -rf build && tsc --sourceMap && tsc --declaration true --emitDeclarationOnly true --removeComments false", "tap:browsers": "./tap/.browsers.sh", "tap:bun": "./tap/.bun.sh", "tap:deno": "./tap/.deno.sh", "tap:edge-runtime": "./tap/.edge-runtime.sh", "tap:electron": "./tap/.electron.sh", "tap:node": "bash -c './tap/.node.sh'", "tap:workerd": "./tap/.workerd.sh", "test": "bash -c 'source .node_flags.sh && ava'"}, "devDependencies": {"@koa/cors": "^5.0.0", "@types/koa__cors": "^5.0.0", "@types/node": "^22.16.5", "@types/qunit": "^2.19.12", "archiver": "^7.0.1", "ava": "^6.4.1", "esbuild": "^0.25.8", "happy-dom": "^18.0.1", "http-cookie-agent": "^7.0.2", "jose": "^6.0.12", "oidc-provider": "^9.4.0", "patch-package": "^8.0.0", "prettier": "^3.6.2", "prettier-plugin-jsdoc": "^1.3.3", "qunit": "^2.24.1", "raw-body": "^3.0.0", "selfsigned": "^3.0.1", "timekeeper": "^2.3.1", "tough-cookie": "^5.1.2", "tsx": "^4.20.3", "typedoc": "0.27.9", "typedoc-plugin-markdown": "4.3.1", "typedoc-plugin-mdn-links": "4.0.3", "typescript": "^5.8.3", "undici": "^7.12.0"}}