'use client'

import { useEffect, useState } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { MapPin, Users, Settings, Menu, X } from 'lucide-react'
import { useAuthStore } from '@/store/authStore.demo'
import { useLocationStore } from '@/store/locationStore.demo'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'
import { Button } from '@/components/ui/Button'
import { InteractiveMap } from '@/components/map/InteractiveMap'
import { FriendsList } from '@/components/friends/FriendsList'
import { LocationPermissionModal } from '@/components/location/LocationPermissionModal'
import { Sidebar } from '@/components/layout/Sidebar'
import { TopBar } from '@/components/layout/TopBar'

export default function Dashboard() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const { user, isAuthenticated } = useAuthStore()
  const {
    isLocationEnabled,
    requestLocationPermission,
    getFriendLocations,
    currentLocation
  } = useLocationStore()

  const [showLocationModal, setShowLocationModal] = useState(false)
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [activeView, setActiveView] = useState<'map' | 'friends' | 'settings'>('map')

  // Redirect if not authenticated
  useEffect(() => {
    if (status === 'unauthenticated' && !isAuthenticated) {
      router.push('/')
    }
  }, [status, isAuthenticated, router])

  // Request location permission on first load
  useEffect(() => {
    if ((session || user) && !isLocationEnabled) {
      setShowLocationModal(true)
    }
  }, [session, user, isLocationEnabled])

  // Load friend locations
  useEffect(() => {
    if (isLocationEnabled && (session || user)) {
      getFriendLocations()
    }
  }, [isLocationEnabled, session, user, getFriendLocations])

  const handleLocationPermission = async (granted: boolean) => {
    if (granted) {
      await requestLocationPermission()
    }
    setShowLocationModal(false)
  }

  if (status === 'loading' || (!session && !user)) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <LoadingSpinner size="lg" />
      </div>
    )
  }

  const currentUser = user || session?.user

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Sidebar */}
      <Sidebar
        isOpen={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
        activeView={activeView}
        onViewChange={setActiveView}
      />

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Top Bar */}
        <TopBar
          user={currentUser}
          onMenuClick={() => setSidebarOpen(true)}
          currentLocation={currentLocation}
        />

        {/* Content Area */}
        <main className="flex-1 relative overflow-hidden">
          {activeView === 'map' && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="h-full"
            >
              <InteractiveMap />
            </motion.div>
          )}

          {activeView === 'friends' && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              className="h-full p-4 overflow-y-auto"
            >
              <div className="max-w-4xl mx-auto">
                <div className="bg-white rounded-lg shadow-sm border p-6">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Friends</h2>
                  <FriendsList />
                </div>
              </div>
            </motion.div>
          )}

          {activeView === 'settings' && (
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              className="h-full p-4 overflow-y-auto"
            >
              <div className="max-w-4xl mx-auto">
                <div className="bg-white rounded-lg shadow-sm border p-6">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Settings</h2>
                  <div className="space-y-6">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900 mb-4">Privacy Settings</h3>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-gray-900">Share Location</p>
                            <p className="text-sm text-gray-500">Allow friends to see your location</p>
                          </div>
                          <Button variant="outline" size="sm">
                            {user?.privacySettings?.shareLocation ? 'Enabled' : 'Disabled'}
                          </Button>
                        </div>
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-gray-900">Invisible Mode</p>
                            <p className="text-sm text-gray-500">Hide from friends temporarily</p>
                          </div>
                          <Button variant="outline" size="sm">
                            {user?.privacySettings?.invisibleMode ? 'On' : 'Off'}
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </main>
      </div>

      {/* Location Permission Modal */}
      <LocationPermissionModal
        isOpen={showLocationModal}
        onClose={() => setShowLocationModal(false)}
        onPermissionResponse={handleLocationPermission}
      />

      {/* Mobile Sidebar Overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  )
}
