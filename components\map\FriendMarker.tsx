'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { MessageCircle, Navigation, Clock } from 'lucide-react'
import { FriendLocation } from '@/types'
import { getInitials, getTimeAgo } from '@/lib/utils'

interface FriendMarkerProps {
  friendLocation: FriendLocation
  currentFloor: number
}

export function FriendMarker({ friendLocation, currentFloor }: FriendMarkerProps) {
  const [showTooltip, setShowTooltip] = useState(false)
  const { friend, coordinates, timestamp } = friendLocation

  // Don't show marker if friend is on a different floor
  if (coordinates.floor !== currentFloor) {
    return null
  }

  return (
    <g>
      {/* Friend Marker */}
      <motion.g
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        whileHover={{ scale: 1.1 }}
        className="cursor-pointer"
        onMouseEnter={() => setShowTooltip(true)}
        onMouseLeave={() => setShowTooltip(false)}
      >
        {/* Outer ring for online status */}
        <circle
          cx={coordinates.x}
          cy={coordinates.y}
          r="12"
          fill={friend.isOnline ? '#22c55e' : '#6b7280'}
          opacity="0.3"
        />
        
        {/* Main marker */}
        <circle
          cx={coordinates.x}
          cy={coordinates.y}
          r="8"
          fill="#ffffff"
          stroke={friend.isOnline ? '#22c55e' : '#6b7280'}
          strokeWidth="2"
        />

        {/* Friend Avatar/Initial */}
        {friend.image || friend.avatar ? (
          <image
            x={coordinates.x - 6}
            y={coordinates.y - 6}
            width="12"
            height="12"
            href={friend.image || friend.avatar}
            clipPath="circle(6px at 6px 6px)"
          />
        ) : (
          <text
            x={coordinates.x}
            y={coordinates.y}
            textAnchor="middle"
            dominantBaseline="middle"
            className="text-xs font-medium fill-gray-700"
          >
            {getInitials(friend.name)}
          </text>
        )}

        {/* Online indicator */}
        {friend.isOnline && (
          <circle
            cx={coordinates.x + 6}
            cy={coordinates.y - 6}
            r="3"
            fill="#22c55e"
            stroke="#ffffff"
            strokeWidth="1"
          />
        )}
      </motion.g>

      {/* Tooltip */}
      <AnimatePresence>
        {showTooltip && (
          <motion.g
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
          >
            <foreignObject
              x={coordinates.x - 75}
              y={coordinates.y - 80}
              width="150"
              height="60"
            >
              <div className="bg-gray-900 text-white rounded-lg p-3 shadow-lg text-sm">
                <div className="flex items-center space-x-2 mb-1">
                  <div className="w-6 h-6 rounded-full bg-gray-700 flex items-center justify-center">
                    {friend.image || friend.avatar ? (
                      <img
                        src={friend.image || friend.avatar}
                        alt={friend.name}
                        className="w-6 h-6 rounded-full object-cover"
                      />
                    ) : (
                      <span className="text-xs font-medium text-white">
                        {getInitials(friend.name)}
                      </span>
                    )}
                  </div>
                  <span className="font-medium">{friend.name}</span>
                  {friend.isOnline && (
                    <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  )}
                </div>
                
                <div className="flex items-center space-x-1 text-xs text-gray-300">
                  <Clock className="w-3 h-3" />
                  <span>{getTimeAgo(timestamp)}</span>
                </div>

                <div className="flex items-center space-x-2 mt-2">
                  <button className="flex items-center space-x-1 text-xs hover:text-blue-300">
                    <MessageCircle className="w-3 h-3" />
                    <span>Message</span>
                  </button>
                  <button className="flex items-center space-x-1 text-xs hover:text-blue-300">
                    <Navigation className="w-3 h-3" />
                    <span>Navigate</span>
                  </button>
                </div>
              </div>
            </foreignObject>
          </motion.g>
        )}
      </AnimatePresence>
    </g>
  )
}
