{"name": "@auth/mongodb-adapter", "version": "3.10.0", "description": "MongoDB adapter for Auth.js", "homepage": "https://authjs.dev", "repository": "https://github.com/nextauthjs/next-auth", "bugs": {"url": "https://github.com/nextauthjs/next-auth/issues"}, "author": "Balá<PERSON>s <PERSON> <<EMAIL>>", "type": "module", "types": "./index.d.ts", "files": ["*.js", "*.d.ts*", "src"], "exports": {".": {"types": "./index.d.ts", "import": "./index.js"}}, "license": "ISC", "keywords": ["next-auth", "next.js", "o<PERSON>h", "mongodb", "adapter"], "private": false, "publishConfig": {"access": "public"}, "dependencies": {"@auth/core": "0.40.0"}, "peerDependencies": {"mongodb": "^6"}, "devDependencies": {"mongodb": "^6.0.0"}, "scripts": {"test": "./test/test.sh", "build": "tsc", "clean": "rm -rf *.js *.d.ts*"}}