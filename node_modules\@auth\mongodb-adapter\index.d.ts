/**
 * <div style={{display: "flex", justifyContent: "space-between", alignItems: "center", padding: 16}}>
 *  <p>Official <a href="https://www.mongodb.com">MongoDB</a> adapter for Auth.js / NextAuth.js.</p>
 *  <a href="https://www.mongodb.com">
 *   <img style={{display: "block"}} src="https://authjs.dev/img/adapters/mongodb.svg" width="30" />
 *  </a>
 * </div>
 *
 * ## Installation
 *
 * ```bash npm2yarn
 * npm install @auth/mongodb-adapter mongodb
 * ```
 *
 * @module @auth/mongodb-adapter
 */
import { ObjectId } from "mongodb";
import type { Adapter } from "@auth/core/adapters";
import type { MongoClient } from "mongodb";
/** This is the interface of the MongoDB adapter options. */
export interface MongoDBAdapterOptions {
    /**
     * The name of the {@link https://www.mongodb.com/docs/manual/core/databases-and-collections/#collections MongoDB collections}.
     */
    collections?: {
        Users?: string;
        Accounts?: string;
        Sessions?: string;
        VerificationTokens?: string;
    };
    /**
     * The name you want to give to the MongoDB database
     */
    databaseName?: string;
    /**
     * Callback function for managing the closing of the MongoDB client.
     * This could be useful when `client` is provided as a function returning MongoClient.
     * It allows for more customized management of database connections,
     * addressing persistence, container reuse, and connection closure issues.
     */
    onClose?: (client: MongoClient) => Promise<void>;
}
export declare const defaultCollections: Required<Required<MongoDBAdapterOptions>["collections"]>;
export declare const format: {
    /** Takes a MongoDB object and returns a plain old JavaScript object */
    from<T = Record<string, unknown>>(object: Record<string, any>): T;
    /** Takes a plain old JavaScript object and turns it into a MongoDB object */
    to<T_1 = Record<string, unknown>>(object: Record<string, any>): T_1 & {
        _id: ObjectId;
    };
};
export declare function MongoDBAdapter(
/**
 * The MongoDB client.
 *
 * The MongoDB team recommends providing a non-connected `MongoClient` instance to avoid unhandled promise rejections if the client fails to connect.
 *
 * Alternatively, you can also pass:
 * - A promise that resolves to a connected `MongoClient` (not recommended).
 * - A function, to handle more complex and custom connection strategies.
 *
 * Using a function combined with `options.onClose`, can be useful when you want a more advanced and customized connection strategy to address challenges related to persistence, container reuse, and connection closure.
 */
client: MongoClient | Promise<MongoClient> | (() => MongoClient | Promise<MongoClient>), options?: MongoDBAdapterOptions): Adapter;
//# sourceMappingURL=index.d.ts.map