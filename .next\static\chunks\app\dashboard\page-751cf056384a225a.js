(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[702],{662:function(e,s,t){Promise.resolve().then(t.bind(t,1595))},1595:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return X}});var a=t(7437),i=t(2265),r=t(2749),n=t(4033),l=t(5251),c=t(7700),o=t(4642),d=t(7319),x=t(9313),m=t(6142),h=t(5750),u=t(2898);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let y=(0,u.Z)("ZoomIn",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"11",x2:"11",y1:"8",y2:"14",key:"1vmskp"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]),g=(0,u.Z)("ZoomOut",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]),p=(0,u.Z)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]);var f=t(9670),j=t(7216);function v(e){let{onZoomIn:s,onZoomOut:t,onReset:i,showFriends:r,onToggleFriends:n}=e;return(0,a.jsxs)("div",{className:"absolute bottom-4 right-4 flex flex-col space-y-2",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border overflow-hidden",children:[(0,a.jsx)(x.z,{variant:"ghost",size:"icon",onClick:s,className:"rounded-none border-b",title:"Zoom In",children:(0,a.jsx)(y,{className:"w-4 h-4"})}),(0,a.jsx)(x.z,{variant:"ghost",size:"icon",onClick:t,className:"rounded-none",title:"Zoom Out",children:(0,a.jsx)(g,{className:"w-4 h-4"})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border overflow-hidden",children:[(0,a.jsx)(x.z,{variant:"ghost",size:"icon",onClick:i,className:"rounded-none border-b",title:"Reset View",children:(0,a.jsx)(p,{className:"w-4 h-4"})}),(0,a.jsx)(x.z,{variant:"ghost",size:"icon",onClick:n,className:"rounded-none ".concat(r?"bg-primary-50 text-primary-600":""),title:r?"Hide Friends":"Show Friends",children:r?(0,a.jsx)(f.Z,{className:"w-4 h-4"}):(0,a.jsx)(j.Z,{className:"w-4 h-4"})})]})]})}function N(e){let{currentFloor:s,floors:t,onFloorChange:i}=e;return(0,a.jsx)("div",{className:"absolute top-4 right-4 bg-white rounded-lg shadow-sm border p-2",children:(0,a.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,a.jsx)("span",{className:"text-xs font-medium text-gray-500 text-center mb-1",children:"Floor"}),t.map(e=>(0,a.jsx)(l.E.div,{whileTap:{scale:.95},children:(0,a.jsx)(x.z,{variant:s===e?"default":"ghost",size:"sm",onClick:()=>i(e),className:"w-12 h-8 text-sm font-medium ".concat(s===e?"bg-primary-600 text-white":"text-gray-600 hover:bg-gray-100"),children:e})},e))]})})}var b=t(2167);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let w=(0,u.Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]),k=(0,u.Z)("MessageCircle",[["path",{d:"m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z",key:"v2veuj"}]]),L=(0,u.Z)("Navigation",[["polygon",{points:"3 11 22 2 13 21 11 13 3 11",key:"1ltx0t"}]]);var z=t(1657);function S(e){let{friendLocation:s,currentFloor:t}=e,[r,n]=(0,i.useState)(!1),{friend:c,coordinates:o,timestamp:d}=s;return o.floor!==t?null:(0,a.jsxs)("g",{children:[(0,a.jsxs)(l.E.g,{initial:{scale:0},animate:{scale:1},whileHover:{scale:1.1},className:"cursor-pointer",onMouseEnter:()=>n(!0),onMouseLeave:()=>n(!1),children:[(0,a.jsx)("circle",{cx:o.x,cy:o.y,r:"12",fill:c.isOnline?"#22c55e":"#6b7280",opacity:"0.3"}),(0,a.jsx)("circle",{cx:o.x,cy:o.y,r:"8",fill:"#ffffff",stroke:c.isOnline?"#22c55e":"#6b7280",strokeWidth:"2"}),c.image||c.avatar?(0,a.jsx)("image",{x:o.x-6,y:o.y-6,width:"12",height:"12",href:c.image||c.avatar,clipPath:"circle(6px at 6px 6px)"}):(0,a.jsx)("text",{x:o.x,y:o.y,textAnchor:"middle",dominantBaseline:"middle",className:"text-xs font-medium fill-gray-700",children:(0,z.Qm)(c.name)}),c.isOnline&&(0,a.jsx)("circle",{cx:o.x+6,cy:o.y-6,r:"3",fill:"#22c55e",stroke:"#ffffff",strokeWidth:"1"})]}),(0,a.jsx)(b.M,{children:r&&(0,a.jsx)(l.E.g,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:10},children:(0,a.jsx)("foreignObject",{x:o.x-75,y:o.y-80,width:"150",height:"60",children:(0,a.jsxs)("div",{className:"bg-gray-900 text-white rounded-lg p-3 shadow-lg text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[(0,a.jsx)("div",{className:"w-6 h-6 rounded-full bg-gray-700 flex items-center justify-center",children:c.image||c.avatar?(0,a.jsx)("img",{src:c.image||c.avatar,alt:c.name,className:"w-6 h-6 rounded-full object-cover"}):(0,a.jsx)("span",{className:"text-xs font-medium text-white",children:(0,z.Qm)(c.name)})}),(0,a.jsx)("span",{className:"font-medium",children:c.name}),c.isOnline&&(0,a.jsx)("div",{className:"w-2 h-2 bg-green-400 rounded-full"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1 text-xs text-gray-300",children:[(0,a.jsx)(w,{className:"w-3 h-3"}),(0,a.jsx)("span",{children:(0,z.pi)(d)})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-2",children:[(0,a.jsxs)("button",{className:"flex items-center space-x-1 text-xs hover:text-blue-300",children:[(0,a.jsx)(k,{className:"w-3 h-3"}),(0,a.jsx)("span",{children:"Message"})]}),(0,a.jsxs)("button",{className:"flex items-center space-x-1 text-xs hover:text-blue-300",children:[(0,a.jsx)(L,{className:"w-3 h-3"}),(0,a.jsx)("span",{children:"Navigate"})]})]})]})})})})]})}function C(){let e=(0,i.useRef)(null),s=(0,i.useRef)(null),{friendLocations:t,currentLocation:r,mapViewState:n,setMapViewState:l}=(0,o.o)(),{user:d}=(0,c.t)(),[x,u]=(0,i.useState)(!1),[y,g]=(0,i.useState)({x:0,y:0}),[p,f]=(0,i.useState)(!0),j=()=>{u(!1)},b={width:800,height:600,rooms:[{id:"101",name:"Classroom 101",x:50,y:50,width:100,height:80},{id:"102",name:"Classroom 102",x:200,y:50,width:100,height:80},{id:"103",name:"Library",x:350,y:50,width:150,height:120},{id:"104",name:"Cafeteria",x:50,y:200,width:200,height:100},{id:"105",name:"Gym",x:300,y:250,width:180,height:150}]};return(0,a.jsxs)("div",{className:"relative h-full bg-gray-100 overflow-hidden",children:[(0,a.jsx)("div",{ref:e,className:"w-full h-full cursor-grab active:cursor-grabbing",onMouseDown:e=>{u(!0),g({x:e.clientX,y:e.clientY})},onMouseMove:e=>{if(!x)return;let s=e.clientX-y.x,t=e.clientY-y.y;l({center:{x:n.center.x-s/n.zoom,y:n.center.y-t/n.zoom}}),g({x:e.clientX,y:e.clientY})},onMouseUp:j,onMouseLeave:j,onWheel:e=>{e.preventDefault();let s=e.deltaY>0?.9:1.1;l({zoom:Math.max(.5,Math.min(3,n.zoom*s))})},children:(0,a.jsxs)("svg",{ref:s,className:"w-full h-full",viewBox:"".concat(n.center.x-400/n.zoom," ").concat(n.center.y-300/n.zoom," ").concat(800/n.zoom," ").concat(600/n.zoom),style:{transform:"scale(".concat(n.zoom,")"),transformOrigin:"center"},children:[(0,a.jsx)("rect",{x:"0",y:"0",width:b.width,height:b.height,fill:"#f8fafc",stroke:"#e2e8f0",strokeWidth:"2"}),(0,a.jsx)("defs",{children:(0,a.jsx)("pattern",{id:"grid",width:"50",height:"50",patternUnits:"userSpaceOnUse",children:(0,a.jsx)("path",{d:"M 50 0 L 0 0 0 50",fill:"none",stroke:"#e2e8f0",strokeWidth:"1",opacity:"0.5"})})}),(0,a.jsx)("rect",{width:"100%",height:"100%",fill:"url(#grid)"}),b.rooms.map(e=>(0,a.jsxs)("g",{children:[(0,a.jsx)("rect",{x:e.x,y:e.y,width:e.width,height:e.height,fill:"#ffffff",stroke:"#cbd5e1",strokeWidth:"2",className:"hover:fill-blue-50 cursor-pointer transition-colors"}),(0,a.jsx)("text",{x:e.x+e.width/2,y:e.y+e.height/2,textAnchor:"middle",dominantBaseline:"middle",className:"text-sm font-medium fill-gray-700 pointer-events-none",children:e.name})]},e.id)),r&&(0,a.jsxs)("g",{children:[(0,a.jsx)("circle",{cx:r.coordinates.x,cy:r.coordinates.y,r:"8",fill:"#3b82f6",stroke:"#ffffff",strokeWidth:"3",className:"animate-pulse"}),(0,a.jsx)("circle",{cx:r.coordinates.x,cy:r.coordinates.y,r:"20",fill:"none",stroke:"#3b82f6",strokeWidth:"2",opacity:"0.3",className:"animate-ping"})]}),p&&t.map(e=>(0,a.jsx)(S,{friendLocation:e,currentFloor:n.currentFloor},e.friend.id))]})}),(0,a.jsx)(N,{currentFloor:n.currentFloor,floors:[1,2,3,4],onFloorChange:e=>{l({currentFloor:e})}}),(0,a.jsx)(v,{onZoomIn:()=>{l({zoom:Math.min(3,1.2*n.zoom)})},onZoomOut:()=>{l({zoom:Math.max(.5,.8*n.zoom)})},onReset:()=>{l({zoom:1,center:{x:0,y:0}})},showFriends:p,onToggleFriends:()=>f(!p)}),(0,a.jsxs)("div",{className:"absolute top-4 left-4 bg-white rounded-lg shadow-sm border p-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,a.jsx)(m.Z,{className:"w-4 h-4 text-primary-600"}),(0,a.jsxs)("span",{className:"font-medium",children:[n.currentBuilding.charAt(0).toUpperCase()+n.currentBuilding.slice(1)," Building"]}),(0,a.jsx)("span",{className:"text-gray-500",children:"•"}),(0,a.jsxs)("span",{className:"text-gray-600",children:["Floor ",n.currentFloor]})]}),t.length>0&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600 mt-1",children:[(0,a.jsx)(h.Z,{className:"w-4 h-4"}),(0,a.jsxs)("span",{children:[t.length," friends nearby"]})]})]}),(0,a.jsxs)("div",{className:"absolute bottom-4 left-4 bg-white rounded-lg shadow-sm border p-3",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-900 mb-2",children:"Legend"}),(0,a.jsxs)("div",{className:"space-y-2 text-xs",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-blue-500 rounded-full"}),(0,a.jsx)("span",{children:"Your location"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded-full"}),(0,a.jsx)("span",{children:"Friends"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-white border border-gray-300"}),(0,a.jsx)("span",{children:"Rooms"})]})]})]})]})}/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let M=(0,u.Z)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]),Z=(0,u.Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]),F=(0,u.Z)("MoreVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]]);var A=t(2549);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let D=(0,u.Z)("Link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]]),O=(0,u.Z)("QrCode",[["rect",{width:"5",height:"5",x:"3",y:"3",rx:"1",key:"1tu5fj"}],["rect",{width:"5",height:"5",x:"16",y:"3",rx:"1",key:"1v8r4q"}],["rect",{width:"5",height:"5",x:"3",y:"16",rx:"1",key:"1x03jg"}],["path",{d:"M21 16h-3a2 2 0 0 0-2 2v3",key:"177gqh"}],["path",{d:"M21 21v.01",key:"ents32"}],["path",{d:"M12 7v3a2 2 0 0 1-2 2H7",key:"8crl2c"}],["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M12 3h.01",key:"n36tog"}],["path",{d:"M12 16v.01",key:"133mhm"}],["path",{d:"M16 12h1",key:"1slzba"}],["path",{d:"M21 12v.01",key:"1lwtk9"}],["path",{d:"M12 21v-1",key:"1880an"}]]);var E=t(1295);function P(e){let{isOpen:s,onClose:t}=e,[r,n]=(0,i.useState)("search"),[c,o]=(0,i.useState)(""),[d,m]=(0,i.useState)([{id:"1",name:"John Doe",email:"<EMAIL>",image:null,mutualFriends:3},{id:"2",name:"Jane Smith",email:"<EMAIL>",image:null,mutualFriends:1}]),h=e=>{o(e)},u=e=>{console.log("Sending friend request to:",e)},y=()=>"".concat(window.location.origin,"/invite/user123");return s?(0,a.jsx)(b.M,{children:(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:[(0,a.jsx)(l.E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},onClick:t,className:"absolute inset-0 bg-black/50 backdrop-blur-sm"}),(0,a.jsxs)(l.E.div,{initial:{opacity:0,scale:.95,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.95,y:20},className:"relative w-full max-w-lg bg-white rounded-2xl shadow-2xl",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Add Friends"}),(0,a.jsx)("button",{onClick:t,className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,a.jsx)(A.Z,{className:"w-5 h-5"})})]}),(0,a.jsxs)("div",{className:"flex border-b",children:[(0,a.jsxs)("button",{onClick:()=>n("search"),className:"flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors ".concat("search"===r?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700"),children:[(0,a.jsx)(Z,{className:"w-4 h-4 inline mr-2"}),"Search"]}),(0,a.jsxs)("button",{onClick:()=>n("invite"),className:"flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors ".concat("invite"===r?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700"),children:[(0,a.jsx)(D,{className:"w-4 h-4 inline mr-2"}),"Invite Link"]}),(0,a.jsxs)("button",{onClick:()=>n("qr"),className:"flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors ".concat("qr"===r?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700"),children:[(0,a.jsx)(O,{className:"w-4 h-4 inline mr-2"}),"QR Code"]})]}),(0,a.jsxs)("div",{className:"p-6",children:["search"===r&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,a.jsx)("input",{type:"text",placeholder:"Search by name or email...",value:c,onChange:e=>h(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"})]}),(0,a.jsxs)("div",{className:"space-y-3 max-h-64 overflow-y-auto",children:[d.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center",children:e.image?(0,a.jsx)("img",{src:e.image,alt:e.name,className:"w-10 h-10 rounded-full object-cover"}):(0,a.jsx)("span",{className:"text-sm font-medium text-primary-600",children:(0,z.Qm)(e.name)})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:e.email}),e.mutualFriends>0&&(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[e.mutualFriends," mutual friends"]})]})]}),(0,a.jsxs)(x.z,{size:"sm",onClick:()=>u(e.id),children:[(0,a.jsx)(M,{className:"w-4 h-4 mr-1"}),"Add"]})]},e.id)),c&&0===d.length&&(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)(Z,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"No users found"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Try searching with a different name or email"})]})]})]}),"invite"===r&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(D,{className:"w-12 h-12 text-primary-600 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Share Your Invite Link"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"Send this link to friends so they can add you directly"})]}),(0,a.jsx)("div",{className:"bg-gray-50 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"text",value:y(),readOnly:!0,className:"flex-1 bg-white border border-gray-300 rounded-lg px-3 py-2 text-sm"}),(0,a.jsx)(x.z,{variant:"outline",onClick:()=>navigator.clipboard.writeText(y()),children:"Copy"})]})}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)(x.z,{variant:"outline",className:"flex-1",children:[(0,a.jsx)(E.Z,{className:"w-4 h-4 mr-2"}),"Email"]}),(0,a.jsx)(x.z,{variant:"outline",className:"flex-1",children:"Share"})]})]}),"qr"===r&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(O,{className:"w-12 h-12 text-primary-600 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"QR Code"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:"Let friends scan this code to add you instantly"})]}),(0,a.jsx)("div",{className:"bg-gray-100 rounded-lg p-8 flex items-center justify-center",children:(0,a.jsx)("div",{className:"w-48 h-48 bg-white rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center",children:(0,a.jsx)(O,{className:"w-16 h-16 text-gray-400"})})}),(0,a.jsx)("p",{className:"text-center text-sm text-gray-500",children:"QR code will be generated here"})]})]})]})]})}):null}function q(){let{user:e}=(0,c.t)(),{friendLocations:s}=(0,o.o)(),[t,r]=(0,i.useState)(""),[n,d]=(0,i.useState)(!1),[u,y]=(0,i.useState)("online"),[g,p]=(0,i.useState)([{id:"1",name:"Emma Johnson",email:"<EMAIL>",image:null,isOnline:!0,lastSeen:new Date(Date.now()-3e5)},{id:"2",name:"Alex Chen",email:"<EMAIL>",image:null,isOnline:!1,lastSeen:new Date(Date.now()-72e5)},{id:"3",name:"Sarah Wilson",email:"<EMAIL>",image:null,isOnline:!0,lastSeen:new Date(Date.now()-6e4)}]),f=g.filter(e=>{let s=e.name.toLowerCase().includes(t.toLowerCase())||e.email.toLowerCase().includes(t.toLowerCase()),a="all"===u||"online"===u&&e.isOnline;return s&&a}),j=g.filter(e=>e.isOnline).length,v=e=>s.find(s=>s.friend.id===e);return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Friends"}),(0,a.jsxs)("p",{className:"text-gray-600",children:[j," of ",g.length," friends online"]})]}),(0,a.jsxs)(x.z,{onClick:()=>d(!0),children:[(0,a.jsx)(M,{className:"w-4 h-4 mr-2"}),"Add Friend"]})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(Z,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),(0,a.jsx)("input",{type:"text",placeholder:"Search friends...",value:t,onChange:e=>r(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"})]}),(0,a.jsxs)("div",{className:"flex space-x-1 bg-gray-100 rounded-lg p-1",children:[(0,a.jsxs)("button",{onClick:()=>y("online"),className:"flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ".concat("online"===u?"bg-white text-gray-900 shadow-sm":"text-gray-600 hover:text-gray-900"),children:["Online (",j,")"]}),(0,a.jsxs)("button",{onClick:()=>y("all"),className:"flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ".concat("all"===u?"bg-white text-gray-900 shadow-sm":"text-gray-600 hover:text-gray-900"),children:["All (",g.length,")"]})]}),(0,a.jsx)("div",{className:"space-y-3",children:0===f.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(h.Z,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:t?"No friends found":"No friends yet"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:t?"Try adjusting your search terms":"Start building your campus network by adding friends"}),!t&&(0,a.jsxs)(x.z,{onClick:()=>d(!0),children:[(0,a.jsx)(M,{className:"w-4 h-4 mr-2"}),"Add Your First Friend"]})]}):f.map((e,s)=>{let t=v(e.id);return(0,a.jsx)(l.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*s},className:"bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center",children:e.image?(0,a.jsx)("img",{src:e.image,alt:e.name,className:"w-12 h-12 rounded-full object-cover"}):(0,a.jsx)("span",{className:"text-lg font-medium text-primary-600",children:(0,z.Qm)(e.name)})}),(0,a.jsx)("div",{className:"absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white ".concat(e.isOnline?"bg-green-500":"bg-gray-400")})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-gray-900",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:e.email}),(0,a.jsx)("div",{className:"flex items-center space-x-2 mt-1",children:e.isOnline?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),(0,a.jsx)("span",{className:"text-xs text-green-600",children:"Online"}),t&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("span",{className:"text-xs text-gray-400",children:"•"}),(0,a.jsx)(m.Z,{className:"w-3 h-3 text-gray-400"}),(0,a.jsxs)("span",{className:"text-xs text-gray-600",children:[t.coordinates.building," Building, Floor ",t.coordinates.floor]})]})]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(w,{className:"w-3 h-3 text-gray-400"}),(0,a.jsxs)("span",{className:"text-xs text-gray-600",children:["Last seen ",(0,z.pi)(e.lastSeen)]})]})})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[t&&(0,a.jsxs)(x.z,{variant:"outline",size:"sm",children:[(0,a.jsx)(m.Z,{className:"w-4 h-4 mr-1"}),"Locate"]}),(0,a.jsxs)(x.z,{variant:"outline",size:"sm",children:[(0,a.jsx)(k,{className:"w-4 h-4 mr-1"}),"Message"]}),(0,a.jsx)(x.z,{variant:"ghost",size:"icon",children:(0,a.jsx)(F,{className:"w-4 h-4"})})]})]})},e.id)})}),(0,a.jsx)(P,{isOpen:n,onClose:()=>d(!1)})]})}var T=t(9036);function U(e){let{isOpen:s,onClose:t,onPermissionResponse:i}=e;return s?(0,a.jsx)(b.M,{children:(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:[(0,a.jsx)(l.E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"absolute inset-0 bg-black/50 backdrop-blur-sm"}),(0,a.jsxs)(l.E.div,{initial:{opacity:0,scale:.95,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.95,y:20},className:"relative w-full max-w-md bg-white rounded-2xl shadow-2xl",children:[(0,a.jsxs)("div",{className:"p-6 text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(m.Z,{className:"w-8 h-8 text-primary-600"})}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Enable Location Sharing"}),(0,a.jsx)("p",{className:"text-gray-600",children:"To help you find friends and navigate campus, we need access to your location."})]}),(0,a.jsx)("div",{className:"px-6 pb-6",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0",children:(0,a.jsx)(h.Z,{className:"w-4 h-4 text-green-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-gray-900",children:"Find Friends"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"See where your friends are on campus in real-time"})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0",children:(0,a.jsx)(m.Z,{className:"w-4 h-4 text-blue-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-gray-900",children:"Campus Navigation"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Get directions and navigate between buildings"})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0",children:(0,a.jsx)(T.Z,{className:"w-4 h-4 text-purple-600"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-gray-900",children:"Privacy Control"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"You can turn off location sharing anytime in settings"})]})]})]})}),(0,a.jsx)("div",{className:"px-6 pb-6",children:(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Your Privacy Matters"}),(0,a.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[(0,a.jsx)("li",{children:"• Your location is only shared with friends you approve"}),(0,a.jsx)("li",{children:"• You can go invisible or stop sharing anytime"}),(0,a.jsx)("li",{children:"• Location data is encrypted and secure"}),(0,a.jsx)("li",{children:"• We never share your data with third parties"})]})]})}),(0,a.jsxs)("div",{className:"px-6 pb-6 flex flex-col space-y-3",children:[(0,a.jsx)(x.z,{onClick:()=>{i(!0)},className:"w-full",size:"lg",children:"Allow Location Access"}),(0,a.jsx)(x.z,{onClick:()=>{i(!1)},variant:"outline",className:"w-full",size:"lg",children:"Not Now"})]}),(0,a.jsx)("button",{onClick:t,className:"absolute top-4 right-4 p-2 hover:bg-gray-100 rounded-lg transition-colors",children:(0,a.jsx)(A.Z,{className:"w-5 h-5 text-gray-500"})})]})]})}):null}/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let R=(0,u.Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),I=(0,u.Z)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]]),Y=(0,u.Z)("HelpCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),B=[{id:"map",label:"Campus Map",icon:m.Z,description:"Interactive campus map"},{id:"friends",label:"Friends",icon:h.Z,description:"Manage your friends"},{id:"settings",label:"Settings",icon:R,description:"App preferences"}],V=[{id:"notifications",label:"Notifications",icon:I,description:"Notification settings"},{id:"privacy",label:"Privacy",icon:T.Z,description:"Privacy controls"},{id:"help",label:"Help & Support",icon:Y,description:"Get help"}];function W(e){let{isOpen:s,onClose:t,activeView:i,onViewChange:r}=e,n=e=>{r(e),t()};return(0,a.jsx)(b.M,{children:s&&(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)(l.E.aside,{initial:{x:-300},animate:{x:0},exit:{x:-300},transition:{type:"spring",damping:25,stiffness:200},className:"fixed lg:relative inset-y-0 left-0 z-50 w-72 bg-white border-r border-gray-200 flex flex-col",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)(m.Z,{className:"w-5 h-5 text-white"})}),(0,a.jsx)("span",{className:"text-lg font-semibold text-gray-900",children:"Menu"})]}),(0,a.jsx)(x.z,{variant:"ghost",size:"icon",onClick:t,className:"lg:hidden",children:(0,a.jsx)(A.Z,{className:"w-5 h-5"})})]}),(0,a.jsxs)("nav",{className:"flex-1 p-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("h3",{className:"text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3",children:"Main"}),B.map(e=>{let s=e.icon,t=i===e.id;return(0,a.jsxs)("button",{onClick:()=>n(e.id),className:"w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-left transition-colors ".concat(t?"bg-primary-50 text-primary-700 border border-primary-200":"text-gray-700 hover:bg-gray-100"),children:[(0,a.jsx)(s,{className:"w-5 h-5 ".concat(t?"text-primary-600":"text-gray-500")}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:e.label}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:e.description})]})]},e.id)})]}),(0,a.jsxs)("div",{className:"mt-8 space-y-2",children:[(0,a.jsx)("h3",{className:"text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3",children:"More"}),V.map(e=>{let s=e.icon;return(0,a.jsxs)("button",{className:"w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-left text-gray-700 hover:bg-gray-100 transition-colors",children:[(0,a.jsx)(s,{className:"w-5 h-5 text-gray-500"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:e.label}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:e.description})]})]},e.id)})]})]}),(0,a.jsx)("div",{className:"p-4 border-t",children:(0,a.jsxs)("div",{className:"bg-primary-50 rounded-lg p-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,a.jsx)("div",{className:"w-6 h-6 bg-primary-600 rounded-full flex items-center justify-center",children:(0,a.jsx)(m.Z,{className:"w-3 h-3 text-white"})}),(0,a.jsx)("span",{className:"text-sm font-medium text-primary-900",children:"SYK SchoolFinder"})]}),(0,a.jsx)("p",{className:"text-xs text-primary-700",children:"Stay connected with your campus community"})]})})]})})})}/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let H=(0,u.Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]),Q=(0,u.Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);var J=t(7972);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let _=(0,u.Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);function G(e){let{user:s,onMenuClick:t,currentLocation:n}=e,[c,o]=(0,i.useState)(!1),[d,h]=(0,i.useState)(!1);return(0,a.jsxs)("header",{className:"bg-white border-b border-gray-200 px-4 py-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(x.z,{variant:"ghost",size:"icon",onClick:t,className:"lg:hidden",children:(0,a.jsx)(H,{className:"w-5 h-5"})}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)(m.Z,{className:"w-5 h-5 text-white"})}),(0,a.jsx)("span",{className:"text-xl font-bold text-gray-900 hidden sm:block",children:"SYK SchoolFinder"})]}),n&&(0,a.jsxs)("div",{className:"hidden md:flex items-center space-x-2 text-sm text-gray-600",children:[(0,a.jsx)(m.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:(()=>{if(!n)return"Location not available";let{building:e,floor:s}=n.coordinates;return"".concat(e.charAt(0).toUpperCase()+e.slice(1)," Building, Floor ").concat(s)})()})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(x.z,{variant:"ghost",size:"icon",className:"hidden sm:flex",children:(0,a.jsx)(Z,{className:"w-5 h-5"})}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)(x.z,{variant:"ghost",size:"icon",onClick:()=>h(!d),className:"relative",children:[(0,a.jsx)(I,{className:"w-5 h-5"}),(0,a.jsx)("span",{className:"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full text-xs text-white flex items-center justify-center",children:"2"})]}),(0,a.jsx)(b.M,{children:d&&(0,a.jsxs)(l.E.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},className:"absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border z-50",children:[(0,a.jsx)("div",{className:"p-4 border-b",children:(0,a.jsx)("h3",{className:"font-semibold text-gray-900",children:"Notifications"})}),(0,a.jsx)("div",{className:"max-h-64 overflow-y-auto",children:(0,a.jsx)("div",{className:"p-4 text-center text-gray-500",children:"No new notifications"})})]})})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("button",{onClick:()=>o(!c),className:"flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 transition-colors",children:[(0,a.jsx)("div",{className:"w-8 h-8 rounded-full bg-primary-100 flex items-center justify-center",children:(null==s?void 0:s.image)||(null==s?void 0:s.avatar)?(0,a.jsx)("img",{src:s.image||s.avatar,alt:s.name,className:"w-8 h-8 rounded-full object-cover"}):(0,a.jsx)("span",{className:"text-sm font-medium text-primary-600",children:(0,z.Qm)((null==s?void 0:s.name)||"User")})}),(0,a.jsxs)("div",{className:"hidden sm:block text-left",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:(null==s?void 0:s.name)||"User"}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:null==s?void 0:s.email})]}),(0,a.jsx)(Q,{className:"w-4 h-4 text-gray-400"})]}),(0,a.jsx)(b.M,{children:c&&(0,a.jsx)(l.E.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},className:"absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg border z-50",children:(0,a.jsxs)("div",{className:"p-2",children:[(0,a.jsxs)("div",{className:"px-3 py-2 border-b",children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:null==s?void 0:s.name}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:null==s?void 0:s.email})]}),(0,a.jsxs)("div",{className:"py-2",children:[(0,a.jsxs)("button",{className:"w-full flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md",children:[(0,a.jsx)(J.Z,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Profile"})]}),(0,a.jsxs)("button",{className:"w-full flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md",children:[(0,a.jsx)(R,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Settings"})]})]}),(0,a.jsx)("div",{className:"border-t pt-2",children:(0,a.jsxs)("button",{onClick:()=>{(0,r.signOut)({callbackUrl:"/"})},className:"w-full flex items-center space-x-2 px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md",children:[(0,a.jsx)(_,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Sign Out"})]})})]})})})]})]})]}),(c||d)&&(0,a.jsx)("div",{className:"fixed inset-0 z-40",onClick:()=>{o(!1),h(!1)}})]})}function X(){var e,s;let{data:t,status:m}=(0,r.useSession)(),h=(0,n.useRouter)(),{user:u,isAuthenticated:y}=(0,c.t)(),{isLocationEnabled:g,requestLocationPermission:p,getFriendLocations:f,currentLocation:j}=(0,o.o)(),[v,N]=(0,i.useState)(!1),[b,w]=(0,i.useState)(!1),[k,L]=(0,i.useState)("map");(0,i.useEffect)(()=>{"unauthenticated"!==m||y||h.push("/")},[m,y,h]),(0,i.useEffect)(()=>{(t||u)&&!g&&N(!0)},[t,u,g]),(0,i.useEffect)(()=>{g&&(t||u)&&f()},[g,t,u,f]);let z=async e=>{e&&await p(),N(!1)};if("loading"===m||!t&&!u)return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,a.jsx)(d.T,{size:"lg"})});let S=u||(null==t?void 0:t.user);return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 flex",children:[(0,a.jsx)(W,{isOpen:b,onClose:()=>w(!1),activeView:k,onViewChange:L}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,a.jsx)(G,{user:S,onMenuClick:()=>w(!0),currentLocation:j}),(0,a.jsxs)("main",{className:"flex-1 relative overflow-hidden",children:["map"===k&&(0,a.jsx)(l.E.div,{initial:{opacity:0},animate:{opacity:1},className:"h-full",children:(0,a.jsx)(C,{})}),"friends"===k&&(0,a.jsx)(l.E.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},className:"h-full p-4 overflow-y-auto",children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Friends"}),(0,a.jsx)(q,{})]})})}),"settings"===k&&(0,a.jsx)(l.E.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},className:"h-full p-4 overflow-y-auto",children:(0,a.jsx)("div",{className:"max-w-4xl mx-auto",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Settings"}),(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Privacy Settings"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:"Share Location"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Allow friends to see your location"})]}),(0,a.jsx)(x.z,{variant:"outline",size:"sm",children:(null==u?void 0:null===(e=u.privacySettings)||void 0===e?void 0:e.shareLocation)?"Enabled":"Disabled"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:"Invisible Mode"}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Hide from friends temporarily"})]}),(0,a.jsx)(x.z,{variant:"outline",size:"sm",children:(null==u?void 0:null===(s=u.privacySettings)||void 0===s?void 0:s.invisibleMode)?"On":"Off"})]})]})]})})]})})})]})]}),(0,a.jsx)(U,{isOpen:v,onClose:()=>N(!1),onPermissionResponse:z}),b&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black/50 z-40 lg:hidden",onClick:()=>w(!1)})]})}},9313:function(e,s,t){"use strict";t.d(s,{z:function(){return n}});var a=t(7437),i=t(2265);let r=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"default",s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"default",t=arguments.length>2?arguments[2]:void 0,a={default:"bg-primary-600 text-white hover:bg-primary-700",destructive:"bg-red-600 text-white hover:bg-red-700",outline:"border border-gray-300 bg-white hover:bg-gray-50 text-gray-700",secondary:"bg-secondary-600 text-white hover:bg-secondary-700",ghost:"hover:bg-gray-100 text-gray-700",link:"underline-offset-4 hover:underline text-primary-600"},i={default:"h-10 py-2 px-4",sm:"h-9 px-3 rounded-md",lg:"h-11 px-8 rounded-lg",icon:"h-10 w-10"};return["inline-flex items-center justify-center rounded-lg text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",a[e]||a.default,i[s]||i.default,t].filter(Boolean).join(" ")},n=(0,i.forwardRef)((e,s)=>{let{className:t,variant:i="default",size:n="default",...l}=e;return(0,a.jsx)("button",{className:r(i,n,t),ref:s,...l})});n.displayName="Button"},7319:function(e,s,t){"use strict";t.d(s,{T:function(){return r}});var a=t(7437),i=t(1657);function r(e){let{size:s="md",className:t,color:r="primary"}=e;return(0,a.jsx)("div",{className:(0,i.cn)("flex items-center justify-center",t),children:(0,a.jsx)("div",{className:(0,i.cn)("animate-spin rounded-full border-2 border-current border-t-transparent",{sm:"w-4 h-4",md:"w-6 h-6",lg:"w-8 h-8"}[s],{primary:"text-primary-600",secondary:"text-secondary-600",white:"text-white"}[r]),role:"status","aria-label":"Loading",children:(0,a.jsx)("span",{className:"sr-only",children:"Loading..."})})})}},1657:function(e,s,t){"use strict";t.d(s,{Qm:function(){return o},cn:function(){return r},oH:function(){return l},pi:function(){return n},uo:function(){return c}});var a=t(7042),i=t(4769);function r(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,i.m6)((0,a.W)(s))}function n(e){let s=new Date,t=new Date(e),a=Math.floor((s.getTime()-t.getTime())/1e3);if(a<60)return"Just now";let i=Math.floor(a/60);if(i<60)return"".concat(i,"m ago");let r=Math.floor(i/60);if(r<24)return"".concat(r,"h ago");let n=Math.floor(r/24);return n<7?"".concat(n,"d ago"):new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})}function l(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}function c(e){let s=[];return e.length<8&&s.push("Password must be at least 8 characters long"),/[A-Z]/.test(e)||s.push("Password must contain at least one uppercase letter"),/[a-z]/.test(e)||s.push("Password must contain at least one lowercase letter"),/\d/.test(e)||s.push("Password must contain at least one number"),{isValid:0===s.length,errors:s}}function o(e){return e.split(" ").map(e=>e.charAt(0).toUpperCase()).join("").slice(0,2)}},7700:function(e,s,t){"use strict";t.d(s,{t:function(){return r}});var a=t(4660),i=t(4810);let r=(0,a.Ue)()((0,i.tJ)((e,s)=>({user:null,isAuthenticated:!1,isLoading:!1,error:null,setUser:s=>{e({user:s,isAuthenticated:!!s,error:null})},setLoading:s=>{e({isLoading:s})},setError:s=>{e({error:s})},login:async(s,t)=>(e({isLoading:!0,error:null}),await new Promise(e=>setTimeout(e,1e3)),s&&t)?(e({user:{id:"1",email:s,name:s.split("@")[0],image:void 0,avatar:void 0,isOnline:!0,lastSeen:new Date,privacySettings:{shareLocation:!0,invisibleMode:!1,allowFriendRequests:!0,showOnlineStatus:!0},createdAt:new Date,updatedAt:new Date},isAuthenticated:!0,isLoading:!1,error:null}),!0):(e({error:"Please enter email and password",isLoading:!1}),!1),register:async(s,t,a)=>(e({isLoading:!0,error:null}),await new Promise(e=>setTimeout(e,1e3)),s&&t&&a)?(e({user:{id:"1",email:t,name:s,image:void 0,avatar:void 0,isOnline:!0,lastSeen:new Date,privacySettings:{shareLocation:!0,invisibleMode:!1,allowFriendRequests:!0,showOnlineStatus:!0},createdAt:new Date,updatedAt:new Date},isAuthenticated:!0,isLoading:!1,error:null}),!0):(e({error:"Please fill in all fields",isLoading:!1}),!1),logout:()=>{e({user:null,isAuthenticated:!1,error:null}),localStorage.removeItem("auth-storage")},updateProfile:async t=>{let{user:a}=s();return!!a&&(e({isLoading:!0,error:null}),await new Promise(e=>setTimeout(e,500)),e({user:{...a,...t},isLoading:!1,error:null}),!0)},updatePrivacySettings:async t=>{let{user:a}=s();return!!a&&(e({isLoading:!0,error:null}),await new Promise(e=>setTimeout(e,500)),e({user:{...a,privacySettings:t},isLoading:!1,error:null}),!0)},initialize:()=>{let s=localStorage.getItem("auth-storage");if(s)try{var t;let a=JSON.parse(s);(null===(t=a.state)||void 0===t?void 0:t.user)&&e({user:a.state.user,isAuthenticated:!0})}catch(e){console.error("Failed to restore auth state:",e)}}}),{name:"auth-storage",partialize:e=>({user:e.user,isAuthenticated:e.isAuthenticated})}))},4642:function(e,s,t){"use strict";t.d(s,{o:function(){return n}});var a=t(4660);let i={zoom:1,center:{x:0,y:0},currentFloor:1,currentBuilding:"main"},r=[{id:"1",userId:"2",coordinates:{x:150,y:100,floor:1,building:"main"},timestamp:new Date(Date.now()-3e5),isActive:!0,friend:{id:"2",email:"<EMAIL>",name:"Emma Johnson",image:void 0,avatar:void 0,isOnline:!0,lastSeen:new Date(Date.now()-12e4),privacySettings:{shareLocation:!0,invisibleMode:!1,allowFriendRequests:!0,showOnlineStatus:!0},createdAt:new Date,updatedAt:new Date}},{id:"2",userId:"3",coordinates:{x:400,y:200,floor:1,building:"main"},timestamp:new Date(Date.now()-6e4),isActive:!0,friend:{id:"3",email:"<EMAIL>",name:"Alex Chen",image:void 0,avatar:void 0,isOnline:!0,lastSeen:new Date(Date.now()-3e4),privacySettings:{shareLocation:!0,invisibleMode:!1,allowFriendRequests:!0,showOnlineStatus:!0},createdAt:new Date,updatedAt:new Date}}],n=(0,a.Ue)((e,s)=>({currentLocation:{id:"current",userId:"1",coordinates:{x:250,y:150,floor:1,building:"main"},timestamp:new Date,isActive:!0},friendLocations:r,mapViewState:i,isLocationEnabled:!0,isLoading:!1,error:null,lastUpdate:new Date,setCurrentLocation:s=>{e({currentLocation:s,lastUpdate:new Date,error:null})},setFriendLocations:s=>{e({friendLocations:s})},updateFriendLocation:t=>{let{friendLocations:a}=s(),i=a.findIndex(e=>e.friend.id===t.friend.id);if(i>=0){let s=[...a];s[i]=t,e({friendLocations:s})}else e({friendLocations:[...a,t]})},removeFriendLocation:t=>{let{friendLocations:a}=s();e({friendLocations:a.filter(e=>e.friend.id!==t)})},setMapViewState:t=>{let{mapViewState:a}=s();e({mapViewState:{...a,...t}})},setLocationEnabled:s=>{e({isLocationEnabled:s})},setLoading:s=>{e({isLoading:s})},setError:s=>{e({error:s})},requestLocationPermission:async()=>(e({isLoading:!0,error:null}),await new Promise(e=>setTimeout(e,1e3)),e({isLocationEnabled:!0,isLoading:!1,error:null}),!0),getCurrentPosition:async()=>(await new Promise(e=>setTimeout(e,500)),{coords:{latitude:60.1699,longitude:24.9384,accuracy:10,altitude:null,altitudeAccuracy:null,heading:null,speed:null},timestamp:Date.now()}),updateLocation:async s=>(e({isLoading:!0,error:null}),await new Promise(e=>setTimeout(e,500)),e({currentLocation:{id:"current",userId:"1",coordinates:s,timestamp:new Date,isActive:!0},isLoading:!1,lastUpdate:new Date,error:null}),!0),shareLocation:async s=>(e({isLoading:!0,error:null}),await new Promise(e=>setTimeout(e,500)),e({isLoading:!1,error:null}),!0),getFriendLocations:async()=>{e({isLoading:!0,error:null}),await new Promise(e=>setTimeout(e,500)),e({friendLocations:r,isLoading:!1,error:null})},initialize:()=>{e({isLocationEnabled:!0,friendLocations:r})}}))}},function(e){e.O(0,[375,679,971,938,744],function(){return e(e.s=662)}),_N_E=e.O()}]);