{"name": "syk-schoolfinder", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "server": "node server/index.js", "dev:server": "nodemon server/index.js", "dev:full": "concurrently \"npm run dev\" \"npm run dev:server\""}, "dependencies": {"@auth/mongodb-adapter": "^3.10.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "clsx": "^2.1.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "framer-motion": "^10.16.16", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.294.0", "mongodb": "^6.18.0", "mongoose": "^8.0.3", "multer": "^1.4.5-lts.1", "next": "14.0.4", "next-auth": "^4.24.5", "nodemailer": "^6.9.7", "postcss": "^8", "react": "^18", "react-dom": "^18", "react-hot-toast": "^2.4.1", "sharp": "^0.33.1", "socket.io": "^4.7.4", "socket.io-client": "^4.7.4", "tailwind-merge": "^3.3.1", "tailwindcss": "^3.3.0", "typescript": "^5", "zustand": "^4.4.7"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/nodemailer": "^6.4.14", "concurrently": "^8.2.2", "eslint": "^8", "eslint-config-next": "14.0.4", "nodemon": "^3.0.2"}}