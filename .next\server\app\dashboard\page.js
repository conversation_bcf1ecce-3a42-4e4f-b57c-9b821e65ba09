(()=>{var e={};e.id=702,e.ids=[702],e.modules={47849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},55403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},94749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},25528:e=>{"use strict";e.exports=require("next/dist\\client\\components\\action-async-storage.external.js")},91877:e=>{"use strict";e.exports=require("next/dist\\client\\components\\request-async-storage.external.js")},25319:e=>{"use strict";e.exports=require("next/dist\\client\\components\\static-generation-async-storage.external.js")},39491:e=>{"use strict";e.exports=require("assert")},14300:e=>{"use strict";e.exports=require("buffer")},32081:e=>{"use strict";e.exports=require("child_process")},6113:e=>{"use strict";e.exports=require("crypto")},82361:e=>{"use strict";e.exports=require("events")},57147:e=>{"use strict";e.exports=require("fs")},13685:e=>{"use strict";e.exports=require("http")},95687:e=>{"use strict";e.exports=require("https")},41808:e=>{"use strict";e.exports=require("net")},22037:e=>{"use strict";e.exports=require("os")},71017:e=>{"use strict";e.exports=require("path")},12781:e=>{"use strict";e.exports=require("stream")},24404:e=>{"use strict";e.exports=require("tls")},76224:e=>{"use strict";e.exports=require("tty")},57310:e=>{"use strict";e.exports=require("url")},73837:e=>{"use strict";e.exports=require("util")},59796:e=>{"use strict";e.exports=require("zlib")},97213:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>d});var a=t(50482),i=t(69108),r=t(62563),l=t.n(r),n=t(68300),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let d=["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,79865)),"C:\\Users\\<USER>\\OneDrive - Suomalaisen Yhteiskoulun Osakeyhti\xf6\\Desktop\\Coding\\schoolfinder\\app\\dashboard\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,78062)),"C:\\Users\\<USER>\\OneDrive - Suomalaisen Yhteiskoulun Osakeyhti\xf6\\Desktop\\Coding\\schoolfinder\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,69361,23)),"next/dist/client/components/not-found-error"]}],o=["C:\\Users\\<USER>\\OneDrive - Suomalaisen Yhteiskoulun Osakeyhti\xf6\\Desktop\\Coding\\schoolfinder\\app\\dashboard\\page.tsx"],x="/dashboard/page",m={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},79040:(e,s,t)=>{Promise.resolve().then(t.bind(t,86089))},86089:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>J});var a=t(95344),i=t(3729),r=t(47674),l=t(22254),n=t(57916),c=t(56413),d=t(92875),o=t(95252),x=t(23511),m=t(80508),h=t(89895),u=t(69224);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let y=(0,u.Z)("ZoomIn",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"11",x2:"11",y1:"8",y2:"14",key:"1vmskp"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]),p=(0,u.Z)("ZoomOut",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["line",{x1:"21",x2:"16.65",y1:"21",y2:"16.65",key:"13gj7c"}],["line",{x1:"8",x2:"14",y1:"11",y2:"11",key:"durymu"}]]),g=(0,u.Z)("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]);var j=t(53148),f=t(1222);function v({onZoomIn:e,onZoomOut:s,onReset:t,showFriends:i,onToggleFriends:r}){return(0,a.jsxs)("div",{className:"absolute bottom-4 right-4 flex flex-col space-y-2",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border overflow-hidden",children:[a.jsx(x.z,{variant:"ghost",size:"icon",onClick:e,className:"rounded-none border-b",title:"Zoom In",children:a.jsx(y,{className:"w-4 h-4"})}),a.jsx(x.z,{variant:"ghost",size:"icon",onClick:s,className:"rounded-none",title:"Zoom Out",children:a.jsx(p,{className:"w-4 h-4"})})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border overflow-hidden",children:[a.jsx(x.z,{variant:"ghost",size:"icon",onClick:t,className:"rounded-none border-b",title:"Reset View",children:a.jsx(g,{className:"w-4 h-4"})}),a.jsx(x.z,{variant:"ghost",size:"icon",onClick:r,className:`rounded-none ${i?"bg-primary-50 text-primary-600":""}`,title:i?"Hide Friends":"Show Friends",children:i?a.jsx(j.Z,{className:"w-4 h-4"}):a.jsx(f.Z,{className:"w-4 h-4"})})]})]})}function b({currentFloor:e,floors:s,onFloorChange:t}){return a.jsx("div",{className:"absolute top-4 right-4 bg-white rounded-lg shadow-sm border p-2",children:(0,a.jsxs)("div",{className:"flex flex-col space-y-1",children:[a.jsx("span",{className:"text-xs font-medium text-gray-500 text-center mb-1",children:"Floor"}),s.map(s=>a.jsx(n.E.div,{whileTap:{scale:.95},children:a.jsx(x.z,{variant:e===s?"default":"ghost",size:"sm",onClick:()=>t(s),className:`w-12 h-8 text-sm font-medium ${e===s?"bg-primary-600 text-white":"text-gray-600 hover:bg-gray-100"}`,children:s})},s))]})})}var N=t(73644);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let w=(0,u.Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]),k=(0,u.Z)("MessageCircle",[["path",{d:"m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z",key:"v2veuj"}]]),z=(0,u.Z)("Navigation",[["polygon",{points:"3 11 22 2 13 21 11 13 3 11",key:"1ltx0t"}]]);var C=t(91626);function M({friendLocation:e,currentFloor:s}){let[t,r]=(0,i.useState)(!1),{friend:l,coordinates:c,timestamp:d}=e;return c.floor!==s?null:(0,a.jsxs)("g",{children:[(0,a.jsxs)(n.E.g,{initial:{scale:0},animate:{scale:1},whileHover:{scale:1.1},className:"cursor-pointer",onMouseEnter:()=>r(!0),onMouseLeave:()=>r(!1),children:[a.jsx("circle",{cx:c.x,cy:c.y,r:"12",fill:l.isOnline?"#22c55e":"#6b7280",opacity:"0.3"}),a.jsx("circle",{cx:c.x,cy:c.y,r:"8",fill:"#ffffff",stroke:l.isOnline?"#22c55e":"#6b7280",strokeWidth:"2"}),l.image||l.avatar?a.jsx("image",{x:c.x-6,y:c.y-6,width:"12",height:"12",href:l.image||l.avatar,clipPath:"circle(6px at 6px 6px)"}):a.jsx("text",{x:c.x,y:c.y,textAnchor:"middle",dominantBaseline:"middle",className:"text-xs font-medium fill-gray-700",children:(0,C.Qm)(l.name)}),l.isOnline&&a.jsx("circle",{cx:c.x+6,cy:c.y-6,r:"3",fill:"#22c55e",stroke:"#ffffff",strokeWidth:"1"})]}),a.jsx(N.M,{children:t&&a.jsx(n.E.g,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:10},children:a.jsx("foreignObject",{x:c.x-75,y:c.y-80,width:"150",height:"60",children:(0,a.jsxs)("div",{className:"bg-gray-900 text-white rounded-lg p-3 shadow-lg text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-1",children:[a.jsx("div",{className:"w-6 h-6 rounded-full bg-gray-700 flex items-center justify-center",children:l.image||l.avatar?a.jsx("img",{src:l.image||l.avatar,alt:l.name,className:"w-6 h-6 rounded-full object-cover"}):a.jsx("span",{className:"text-xs font-medium text-white",children:(0,C.Qm)(l.name)})}),a.jsx("span",{className:"font-medium",children:l.name}),l.isOnline&&a.jsx("div",{className:"w-2 h-2 bg-green-400 rounded-full"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1 text-xs text-gray-300",children:[a.jsx(w,{className:"w-3 h-3"}),a.jsx("span",{children:(0,C.pi)(d)})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-2",children:[(0,a.jsxs)("button",{className:"flex items-center space-x-1 text-xs hover:text-blue-300",children:[a.jsx(k,{className:"w-3 h-3"}),a.jsx("span",{children:"Message"})]}),(0,a.jsxs)("button",{className:"flex items-center space-x-1 text-xs hover:text-blue-300",children:[a.jsx(z,{className:"w-3 h-3"}),a.jsx("span",{children:"Navigate"})]})]})]})})})})]})}function S(){let e=(0,i.useRef)(null),s=(0,i.useRef)(null),{friendLocations:t,currentLocation:r,mapViewState:l,setMapViewState:n}=(0,d.o)(),{user:o}=(0,c.t)(),[x,u]=(0,i.useState)(!1),[y,p]=(0,i.useState)({x:0,y:0}),[g,j]=(0,i.useState)(!0),f=()=>{u(!1)},N={width:800,height:600,rooms:[{id:"101",name:"Classroom 101",x:50,y:50,width:100,height:80},{id:"102",name:"Classroom 102",x:200,y:50,width:100,height:80},{id:"103",name:"Library",x:350,y:50,width:150,height:120},{id:"104",name:"Cafeteria",x:50,y:200,width:200,height:100},{id:"105",name:"Gym",x:300,y:250,width:180,height:150}]};return(0,a.jsxs)("div",{className:"relative h-full bg-gray-100 overflow-hidden",children:[a.jsx("div",{ref:e,className:"w-full h-full cursor-grab active:cursor-grabbing",onMouseDown:e=>{u(!0),p({x:e.clientX,y:e.clientY})},onMouseMove:e=>{if(!x)return;let s=e.clientX-y.x,t=e.clientY-y.y;n({center:{x:l.center.x-s/l.zoom,y:l.center.y-t/l.zoom}}),p({x:e.clientX,y:e.clientY})},onMouseUp:f,onMouseLeave:f,onWheel:e=>{e.preventDefault();let s=e.deltaY>0?.9:1.1;n({zoom:Math.max(.5,Math.min(3,l.zoom*s))})},children:(0,a.jsxs)("svg",{ref:s,className:"w-full h-full",viewBox:`${l.center.x-400/l.zoom} ${l.center.y-300/l.zoom} ${800/l.zoom} ${600/l.zoom}`,style:{transform:`scale(${l.zoom})`,transformOrigin:"center"},children:[a.jsx("rect",{x:"0",y:"0",width:N.width,height:N.height,fill:"#f8fafc",stroke:"#e2e8f0",strokeWidth:"2"}),a.jsx("defs",{children:a.jsx("pattern",{id:"grid",width:"50",height:"50",patternUnits:"userSpaceOnUse",children:a.jsx("path",{d:"M 50 0 L 0 0 0 50",fill:"none",stroke:"#e2e8f0",strokeWidth:"1",opacity:"0.5"})})}),a.jsx("rect",{width:"100%",height:"100%",fill:"url(#grid)"}),N.rooms.map(e=>(0,a.jsxs)("g",{children:[a.jsx("rect",{x:e.x,y:e.y,width:e.width,height:e.height,fill:"#ffffff",stroke:"#cbd5e1",strokeWidth:"2",className:"hover:fill-blue-50 cursor-pointer transition-colors"}),a.jsx("text",{x:e.x+e.width/2,y:e.y+e.height/2,textAnchor:"middle",dominantBaseline:"middle",className:"text-sm font-medium fill-gray-700 pointer-events-none",children:e.name})]},e.id)),r&&(0,a.jsxs)("g",{children:[a.jsx("circle",{cx:r.coordinates.x,cy:r.coordinates.y,r:"8",fill:"#3b82f6",stroke:"#ffffff",strokeWidth:"3",className:"animate-pulse"}),a.jsx("circle",{cx:r.coordinates.x,cy:r.coordinates.y,r:"20",fill:"none",stroke:"#3b82f6",strokeWidth:"2",opacity:"0.3",className:"animate-ping"})]}),g&&t.map(e=>a.jsx(M,{friendLocation:e,currentFloor:l.currentFloor},e.friend.id))]})}),a.jsx(b,{currentFloor:l.currentFloor,floors:[1,2,3,4],onFloorChange:e=>{n({currentFloor:e})}}),a.jsx(v,{onZoomIn:()=>{n({zoom:Math.min(3,1.2*l.zoom)})},onZoomOut:()=>{n({zoom:Math.max(.5,.8*l.zoom)})},onReset:()=>{n({zoom:1,center:{x:0,y:0}})},showFriends:g,onToggleFriends:()=>j(!g)}),(0,a.jsxs)("div",{className:"absolute top-4 left-4 bg-white rounded-lg shadow-sm border p-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[a.jsx(m.Z,{className:"w-4 h-4 text-primary-600"}),(0,a.jsxs)("span",{className:"font-medium",children:[l.currentBuilding.charAt(0).toUpperCase()+l.currentBuilding.slice(1)," Building"]}),a.jsx("span",{className:"text-gray-500",children:"•"}),(0,a.jsxs)("span",{className:"text-gray-600",children:["Floor ",l.currentFloor]})]}),t.length>0&&(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-600 mt-1",children:[a.jsx(h.Z,{className:"w-4 h-4"}),(0,a.jsxs)("span",{children:[t.length," friends nearby"]})]})]}),(0,a.jsxs)("div",{className:"absolute bottom-4 left-4 bg-white rounded-lg shadow-sm border p-3",children:[a.jsx("h3",{className:"text-sm font-medium text-gray-900 mb-2",children:"Legend"}),(0,a.jsxs)("div",{className:"space-y-2 text-xs",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("div",{className:"w-3 h-3 bg-blue-500 rounded-full"}),a.jsx("span",{children:"Your location"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("div",{className:"w-3 h-3 bg-green-500 rounded-full"}),a.jsx("span",{children:"Friends"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("div",{className:"w-3 h-3 bg-white border border-gray-300"}),a.jsx("span",{children:"Rooms"})]})]})]})]})}/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let Z=(0,u.Z)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]]),q=(0,u.Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]),O=(0,u.Z)("MoreVertical",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"12",cy:"5",r:"1",key:"gxeob9"}],["circle",{cx:"12",cy:"19",r:"1",key:"lyex9k"}]]);var F=t(14513);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let L=(0,u.Z)("Link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]]),P=(0,u.Z)("QrCode",[["rect",{width:"5",height:"5",x:"3",y:"3",rx:"1",key:"1tu5fj"}],["rect",{width:"5",height:"5",x:"16",y:"3",rx:"1",key:"1v8r4q"}],["rect",{width:"5",height:"5",x:"3",y:"16",rx:"1",key:"1x03jg"}],["path",{d:"M21 16h-3a2 2 0 0 0-2 2v3",key:"177gqh"}],["path",{d:"M21 21v.01",key:"ents32"}],["path",{d:"M12 7v3a2 2 0 0 1-2 2H7",key:"8crl2c"}],["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M12 3h.01",key:"n36tog"}],["path",{d:"M12 16v.01",key:"133mhm"}],["path",{d:"M16 12h1",key:"1slzba"}],["path",{d:"M21 12v.01",key:"1lwtk9"}],["path",{d:"M12 21v-1",key:"1880an"}]]);var E=t(71206);function $({isOpen:e,onClose:s}){let[t,r]=(0,i.useState)("search"),[l,c]=(0,i.useState)(""),[d,o]=(0,i.useState)([{id:"1",name:"John Doe",email:"<EMAIL>",image:null,mutualFriends:3},{id:"2",name:"Jane Smith",email:"<EMAIL>",image:null,mutualFriends:1}]),m=e=>{c(e)},h=e=>{console.log("Sending friend request to:",e)},u=()=>`${window.location.origin}/invite/user123`;return e?a.jsx(N.M,{children:(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:[a.jsx(n.E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},onClick:s,className:"absolute inset-0 bg-black/50 backdrop-blur-sm"}),(0,a.jsxs)(n.E.div,{initial:{opacity:0,scale:.95,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.95,y:20},className:"relative w-full max-w-lg bg-white rounded-2xl shadow-2xl",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-6 border-b",children:[a.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Add Friends"}),a.jsx("button",{onClick:s,className:"p-2 hover:bg-gray-100 rounded-lg transition-colors",children:a.jsx(F.Z,{className:"w-5 h-5"})})]}),(0,a.jsxs)("div",{className:"flex border-b",children:[(0,a.jsxs)("button",{onClick:()=>r("search"),className:`flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors ${"search"===t?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700"}`,children:[a.jsx(q,{className:"w-4 h-4 inline mr-2"}),"Search"]}),(0,a.jsxs)("button",{onClick:()=>r("invite"),className:`flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors ${"invite"===t?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700"}`,children:[a.jsx(L,{className:"w-4 h-4 inline mr-2"}),"Invite Link"]}),(0,a.jsxs)("button",{onClick:()=>r("qr"),className:`flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors ${"qr"===t?"border-primary-500 text-primary-600":"border-transparent text-gray-500 hover:text-gray-700"}`,children:[a.jsx(P,{className:"w-4 h-4 inline mr-2"}),"QR Code"]})]}),(0,a.jsxs)("div",{className:"p-6",children:["search"===t&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"relative",children:[a.jsx(q,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),a.jsx("input",{type:"text",placeholder:"Search by name or email...",value:l,onChange:e=>m(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"})]}),(0,a.jsxs)("div",{className:"space-y-3 max-h-64 overflow-y-auto",children:[d.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx("div",{className:"w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center",children:e.image?a.jsx("img",{src:e.image,alt:e.name,className:"w-10 h-10 rounded-full object-cover"}):a.jsx("span",{className:"text-sm font-medium text-primary-600",children:(0,C.Qm)(e.name)})}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium text-gray-900",children:e.name}),a.jsx("p",{className:"text-sm text-gray-600",children:e.email}),e.mutualFriends>0&&(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[e.mutualFriends," mutual friends"]})]})]}),(0,a.jsxs)(x.z,{size:"sm",onClick:()=>h(e.id),children:[a.jsx(Z,{className:"w-4 h-4 mr-1"}),"Add"]})]},e.id)),l&&0===d.length&&(0,a.jsxs)("div",{className:"text-center py-8",children:[a.jsx(q,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),a.jsx("p",{className:"text-gray-600",children:"No users found"}),a.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Try searching with a different name or email"})]})]})]}),"invite"===t&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[a.jsx(L,{className:"w-12 h-12 text-primary-600 mx-auto mb-4"}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Share Your Invite Link"}),a.jsx("p",{className:"text-gray-600 mb-6",children:"Send this link to friends so they can add you directly"})]}),a.jsx("div",{className:"bg-gray-50 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("input",{type:"text",value:u(),readOnly:!0,className:"flex-1 bg-white border border-gray-300 rounded-lg px-3 py-2 text-sm"}),a.jsx(x.z,{variant:"outline",onClick:()=>navigator.clipboard.writeText(u()),children:"Copy"})]})}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)(x.z,{variant:"outline",className:"flex-1",children:[a.jsx(E.Z,{className:"w-4 h-4 mr-2"}),"Email"]}),a.jsx(x.z,{variant:"outline",className:"flex-1",children:"Share"})]})]}),"qr"===t&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[a.jsx(P,{className:"w-12 h-12 text-primary-600 mx-auto mb-4"}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"QR Code"}),a.jsx("p",{className:"text-gray-600 mb-6",children:"Let friends scan this code to add you instantly"})]}),a.jsx("div",{className:"bg-gray-100 rounded-lg p-8 flex items-center justify-center",children:a.jsx("div",{className:"w-48 h-48 bg-white rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center",children:a.jsx(P,{className:"w-16 h-16 text-gray-400"})})}),a.jsx("p",{className:"text-center text-sm text-gray-500",children:"QR code will be generated here"})]})]})]})]})}):null}function D(){let{user:e}=(0,c.t)(),{friendLocations:s}=(0,d.o)(),[t,r]=(0,i.useState)(""),[l,o]=(0,i.useState)(!1),[u,y]=(0,i.useState)("online"),[p,g]=(0,i.useState)([{id:"1",name:"Emma Johnson",email:"<EMAIL>",image:null,isOnline:!0,lastSeen:new Date(Date.now()-3e5)},{id:"2",name:"Alex Chen",email:"<EMAIL>",image:null,isOnline:!1,lastSeen:new Date(Date.now()-72e5)},{id:"3",name:"Sarah Wilson",email:"<EMAIL>",image:null,isOnline:!0,lastSeen:new Date(Date.now()-6e4)}]),j=p.filter(e=>{let s=e.name.toLowerCase().includes(t.toLowerCase())||e.email.toLowerCase().includes(t.toLowerCase()),a="all"===u||"online"===u&&e.isOnline;return s&&a}),f=p.filter(e=>e.isOnline).length,v=e=>s.find(s=>s.friend.id===e);return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Friends"}),(0,a.jsxs)("p",{className:"text-gray-600",children:[f," of ",p.length," friends online"]})]}),(0,a.jsxs)(x.z,{onClick:()=>o(!0),children:[a.jsx(Z,{className:"w-4 h-4 mr-2"}),"Add Friend"]})]}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx(q,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400"}),a.jsx("input",{type:"text",placeholder:"Search friends...",value:t,onChange:e=>r(e.target.value),className:"w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"})]}),(0,a.jsxs)("div",{className:"flex space-x-1 bg-gray-100 rounded-lg p-1",children:[(0,a.jsxs)("button",{onClick:()=>y("online"),className:`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${"online"===u?"bg-white text-gray-900 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:["Online (",f,")"]}),(0,a.jsxs)("button",{onClick:()=>y("all"),className:`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${"all"===u?"bg-white text-gray-900 shadow-sm":"text-gray-600 hover:text-gray-900"}`,children:["All (",p.length,")"]})]}),a.jsx("div",{className:"space-y-3",children:0===j.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[a.jsx(h.Z,{className:"w-12 h-12 text-gray-400 mx-auto mb-4"}),a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:t?"No friends found":"No friends yet"}),a.jsx("p",{className:"text-gray-600 mb-4",children:t?"Try adjusting your search terms":"Start building your campus network by adding friends"}),!t&&(0,a.jsxs)(x.z,{onClick:()=>o(!0),children:[a.jsx(Z,{className:"w-4 h-4 mr-2"}),"Add Your First Friend"]})]}):j.map((e,s)=>{let t=v(e.id);return a.jsx(n.E.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*s},className:"bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("div",{className:"relative",children:[a.jsx("div",{className:"w-12 h-12 rounded-full bg-primary-100 flex items-center justify-center",children:e.image?a.jsx("img",{src:e.image,alt:e.name,className:"w-12 h-12 rounded-full object-cover"}):a.jsx("span",{className:"text-lg font-medium text-primary-600",children:(0,C.Qm)(e.name)})}),a.jsx("div",{className:`absolute -bottom-1 -right-1 w-4 h-4 rounded-full border-2 border-white ${e.isOnline?"bg-green-500":"bg-gray-400"}`})]}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-medium text-gray-900",children:e.name}),a.jsx("p",{className:"text-sm text-gray-600",children:e.email}),a.jsx("div",{className:"flex items-center space-x-2 mt-1",children:e.isOnline?(0,a.jsxs)(a.Fragment,{children:[a.jsx("div",{className:"w-2 h-2 bg-green-500 rounded-full"}),a.jsx("span",{className:"text-xs text-green-600",children:"Online"}),t&&(0,a.jsxs)(a.Fragment,{children:[a.jsx("span",{className:"text-xs text-gray-400",children:"•"}),a.jsx(m.Z,{className:"w-3 h-3 text-gray-400"}),(0,a.jsxs)("span",{className:"text-xs text-gray-600",children:[t.coordinates.building," Building, Floor ",t.coordinates.floor]})]})]}):(0,a.jsxs)(a.Fragment,{children:[a.jsx(w,{className:"w-3 h-3 text-gray-400"}),(0,a.jsxs)("span",{className:"text-xs text-gray-600",children:["Last seen ",(0,C.pi)(e.lastSeen)]})]})})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[t&&(0,a.jsxs)(x.z,{variant:"outline",size:"sm",children:[a.jsx(m.Z,{className:"w-4 h-4 mr-1"}),"Locate"]}),(0,a.jsxs)(x.z,{variant:"outline",size:"sm",children:[a.jsx(k,{className:"w-4 h-4 mr-1"}),"Message"]}),a.jsx(x.z,{variant:"ghost",size:"icon",children:a.jsx(O,{className:"w-4 h-4"})})]})]})},e.id)})}),a.jsx($,{isOpen:l,onClose:()=>o(!1)})]})}var A=t(23485);function _({isOpen:e,onClose:s,onPermissionResponse:t}){return e?a.jsx(N.M,{children:(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center p-4",children:[a.jsx(n.E.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},className:"absolute inset-0 bg-black/50 backdrop-blur-sm"}),(0,a.jsxs)(n.E.div,{initial:{opacity:0,scale:.95,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.95,y:20},className:"relative w-full max-w-md bg-white rounded-2xl shadow-2xl",children:[(0,a.jsxs)("div",{className:"p-6 text-center",children:[a.jsx("div",{className:"w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4",children:a.jsx(m.Z,{className:"w-8 h-8 text-primary-600"})}),a.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Enable Location Sharing"}),a.jsx("p",{className:"text-gray-600",children:"To help you find friends and navigate campus, we need access to your location."})]}),a.jsx("div",{className:"px-6 pb-6",children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[a.jsx("div",{className:"w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0",children:a.jsx(h.Z,{className:"w-4 h-4 text-green-600"})}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-medium text-gray-900",children:"Find Friends"}),a.jsx("p",{className:"text-sm text-gray-600",children:"See where your friends are on campus in real-time"})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[a.jsx("div",{className:"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0",children:a.jsx(m.Z,{className:"w-4 h-4 text-blue-600"})}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-medium text-gray-900",children:"Campus Navigation"}),a.jsx("p",{className:"text-sm text-gray-600",children:"Get directions and navigate between buildings"})]})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[a.jsx("div",{className:"w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0",children:a.jsx(A.Z,{className:"w-4 h-4 text-purple-600"})}),(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-medium text-gray-900",children:"Privacy Control"}),a.jsx("p",{className:"text-sm text-gray-600",children:"You can turn off location sharing anytime in settings"})]})]})]})}),a.jsx("div",{className:"px-6 pb-6",children:(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[a.jsx("h4",{className:"font-medium text-gray-900 mb-2",children:"Your Privacy Matters"}),(0,a.jsxs)("ul",{className:"text-sm text-gray-600 space-y-1",children:[a.jsx("li",{children:"• Your location is only shared with friends you approve"}),a.jsx("li",{children:"• You can go invisible or stop sharing anytime"}),a.jsx("li",{children:"• Location data is encrypted and secure"}),a.jsx("li",{children:"• We never share your data with third parties"})]})]})}),(0,a.jsxs)("div",{className:"px-6 pb-6 flex flex-col space-y-3",children:[a.jsx(x.z,{onClick:()=>{t(!0)},className:"w-full",size:"lg",children:"Allow Location Access"}),a.jsx(x.z,{onClick:()=>{t(!1)},variant:"outline",className:"w-full",size:"lg",children:"Not Now"})]}),a.jsx("button",{onClick:s,className:"absolute top-4 right-4 p-2 hover:bg-gray-100 rounded-lg transition-colors",children:a.jsx(F.Z,{className:"w-5 h-5 text-gray-500"})})]})]})}):null}/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let Y=(0,u.Z)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),U=(0,u.Z)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]]),R=(0,u.Z)("HelpCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]]),B=[{id:"map",label:"Campus Map",icon:m.Z,description:"Interactive campus map"},{id:"friends",label:"Friends",icon:h.Z,description:"Manage your friends"},{id:"settings",label:"Settings",icon:Y,description:"App preferences"}],T=[{id:"notifications",label:"Notifications",icon:U,description:"Notification settings"},{id:"privacy",label:"Privacy",icon:A.Z,description:"Privacy controls"},{id:"help",label:"Help & Support",icon:R,description:"Get help"}];function W({isOpen:e,onClose:s,activeView:t,onViewChange:i}){let r=e=>{i(e),s()};return a.jsx(N.M,{children:e&&a.jsx(a.Fragment,{children:(0,a.jsxs)(n.E.aside,{initial:{x:-300},animate:{x:0},exit:{x:-300},transition:{type:"spring",damping:25,stiffness:200},className:"fixed lg:relative inset-y-0 left-0 z-50 w-72 bg-white border-r border-gray-200 flex flex-col",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border-b",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("div",{className:"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center",children:a.jsx(m.Z,{className:"w-5 h-5 text-white"})}),a.jsx("span",{className:"text-lg font-semibold text-gray-900",children:"Menu"})]}),a.jsx(x.z,{variant:"ghost",size:"icon",onClick:s,className:"lg:hidden",children:a.jsx(F.Z,{className:"w-5 h-5"})})]}),(0,a.jsxs)("nav",{className:"flex-1 p-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("h3",{className:"text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3",children:"Main"}),B.map(e=>{let s=e.icon,i=t===e.id;return(0,a.jsxs)("button",{onClick:()=>r(e.id),className:`w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-left transition-colors ${i?"bg-primary-50 text-primary-700 border border-primary-200":"text-gray-700 hover:bg-gray-100"}`,children:[a.jsx(s,{className:`w-5 h-5 ${i?"text-primary-600":"text-gray-500"}`}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium",children:e.label}),a.jsx("p",{className:"text-xs text-gray-500",children:e.description})]})]},e.id)})]}),(0,a.jsxs)("div",{className:"mt-8 space-y-2",children:[a.jsx("h3",{className:"text-xs font-semibold text-gray-500 uppercase tracking-wider mb-3",children:"More"}),T.map(e=>{let s=e.icon;return(0,a.jsxs)("button",{className:"w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-left text-gray-700 hover:bg-gray-100 transition-colors",children:[a.jsx(s,{className:"w-5 h-5 text-gray-500"}),(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium",children:e.label}),a.jsx("p",{className:"text-xs text-gray-500",children:e.description})]})]},e.id)})]})]}),a.jsx("div",{className:"p-4 border-t",children:(0,a.jsxs)("div",{className:"bg-primary-50 rounded-lg p-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[a.jsx("div",{className:"w-6 h-6 bg-primary-600 rounded-full flex items-center justify-center",children:a.jsx(m.Z,{className:"w-3 h-3 text-white"})}),a.jsx("span",{className:"text-sm font-medium text-primary-900",children:"SYK SchoolFinder"})]}),a.jsx("p",{className:"text-xs text-primary-700",children:"Stay connected with your campus community"})]})})]})})})}/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let H=(0,u.Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]),Q=(0,u.Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);var V=t(18822);/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let G=(0,u.Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]]);function I({user:e,onMenuClick:s,currentLocation:t}){let[l,c]=(0,i.useState)(!1),[d,o]=(0,i.useState)(!1);return(0,a.jsxs)("header",{className:"bg-white border-b border-gray-200 px-4 py-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[a.jsx(x.z,{variant:"ghost",size:"icon",onClick:s,className:"lg:hidden",children:a.jsx(H,{className:"w-5 h-5"})}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("div",{className:"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center",children:a.jsx(m.Z,{className:"w-5 h-5 text-white"})}),a.jsx("span",{className:"text-xl font-bold text-gray-900 hidden sm:block",children:"SYK SchoolFinder"})]}),t&&(0,a.jsxs)("div",{className:"hidden md:flex items-center space-x-2 text-sm text-gray-600",children:[a.jsx(m.Z,{className:"w-4 h-4"}),a.jsx("span",{children:(()=>{if(!t)return"Location not available";let{building:e,floor:s}=t.coordinates;return`${e.charAt(0).toUpperCase()+e.slice(1)} Building, Floor ${s}`})()})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[a.jsx(x.z,{variant:"ghost",size:"icon",className:"hidden sm:flex",children:a.jsx(q,{className:"w-5 h-5"})}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)(x.z,{variant:"ghost",size:"icon",onClick:()=>o(!d),className:"relative",children:[a.jsx(U,{className:"w-5 h-5"}),a.jsx("span",{className:"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full text-xs text-white flex items-center justify-center",children:"2"})]}),a.jsx(N.M,{children:d&&(0,a.jsxs)(n.E.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},className:"absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border z-50",children:[a.jsx("div",{className:"p-4 border-b",children:a.jsx("h3",{className:"font-semibold text-gray-900",children:"Notifications"})}),a.jsx("div",{className:"max-h-64 overflow-y-auto",children:a.jsx("div",{className:"p-4 text-center text-gray-500",children:"No new notifications"})})]})})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)("button",{onClick:()=>c(!l),className:"flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 transition-colors",children:[a.jsx("div",{className:"w-8 h-8 rounded-full bg-primary-100 flex items-center justify-center",children:e?.image||e?.avatar?a.jsx("img",{src:e.image||e.avatar,alt:e.name,className:"w-8 h-8 rounded-full object-cover"}):a.jsx("span",{className:"text-sm font-medium text-primary-600",children:(0,C.Qm)(e?.name||"User")})}),(0,a.jsxs)("div",{className:"hidden sm:block text-left",children:[a.jsx("p",{className:"text-sm font-medium text-gray-900",children:e?.name||"User"}),a.jsx("p",{className:"text-xs text-gray-500",children:e?.email})]}),a.jsx(Q,{className:"w-4 h-4 text-gray-400"})]}),a.jsx(N.M,{children:l&&a.jsx(n.E.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},className:"absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg border z-50",children:(0,a.jsxs)("div",{className:"p-2",children:[(0,a.jsxs)("div",{className:"px-3 py-2 border-b",children:[a.jsx("p",{className:"font-medium text-gray-900",children:e?.name}),a.jsx("p",{className:"text-sm text-gray-500",children:e?.email})]}),(0,a.jsxs)("div",{className:"py-2",children:[(0,a.jsxs)("button",{className:"w-full flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md",children:[a.jsx(V.Z,{className:"w-4 h-4"}),a.jsx("span",{children:"Profile"})]}),(0,a.jsxs)("button",{className:"w-full flex items-center space-x-2 px-3 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded-md",children:[a.jsx(Y,{className:"w-4 h-4"}),a.jsx("span",{children:"Settings"})]})]}),a.jsx("div",{className:"border-t pt-2",children:(0,a.jsxs)("button",{onClick:()=>{(0,r.signOut)({callbackUrl:"/"})},className:"w-full flex items-center space-x-2 px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md",children:[a.jsx(G,{className:"w-4 h-4"}),a.jsx("span",{children:"Sign Out"})]})})]})})})]})]})]}),(l||d)&&a.jsx("div",{className:"fixed inset-0 z-40",onClick:()=>{c(!1),o(!1)}})]})}function J(){let{data:e,status:s}=(0,r.useSession)(),t=(0,l.useRouter)(),{user:m,isAuthenticated:h}=(0,c.t)(),{isLocationEnabled:u,requestLocationPermission:y,getFriendLocations:p,currentLocation:g}=(0,d.o)(),[j,f]=(0,i.useState)(!1),[v,b]=(0,i.useState)(!1),[N,w]=(0,i.useState)("map");(0,i.useEffect)(()=>{"unauthenticated"!==s||h||t.push("/")},[s,h,t]),(0,i.useEffect)(()=>{(e||m)&&!u&&f(!0)},[e,m,u]),(0,i.useEffect)(()=>{u&&(e||m)&&p()},[u,e,m,p]);let k=async e=>{e&&await y(),f(!1)};if("loading"===s||!e&&!m)return a.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:a.jsx(o.T,{size:"lg"})});let z=m||e?.user;return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 flex",children:[a.jsx(W,{isOpen:v,onClose:()=>b(!1),activeView:N,onViewChange:w}),(0,a.jsxs)("div",{className:"flex-1 flex flex-col",children:[a.jsx(I,{user:z,onMenuClick:()=>b(!0),currentLocation:g}),(0,a.jsxs)("main",{className:"flex-1 relative overflow-hidden",children:["map"===N&&a.jsx(n.E.div,{initial:{opacity:0},animate:{opacity:1},className:"h-full",children:a.jsx(S,{})}),"friends"===N&&a.jsx(n.E.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},className:"h-full p-4 overflow-y-auto",children:a.jsx("div",{className:"max-w-4xl mx-auto",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[a.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Friends"}),a.jsx(D,{})]})})}),"settings"===N&&a.jsx(n.E.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},className:"h-full p-4 overflow-y-auto",children:a.jsx("div",{className:"max-w-4xl mx-auto",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm border p-6",children:[a.jsx("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Settings"}),a.jsx("div",{className:"space-y-6",children:(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Privacy Settings"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium text-gray-900",children:"Share Location"}),a.jsx("p",{className:"text-sm text-gray-500",children:"Allow friends to see your location"})]}),a.jsx(x.z,{variant:"outline",size:"sm",children:m?.privacySettings?.shareLocation?"Enabled":"Disabled"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[a.jsx("p",{className:"font-medium text-gray-900",children:"Invisible Mode"}),a.jsx("p",{className:"text-sm text-gray-500",children:"Hide from friends temporarily"})]}),a.jsx(x.z,{variant:"outline",size:"sm",children:m?.privacySettings?.invisibleMode?"On":"Off"})]})]})]})})]})})})]})]}),a.jsx(_,{isOpen:j,onClose:()=>f(!1),onPermissionResponse:k}),v&&a.jsx("div",{className:"fixed inset-0 bg-black/50 z-40 lg:hidden",onClick:()=>b(!1)})]})}},23511:(e,s,t)=>{"use strict";t.d(s,{z:()=>l});var a=t(95344),i=t(3729);let r=(e="default",s="default",t)=>{let a={default:"bg-primary-600 text-white hover:bg-primary-700",destructive:"bg-red-600 text-white hover:bg-red-700",outline:"border border-gray-300 bg-white hover:bg-gray-50 text-gray-700",secondary:"bg-secondary-600 text-white hover:bg-secondary-700",ghost:"hover:bg-gray-100 text-gray-700",link:"underline-offset-4 hover:underline text-primary-600"},i={default:"h-10 py-2 px-4",sm:"h-9 px-3 rounded-md",lg:"h-11 px-8 rounded-lg",icon:"h-10 w-10"};return["inline-flex items-center justify-center rounded-lg text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none",a[e]||a.default,i[s]||i.default,t].filter(Boolean).join(" ")},l=(0,i.forwardRef)(({className:e,variant:s="default",size:t="default",...i},l)=>a.jsx("button",{className:r(s,t,e),ref:l,...i}));l.displayName="Button"},95252:(e,s,t)=>{"use strict";t.d(s,{T:()=>r});var a=t(95344),i=t(91626);function r({size:e="md",className:s,color:t="primary"}){return a.jsx("div",{className:(0,i.cn)("flex items-center justify-center",s),children:a.jsx("div",{className:(0,i.cn)("animate-spin rounded-full border-2 border-current border-t-transparent",{sm:"w-4 h-4",md:"w-6 h-6",lg:"w-8 h-8"}[e],{primary:"text-primary-600",secondary:"text-secondary-600",white:"text-white"}[t]),role:"status","aria-label":"Loading",children:a.jsx("span",{className:"sr-only",children:"Loading..."})})})}},91626:(e,s,t)=>{"use strict";t.d(s,{Qm:()=>d,cn:()=>r,oH:()=>n,pi:()=>l,uo:()=>c});var a=t(56815),i=t(79377);function r(...e){return(0,i.m6)((0,a.W)(e))}function l(e){let s=new Date,t=new Date(e),a=Math.floor((s.getTime()-t.getTime())/1e3);if(a<60)return"Just now";let i=Math.floor(a/60);if(i<60)return`${i}m ago`;let r=Math.floor(i/60);if(r<24)return`${r}h ago`;let l=Math.floor(r/24);return l<7?`${l}d ago`:new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"})}function n(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}function c(e){let s=[];return e.length<8&&s.push("Password must be at least 8 characters long"),/[A-Z]/.test(e)||s.push("Password must contain at least one uppercase letter"),/[a-z]/.test(e)||s.push("Password must contain at least one lowercase letter"),/\d/.test(e)||s.push("Password must contain at least one number"),{isValid:0===s.length,errors:s}}function d(e){return e.split(" ").map(e=>e.charAt(0).toUpperCase()).join("").slice(0,2)}},79865:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>r,__esModule:()=>i,default:()=>l});let a=(0,t(86843).createProxy)(String.raw`C:\Users\<USER>\OneDrive - Suomalaisen Yhteiskoulun Osakeyhtiö\Desktop\Coding\schoolfinder\app\dashboard\page.tsx`),{__esModule:i,$$typeof:r}=a,l=a.default}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[225,340,129,978],()=>t(97213));module.exports=a})();