'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, Search, UserPlus, Mail, Link, QrCode } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { getInitials } from '@/lib/utils'

interface AddFriendModalProps {
  isOpen: boolean
  onClose: () => void
}

export function AddFriendModal({ isOpen, onClose }: AddFriendModalProps) {
  const [activeTab, setActiveTab] = useState<'search' | 'invite' | 'qr'>('search')
  const [searchQuery, setSearchQuery] = useState('')
  const [searchResults, setSearchResults] = useState([
    {
      id: '1',
      name: '<PERSON>',
      email: '<EMAIL>',
      image: null,
      mutualFriends: 3
    },
    {
      id: '2',
      name: '<PERSON>',
      email: '<EMAIL>',
      image: null,
      mutualFriends: 1
    }
  ])

  const handleSearch = (query: string) => {
    setSearchQuery(query)
    // TODO: Implement actual search API call
  }

  const handleSendRequest = (userId: string) => {
    // TODO: Implement friend request API call
    console.log('Sending friend request to:', userId)
  }

  const generateInviteLink = () => {
    // TODO: Generate actual invite link
    return `${window.location.origin}/invite/user123`
  }

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        {/* Backdrop */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={onClose}
          className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        />

        {/* Modal */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          className="relative w-full max-w-lg bg-white rounded-2xl shadow-2xl"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b">
            <h2 className="text-2xl font-bold text-gray-900">Add Friends</h2>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Tabs */}
          <div className="flex border-b">
            <button
              onClick={() => setActiveTab('search')}
              className={`flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors ${
                activeTab === 'search'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              <Search className="w-4 h-4 inline mr-2" />
              Search
            </button>
            <button
              onClick={() => setActiveTab('invite')}
              className={`flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors ${
                activeTab === 'invite'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              <Link className="w-4 h-4 inline mr-2" />
              Invite Link
            </button>
            <button
              onClick={() => setActiveTab('qr')}
              className={`flex-1 py-3 px-4 text-sm font-medium border-b-2 transition-colors ${
                activeTab === 'qr'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              <QrCode className="w-4 h-4 inline mr-2" />
              QR Code
            </button>
          </div>

          {/* Content */}
          <div className="p-6">
            {activeTab === 'search' && (
              <div className="space-y-4">
                {/* Search Input */}
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search by name or email..."
                    value={searchQuery}
                    onChange={(e) => handleSearch(e.target.value)}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  />
                </div>

                {/* Search Results */}
                <div className="space-y-3 max-h-64 overflow-y-auto">
                  {searchResults.map((user) => (
                    <div
                      key={user.id}
                      className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50"
                    >
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center">
                          {user.image ? (
                            <img
                              src={user.image}
                              alt={user.name}
                              className="w-10 h-10 rounded-full object-cover"
                            />
                          ) : (
                            <span className="text-sm font-medium text-primary-600">
                              {getInitials(user.name)}
                            </span>
                          )}
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">{user.name}</p>
                          <p className="text-sm text-gray-600">{user.email}</p>
                          {user.mutualFriends > 0 && (
                            <p className="text-xs text-gray-500">
                              {user.mutualFriends} mutual friends
                            </p>
                          )}
                        </div>
                      </div>
                      <Button
                        size="sm"
                        onClick={() => handleSendRequest(user.id)}
                      >
                        <UserPlus className="w-4 h-4 mr-1" />
                        Add
                      </Button>
                    </div>
                  ))}
                  
                  {searchQuery && searchResults.length === 0 && (
                    <div className="text-center py-8">
                      <Search className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                      <p className="text-gray-600">No users found</p>
                      <p className="text-sm text-gray-500 mt-1">
                        Try searching with a different name or email
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {activeTab === 'invite' && (
              <div className="space-y-4">
                <div className="text-center">
                  <Link className="w-12 h-12 text-primary-600 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Share Your Invite Link
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Send this link to friends so they can add you directly
                  </p>
                </div>

                <div className="bg-gray-50 rounded-lg p-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="text"
                      value={generateInviteLink()}
                      readOnly
                      className="flex-1 bg-white border border-gray-300 rounded-lg px-3 py-2 text-sm"
                    />
                    <Button
                      variant="outline"
                      onClick={() => navigator.clipboard.writeText(generateInviteLink())}
                    >
                      Copy
                    </Button>
                  </div>
                </div>

                <div className="flex space-x-2">
                  <Button variant="outline" className="flex-1">
                    <Mail className="w-4 h-4 mr-2" />
                    Email
                  </Button>
                  <Button variant="outline" className="flex-1">
                    Share
                  </Button>
                </div>
              </div>
            )}

            {activeTab === 'qr' && (
              <div className="space-y-4">
                <div className="text-center">
                  <QrCode className="w-12 h-12 text-primary-600 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    QR Code
                  </h3>
                  <p className="text-gray-600 mb-6">
                    Let friends scan this code to add you instantly
                  </p>
                </div>

                {/* QR Code Placeholder */}
                <div className="bg-gray-100 rounded-lg p-8 flex items-center justify-center">
                  <div className="w-48 h-48 bg-white rounded-lg border-2 border-dashed border-gray-300 flex items-center justify-center">
                    <QrCode className="w-16 h-16 text-gray-400" />
                  </div>
                </div>

                <p className="text-center text-sm text-gray-500">
                  QR code will be generated here
                </p>
              </div>
            )}
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  )
}
