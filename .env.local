# Next.js Configuration
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-super-secret-key-change-this-in-production-12345

# Google OAuth (Optional - you can skip this for now)
GOOGLE_CLIENT_ID=your-google-client-id-here
GOOGLE_CLIENT_SECRET=your-google-client-secret-here

# Database (We'll use a simple local MongoDB for now)
MONGODB_URI=mongodb://localhost:27017/syk-schoolfinder

# JWT Secret for authentication
JWT_SECRET=your-jwt-secret-key-change-this-in-production-67890

# Server Configuration
SERVER_URL=http://localhost:3001
NEXT_PUBLIC_API_URL=http://localhost:3001/api
NEXT_PUBLIC_SERVER_URL=http://localhost:3001

# Email Configuration (Optional for now)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password
