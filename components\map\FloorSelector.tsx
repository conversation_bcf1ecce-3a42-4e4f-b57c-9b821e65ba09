'use client'

import { motion } from 'framer-motion'
import { Button } from '@/components/ui/Button'

interface FloorSelectorProps {
  currentFloor: number
  floors: number[]
  onFloorChange: (floor: number) => void
}

export function FloorSelector({ currentFloor, floors, onFloorChange }: FloorSelectorProps) {
  return (
    <div className="absolute top-4 right-4 bg-white rounded-lg shadow-sm border p-2">
      <div className="flex flex-col space-y-1">
        <span className="text-xs font-medium text-gray-500 text-center mb-1">
          Floor
        </span>
        {floors.map((floor) => (
          <motion.div key={floor} whileTap={{ scale: 0.95 }}>
            <Button
              variant={currentFloor === floor ? 'default' : 'ghost'}
              size="sm"
              onClick={() => onFloorChange(floor)}
              className={`w-12 h-8 text-sm font-medium ${
                currentFloor === floor
                  ? 'bg-primary-600 text-white'
                  : 'text-gray-600 hover:bg-gray-100'
              }`}
            >
              {floor}
            </Button>
          </motion.div>
        ))}
      </div>
    </div>
  )
}
