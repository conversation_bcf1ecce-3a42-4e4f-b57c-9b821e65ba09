# SYK SchoolFinder

A modern, intuitive web/mobile app for SYK school that lets students add friends and see their real-time locations on detailed interactive campus maps.

## 🚀 Features

### Core Features
- **User Authentication** - Email/password and Google OAuth sign-in
- **Friend Management** - Add/remove friends, search by name/email, invite links
- **Real-time Location Sharing** - See friends' locations on campus maps
- **Interactive Campus Maps** - Multi-floor navigation with detailed room layouts
- **Privacy Controls** - Toggle location sharing, invisible mode, granular permissions
- **Mobile-First Design** - Responsive PWA optimized for iOS and Android

### Technical Features
- **Real-time Updates** - WebSocket connections for instant location updates
- **Progressive Web App** - Installable, offline-capable, app-like experience
- **Modern UI/UX** - Clean design inspired by Apple Maps and otamaps.com
- **Multi-floor Support** - Navigate between different floors and buildings
- **Location Accuracy** - Indoor positioning with room-level precision

## 🛠️ Tech Stack

### Frontend
- **Next.js 14** - React framework with App Router
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first styling
- **Framer Motion** - Smooth animations and transitions
- **Zustand** - Lightweight state management
- **NextAuth.js** - Authentication with Google OAuth

### Backend
- **Node.js** - Server runtime
- **Express.js** - Web framework
- **Socket.io** - Real-time communication
- **MongoDB** - Document database
- **Mongoose** - MongoDB object modeling

### Additional Tools
- **PWA** - Progressive Web App capabilities
- **SVG Maps** - Interactive vector-based campus maps
- **JWT** - Secure token-based authentication

## 📱 Getting Started

### Prerequisites
- Node.js 18+ 
- MongoDB (local or cloud)
- Google OAuth credentials (optional)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd schoolfinder
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env.local
   ```
   
   Fill in your environment variables:
   ```env
   NEXTAUTH_URL=http://localhost:3000
   NEXTAUTH_SECRET=your-nextauth-secret
   GOOGLE_CLIENT_ID=your-google-client-id
   GOOGLE_CLIENT_SECRET=your-google-client-secret
   MONGODB_URI=mongodb://localhost:27017/syk-schoolfinder
   JWT_SECRET=your-jwt-secret
   SERVER_URL=http://localhost:3001
   ```

4. **Start the development servers**
   ```bash
   # Start both frontend and backend
   npm run dev:full
   
   # Or start them separately:
   npm run dev        # Frontend (Next.js)
   npm run dev:server # Backend (Express + Socket.io)
   ```

5. **Open your browser**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:3001

## 🏗️ Project Structure

```
schoolfinder/
├── app/                    # Next.js App Router pages
│   ├── dashboard/         # Main dashboard page
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   ├── page.tsx          # Landing page
│   └── providers.tsx     # App providers
├── components/            # React components
│   ├── auth/             # Authentication components
│   ├── friends/          # Friend management
│   ├── layout/           # Layout components
│   ├── location/         # Location services
│   ├── map/              # Interactive map components
│   ├── pages/            # Page components
│   ├── providers/        # Context providers
│   └── ui/               # Reusable UI components
├── lib/                  # Utility libraries
│   ├── api.ts           # API client
│   └── utils.ts         # Helper functions
├── server/               # Backend server
│   ├── middleware/      # Express middleware
│   ├── models/          # Database models
│   ├── routes/          # API routes
│   └── index.js         # Server entry point
├── store/               # Zustand stores
│   ├── authStore.ts     # Authentication state
│   └── locationStore.ts # Location state
├── types/               # TypeScript definitions
│   └── index.ts         # Type definitions
└── public/              # Static assets
    ├── icons/           # PWA icons
    ├── manifest.json    # PWA manifest
    └── screenshots/     # App screenshots
```

## 🗺️ Map Integration

The app uses SVG-based interactive maps for precise indoor positioning:

1. **Add your SYK floor plans** to the map system
2. **Configure building and floor data** in the database
3. **Set up coordinate systems** for each floor
4. **Define rooms and zones** for navigation

### Map Data Structure
```typescript
interface MapData {
  building: string
  floor: number
  svgData: string
  bounds: { width: number, height: number }
  rooms: Room[]
  zones: Zone[]
}
```

## 🔐 Privacy & Security

- **End-to-end encryption** for location data
- **Granular privacy controls** - users control who sees their location
- **Invisible mode** - temporarily hide from all friends
- **Data retention policies** - automatic cleanup of old location data
- **GDPR compliant** - user data export and deletion

## 📱 PWA Features

- **Installable** - Add to home screen on mobile devices
- **Offline support** - Basic functionality without internet
- **Push notifications** - Friend requests and location updates
- **Native app feel** - Full-screen, app-like experience

## 🚀 Deployment

### Frontend (Vercel)
```bash
npm run build
# Deploy to Vercel or your preferred platform
```

### Backend (Railway/Heroku)
```bash
# Set environment variables
# Deploy server/ directory
```

### Database (MongoDB Atlas)
- Set up MongoDB Atlas cluster
- Update MONGODB_URI in environment variables

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue on GitHub
- Contact the development team
- Check the documentation

## 🎯 Roadmap

- [ ] **Phase 1**: Core functionality (authentication, friends, basic maps)
- [ ] **Phase 2**: Advanced mapping (multi-building, outdoor areas)
- [ ] **Phase 3**: Social features (groups, events, messaging)
- [ ] **Phase 4**: Analytics and insights
- [ ] **Phase 5**: Integration with school systems

---

Built with ❤️ for the SYK community
