var n=require("preact"),t=require("preact/hooks");function e(n,t){for(var e in n)if("__source"!==e&&!(e in t))return!0;for(var r in t)if("__source"!==r&&n[r]!==t[r])return!0;return!1}function r(n,t){this.props=n,this.context=t}function u(t,r){function u(n){var t=this.props.ref,u=t==n.ref;return!u&&t&&(t.call?t(null):t.current=null),r?!r(this.props,n)||!u:e(this.props,n)}function o(e){return this.shouldComponentUpdate=u,n.createElement(t,e)}return o.displayName="Memo("+(t.displayName||t.name)+")",o.prototype.isReactComponent=!0,o.__f=!0,o}(r.prototype=new n.Component).isPureReactComponent=!0,r.prototype.shouldComponentUpdate=function(n,t){return e(this.props,n)||e(this.state,t)};var o=n.options.__b;n.options.__b=function(n){n.type&&n.type.__f&&n.ref&&(n.props.ref=n.ref,n.ref=null),o&&o(n)};var i="undefined"!=typeof Symbol&&Symbol.for&&Symbol.for("react.forward_ref")||3911;function c(n){function t(t){if(!("ref"in t))return n(t,null);var e=t.ref;delete t.ref;var r=n(t,e);return t.ref=e,r}return t.$$typeof=i,t.render=t,t.prototype.isReactComponent=t.__f=!0,t.displayName="ForwardRef("+(n.displayName||n.name)+")",t}var l=function(t,e){return null==t?null:n.toChildArray(n.toChildArray(t).map(e))},f={map:l,forEach:l,count:function(t){return t?n.toChildArray(t).length:0},only:function(t){var e=n.toChildArray(t);if(1!==e.length)throw"Children.only";return e[0]},toArray:n.toChildArray},a=n.options.__e;n.options.__e=function(n,t,e,r){if(n.then)for(var u,o=t;o=o.__;)if((u=o.__c)&&u.__c)return null==t.__e&&(t.__e=e.__e,t.__k=e.__k),u.__c(n,t);a(n,t,e,r)};var s=n.options.unmount;function p(n,t,e){return n&&(n.__c&&n.__c.__H&&(n.__c.__H.__.forEach(function(n){"function"==typeof n.__c&&n.__c()}),n.__c.__H=null),null!=(n=function(n,t){for(var e in t)n[e]=t[e];return n}({},n)).__c&&(n.__c.__P===e&&(n.__c.__P=t),n.__c=null),n.__k=n.__k&&n.__k.map(function(n){return p(n,t,e)})),n}function h(n,t,e){return n&&e&&(n.__v=null,n.__k=n.__k&&n.__k.map(function(n){return h(n,t,e)}),n.__c&&n.__c.__P===t&&(n.__e&&e.appendChild(n.__e),n.__c.__e=!0,n.__c.__P=e)),n}function v(){this.__u=0,this.t=null,this.__b=null}function d(n){var t=n.__.__c;return t&&t.__a&&t.__a(n)}function m(t){var e,r,u;function o(o){if(e||(e=t()).then(function(n){r=n.default||n},function(n){u=n}),u)throw u;if(!r)throw e;return n.createElement(r,o)}return o.displayName="Lazy",o.__f=!0,o}function x(){this.u=null,this.o=null}n.options.unmount=function(n){var t=n.__c;t&&t.__R&&t.__R(),t&&32&n.__u&&(n.type=null),s&&s(n)},(v.prototype=new n.Component).__c=function(n,t){var e=t.__c,r=this;null==r.t&&(r.t=[]),r.t.push(e);var u=d(r.__v),o=!1,i=function(){o||(o=!0,e.__R=null,u?u(c):c())};e.__R=i;var c=function(){if(!--r.__u){if(r.state.__a){var n=r.state.__a;r.__v.__k[0]=h(n,n.__c.__P,n.__c.__O)}var t;for(r.setState({__a:r.__b=null});t=r.t.pop();)t.forceUpdate()}};r.__u++||32&t.__u||r.setState({__a:r.__b=r.__v.__k[0]}),n.then(i,i)},v.prototype.componentWillUnmount=function(){this.t=[]},v.prototype.render=function(t,e){if(this.__b){if(this.__v.__k){var r=document.createElement("div"),u=this.__v.__k[0].__c;this.__v.__k[0]=p(this.__b,r,u.__O=u.__P)}this.__b=null}var o=e.__a&&n.createElement(n.Fragment,null,t.fallback);return o&&(o.__u&=-33),[n.createElement(n.Fragment,null,e.__a?null:t.children),o]};var b=function(n,t,e){if(++e[1]===e[0]&&n.o.delete(t),n.props.revealOrder&&("t"!==n.props.revealOrder[0]||!n.o.size))for(e=n.u;e;){for(;e.length>3;)e.pop()();if(e[1]<e[0])break;n.u=e=e[2]}};function y(n){return this.getChildContext=function(){return n.context},n.children}function _(t){var e=this,r=t.i;e.componentWillUnmount=function(){n.render(null,e.l),e.l=null,e.i=null},e.i&&e.i!==r&&e.componentWillUnmount(),e.l||(e.i=r,e.l={nodeType:1,parentNode:r,childNodes:[],contains:function(){return!0},appendChild:function(n){this.childNodes.push(n),e.i.appendChild(n)},insertBefore:function(n,t){this.childNodes.push(n),e.i.appendChild(n)},removeChild:function(n){this.childNodes.splice(this.childNodes.indexOf(n)>>>1,1),e.i.removeChild(n)}}),n.render(n.createElement(y,{context:e.context},t.__v),e.l)}function g(t,e){var r=n.createElement(_,{__v:t,i:e});return r.containerInfo=e,r}(x.prototype=new n.Component).__a=function(n){var t=this,e=d(t.__v),r=t.o.get(n);return r[0]++,function(u){var o=function(){t.props.revealOrder?(r.push(u),b(t,n,r)):u()};e?e(o):o()}},x.prototype.render=function(t){this.u=null,this.o=new Map;var e=n.toChildArray(t.children);t.revealOrder&&"b"===t.revealOrder[0]&&e.reverse();for(var r=e.length;r--;)this.o.set(e[r],this.u=[1,0,this.u]);return t.children},x.prototype.componentDidUpdate=x.prototype.componentDidMount=function(){var n=this;this.o.forEach(function(t,e){b(n,e,t)})};var S="undefined"!=typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103,E=/^(?:accent|alignment|arabic|baseline|cap|clip(?!PathU)|color|dominant|fill|flood|font|glyph(?!R)|horiz|image(!S)|letter|lighting|marker(?!H|W|U)|overline|paint|pointer|shape|stop|strikethrough|stroke|text(?!L)|transform|underline|unicode|units|v|vector|vert|word|writing|x(?!C))[A-Z]/,C=/^on(Ani|Tra|Tou|BeforeInp|Compo)/,O=/[A-Z0-9]/g,R="undefined"!=typeof document,w=function(n){return("undefined"!=typeof Symbol&&"symbol"==typeof Symbol()?/fil|che|rad/:/fil|che|ra/).test(n)};function j(t,e,r){return null==e.__k&&(e.textContent=""),n.render(t,e),"function"==typeof r&&r(),t?t.__c:null}function I(t,e,r){return n.hydrate(t,e),"function"==typeof r&&r(),t?t.__c:null}n.Component.prototype.isReactComponent={},["componentWillMount","componentWillReceiveProps","componentWillUpdate"].forEach(function(t){Object.defineProperty(n.Component.prototype,t,{configurable:!0,get:function(){return this["UNSAFE_"+t]},set:function(n){Object.defineProperty(this,t,{configurable:!0,writable:!0,value:n})}})});var N=n.options.event;function k(){}function M(){return this.cancelBubble}function T(){return this.defaultPrevented}n.options.event=function(n){return N&&(n=N(n)),n.persist=k,n.isPropagationStopped=M,n.isDefaultPrevented=T,n.nativeEvent=n};var A,D={enumerable:!1,configurable:!0,get:function(){return this.class}},L=n.options.vnode;n.options.vnode=function(t){"string"==typeof t.type&&function(t){var e=t.props,r=t.type,u={},o=-1===r.indexOf("-");for(var i in e){var c=e[i];if(!("value"===i&&"defaultValue"in e&&null==c||R&&"children"===i&&"noscript"===r||"class"===i||"className"===i)){var l=i.toLowerCase();"defaultValue"===i&&"value"in e&&null==e.value?i="value":"download"===i&&!0===c?c="":"translate"===l&&"no"===c?c=!1:"o"===l[0]&&"n"===l[1]?"ondoubleclick"===l?i="ondblclick":"onchange"!==l||"input"!==r&&"textarea"!==r||w(e.type)?"onfocus"===l?i="onfocusin":"onblur"===l?i="onfocusout":C.test(i)&&(i=l):l=i="oninput":o&&E.test(i)?i=i.replace(O,"-$&").toLowerCase():null===c&&(c=void 0),"oninput"===l&&u[i=l]&&(i="oninputCapture"),u[i]=c}}"select"==r&&u.multiple&&Array.isArray(u.value)&&(u.value=n.toChildArray(e.children).forEach(function(n){n.props.selected=-1!=u.value.indexOf(n.props.value)})),"select"==r&&null!=u.defaultValue&&(u.value=n.toChildArray(e.children).forEach(function(n){n.props.selected=u.multiple?-1!=u.defaultValue.indexOf(n.props.value):u.defaultValue==n.props.value})),e.class&&!e.className?(u.class=e.class,Object.defineProperty(u,"className",D)):(e.className&&!e.class||e.class&&e.className)&&(u.class=u.className=e.className),t.props=u}(t),t.$$typeof=S,L&&L(t)};var F=n.options.__r;n.options.__r=function(n){F&&F(n),A=n.__c};var U=n.options.diffed;n.options.diffed=function(n){U&&U(n);var t=n.props,e=n.__e;null!=e&&"textarea"===n.type&&"value"in t&&t.value!==e.value&&(e.value=null==t.value?"":t.value),A=null};var V={ReactCurrentDispatcher:{current:{readContext:function(n){return A.__n[n.__c].props.value},useCallback:t.useCallback,useContext:t.useContext,useDebugValue:t.useDebugValue,useDeferredValue:K,useEffect:t.useEffect,useId:t.useId,useImperativeHandle:t.useImperativeHandle,useInsertionEffect:X,useLayoutEffect:t.useLayoutEffect,useMemo:t.useMemo,useReducer:t.useReducer,useRef:t.useRef,useState:t.useState,useSyncExternalStore:tn,useTransition:Q}}};function W(t){return n.createElement.bind(null,t)}function P(n){return!!n&&n.$$typeof===S}function z(t){return P(t)&&t.type===n.Fragment}function B(n){return!!n&&!!n.displayName&&("string"==typeof n.displayName||n.displayName instanceof String)&&n.displayName.startsWith("Memo(")}function H(t){return P(t)?n.cloneElement.apply(null,arguments):t}function q(t){return!!t.__k&&(n.render(null,t),!0)}function Z(n){return n&&(n.base||1===n.nodeType&&n)||null}var Y=function(n,t){return n(t)},$=function(n,t){return n(t)},G=n.Fragment;function J(n){n()}function K(n){return n}function Q(){return[!1,J]}var X=t.useLayoutEffect,nn=P;function tn(n,e){var r=e(),u=t.useState({p:{__:r,h:e}}),o=u[0].p,i=u[1];return t.useLayoutEffect(function(){o.__=r,o.h=e,en(o)&&i({p:o})},[n,r,e]),t.useEffect(function(){return en(o)&&i({p:o}),n(function(){en(o)&&i({p:o})})},[n]),r}function en(n){var t,e,r=n.h,u=n.__;try{var o=r();return!((t=u)===(e=o)&&(0!==t||1/t==1/e)||t!=t&&e!=e)}catch(n){return!0}}var rn={useState:t.useState,useId:t.useId,useReducer:t.useReducer,useEffect:t.useEffect,useLayoutEffect:t.useLayoutEffect,useInsertionEffect:X,useTransition:Q,useDeferredValue:K,useSyncExternalStore:tn,startTransition:J,useRef:t.useRef,useImperativeHandle:t.useImperativeHandle,useMemo:t.useMemo,useCallback:t.useCallback,useContext:t.useContext,useDebugValue:t.useDebugValue,version:"18.3.1",Children:f,render:j,hydrate:I,unmountComponentAtNode:q,createPortal:g,createElement:n.createElement,createContext:n.createContext,createFactory:W,cloneElement:H,createRef:n.createRef,Fragment:n.Fragment,isValidElement:P,isElement:nn,isFragment:z,isMemo:B,findDOMNode:Z,Component:n.Component,PureComponent:r,memo:u,forwardRef:c,flushSync:$,unstable_batchedUpdates:Y,StrictMode:G,Suspense:v,SuspenseList:x,lazy:m,__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED:V};Object.defineProperty(exports,"Component",{enumerable:!0,get:function(){return n.Component}}),Object.defineProperty(exports,"Fragment",{enumerable:!0,get:function(){return n.Fragment}}),Object.defineProperty(exports,"createContext",{enumerable:!0,get:function(){return n.createContext}}),Object.defineProperty(exports,"createElement",{enumerable:!0,get:function(){return n.createElement}}),Object.defineProperty(exports,"createRef",{enumerable:!0,get:function(){return n.createRef}}),exports.Children=f,exports.PureComponent=r,exports.StrictMode=G,exports.Suspense=v,exports.SuspenseList=x,exports.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=V,exports.cloneElement=H,exports.createFactory=W,exports.createPortal=g,exports.default=rn,exports.findDOMNode=Z,exports.flushSync=$,exports.forwardRef=c,exports.hydrate=I,exports.isElement=nn,exports.isFragment=z,exports.isMemo=B,exports.isValidElement=P,exports.lazy=m,exports.memo=u,exports.render=j,exports.startTransition=J,exports.unmountComponentAtNode=q,exports.unstable_batchedUpdates=Y,exports.useDeferredValue=K,exports.useInsertionEffect=X,exports.useSyncExternalStore=tn,exports.useTransition=Q,exports.version="18.3.1",Object.keys(t).forEach(function(n){"default"===n||exports.hasOwnProperty(n)||Object.defineProperty(exports,n,{enumerable:!0,get:function(){return t[n]}})});
//# sourceMappingURL=compat.js.map
