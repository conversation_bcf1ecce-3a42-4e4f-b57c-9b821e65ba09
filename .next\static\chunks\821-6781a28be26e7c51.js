(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[821],{263:function(t){!function(){var e={675:function(t,e){"use strict";e.byteLength=function(t){var e=u(t),r=e[0],n=e[1];return(r+n)*3/4-n},e.toByteArray=function(t){var e,r,s=u(t),o=s[0],a=s[1],l=new i((o+a)*3/4-a),c=0,h=a>0?o-4:o;for(r=0;r<h;r+=4)e=n[t.charCodeAt(r)]<<18|n[t.charCodeAt(r+1)]<<12|n[t.charCodeAt(r+2)]<<6|n[t.charCodeAt(r+3)],l[c++]=e>>16&255,l[c++]=e>>8&255,l[c++]=255&e;return 2===a&&(e=n[t.charCodeAt(r)]<<2|n[t.charCodeAt(r+1)]>>4,l[c++]=255&e),1===a&&(e=n[t.charCodeAt(r)]<<10|n[t.charCodeAt(r+1)]<<4|n[t.charCodeAt(r+2)]>>2,l[c++]=e>>8&255,l[c++]=255&e),l},e.fromByteArray=function(t){for(var e,n=t.length,i=n%3,s=[],o=0,a=n-i;o<a;o+=16383)s.push(function(t,e,n){for(var i,s=[],o=e;o<n;o+=3)s.push(r[(i=(t[o]<<16&16711680)+(t[o+1]<<8&65280)+(255&t[o+2]))>>18&63]+r[i>>12&63]+r[i>>6&63]+r[63&i]);return s.join("")}(t,o,o+16383>a?a:o+16383));return 1===i?s.push(r[(e=t[n-1])>>2]+r[e<<4&63]+"=="):2===i&&s.push(r[(e=(t[n-2]<<8)+t[n-1])>>10]+r[e>>4&63]+r[e<<2&63]+"="),s.join("")};for(var r=[],n=[],i="undefined"!=typeof Uint8Array?Uint8Array:Array,s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",o=0,a=s.length;o<a;++o)r[o]=s[o],n[s.charCodeAt(o)]=o;function u(t){var e=t.length;if(e%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=t.indexOf("=");-1===r&&(r=e);var n=r===e?0:4-r%4;return[r,n]}n["-".charCodeAt(0)]=62,n["_".charCodeAt(0)]=63},72:function(t,e,r){"use strict";/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */var n=r(675),i=r(783),s="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function o(t){if(t>**********)throw RangeError('The value "'+t+'" is invalid for option "size"');var e=new Uint8Array(t);return Object.setPrototypeOf(e,a.prototype),e}function a(t,e,r){if("number"==typeof t){if("string"==typeof e)throw TypeError('The "string" argument must be of type string. Received type number');return c(t)}return u(t,e,r)}function u(t,e,r){if("string"==typeof t)return function(t,e){if(("string"!=typeof e||""===e)&&(e="utf8"),!a.isEncoding(e))throw TypeError("Unknown encoding: "+e);var r=0|p(t,e),n=o(r),i=n.write(t,e);return i!==r&&(n=n.slice(0,i)),n}(t,e);if(ArrayBuffer.isView(t))return h(t);if(null==t)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t);if(x(t,ArrayBuffer)||t&&x(t.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(x(t,SharedArrayBuffer)||t&&x(t.buffer,SharedArrayBuffer)))return function(t,e,r){var n;if(e<0||t.byteLength<e)throw RangeError('"offset" is outside of buffer bounds');if(t.byteLength<e+(r||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(n=void 0===e&&void 0===r?new Uint8Array(t):void 0===r?new Uint8Array(t,e):new Uint8Array(t,e,r),a.prototype),n}(t,e,r);if("number"==typeof t)throw TypeError('The "value" argument must not be of type number. Received type number');var n=t.valueOf&&t.valueOf();if(null!=n&&n!==t)return a.from(n,e,r);var i=function(t){if(a.isBuffer(t)){var e,r=0|f(t.length),n=o(r);return 0===n.length||t.copy(n,0,0,r),n}return void 0!==t.length?"number"!=typeof t.length||(e=t.length)!=e?o(0):h(t):"Buffer"===t.type&&Array.isArray(t.data)?h(t.data):void 0}(t);if(i)return i;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof t[Symbol.toPrimitive])return a.from(t[Symbol.toPrimitive]("string"),e,r);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof t)}function l(t){if("number"!=typeof t)throw TypeError('"size" argument must be of type number');if(t<0)throw RangeError('The value "'+t+'" is invalid for option "size"')}function c(t){return l(t),o(t<0?0:0|f(t))}function h(t){for(var e=t.length<0?0:0|f(t.length),r=o(e),n=0;n<e;n+=1)r[n]=255&t[n];return r}function f(t){if(t>=**********)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|t}function p(t,e){if(a.isBuffer(t))return t.length;if(ArrayBuffer.isView(t)||x(t,ArrayBuffer))return t.byteLength;if("string"!=typeof t)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof t);var r=t.length,n=arguments.length>2&&!0===arguments[2];if(!n&&0===r)return 0;for(var i=!1;;)switch(e){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return R(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return T(t).length;default:if(i)return n?-1:R(t).length;e=(""+e).toLowerCase(),i=!0}}function d(t,e,r){var i,s,o=!1;if((void 0===e||e<0)&&(e=0),e>this.length||((void 0===r||r>this.length)&&(r=this.length),r<=0||(r>>>=0)<=(e>>>=0)))return"";for(t||(t="utf8");;)switch(t){case"hex":return function(t,e,r){var n=t.length;(!e||e<0)&&(e=0),(!r||r<0||r>n)&&(r=n);for(var i="",s=e;s<r;++s)i+=C[t[s]];return i}(this,e,r);case"utf8":case"utf-8":return b(this,e,r);case"ascii":return function(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(127&t[i]);return n}(this,e,r);case"latin1":case"binary":return function(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(t[i]);return n}(this,e,r);case"base64":return i=e,s=r,0===i&&s===this.length?n.fromByteArray(this):n.fromByteArray(this.slice(i,s));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(t,e,r){for(var n=t.slice(e,r),i="",s=0;s<n.length;s+=2)i+=String.fromCharCode(n[s]+256*n[s+1]);return i}(this,e,r);default:if(o)throw TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),o=!0}}function y(t,e,r){var n=t[e];t[e]=t[r],t[r]=n}function g(t,e,r,n,i){var s;if(0===t.length)return -1;if("string"==typeof r?(n=r,r=0):r>**********?r=**********:r<-2147483648&&(r=-2147483648),(s=r=+r)!=s&&(r=i?0:t.length-1),r<0&&(r=t.length+r),r>=t.length){if(i)return -1;r=t.length-1}else if(r<0){if(!i)return -1;r=0}if("string"==typeof e&&(e=a.from(e,n)),a.isBuffer(e))return 0===e.length?-1:m(t,e,r,n,i);if("number"==typeof e)return(e&=255,"function"==typeof Uint8Array.prototype.indexOf)?i?Uint8Array.prototype.indexOf.call(t,e,r):Uint8Array.prototype.lastIndexOf.call(t,e,r):m(t,[e],r,n,i);throw TypeError("val must be string, number or Buffer")}function m(t,e,r,n,i){var s,o=1,a=t.length,u=e.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(t.length<2||e.length<2)return -1;o=2,a/=2,u/=2,r/=2}function l(t,e){return 1===o?t[e]:t.readUInt16BE(e*o)}if(i){var c=-1;for(s=r;s<a;s++)if(l(t,s)===l(e,-1===c?0:s-c)){if(-1===c&&(c=s),s-c+1===u)return c*o}else -1!==c&&(s-=s-c),c=-1}else for(r+u>a&&(r=a-u),s=r;s>=0;s--){for(var h=!0,f=0;f<u;f++)if(l(t,s+f)!==l(e,f)){h=!1;break}if(h)return s}return -1}function b(t,e,r){r=Math.min(t.length,r);for(var n=[],i=e;i<r;){var s,o,a,u,l=t[i],c=null,h=l>239?4:l>223?3:l>191?2:1;if(i+h<=r)switch(h){case 1:l<128&&(c=l);break;case 2:(192&(s=t[i+1]))==128&&(u=(31&l)<<6|63&s)>127&&(c=u);break;case 3:s=t[i+1],o=t[i+2],(192&s)==128&&(192&o)==128&&(u=(15&l)<<12|(63&s)<<6|63&o)>2047&&(u<55296||u>57343)&&(c=u);break;case 4:s=t[i+1],o=t[i+2],a=t[i+3],(192&s)==128&&(192&o)==128&&(192&a)==128&&(u=(15&l)<<18|(63&s)<<12|(63&o)<<6|63&a)>65535&&u<1114112&&(c=u)}null===c?(c=65533,h=1):c>65535&&(c-=65536,n.push(c>>>10&1023|55296),c=56320|1023&c),n.push(c),i+=h}return function(t){var e=t.length;if(e<=4096)return String.fromCharCode.apply(String,t);for(var r="",n=0;n<e;)r+=String.fromCharCode.apply(String,t.slice(n,n+=4096));return r}(n)}function w(t,e,r){if(t%1!=0||t<0)throw RangeError("offset is not uint");if(t+e>r)throw RangeError("Trying to access beyond buffer length")}function v(t,e,r,n,i,s){if(!a.isBuffer(t))throw TypeError('"buffer" argument must be a Buffer instance');if(e>i||e<s)throw RangeError('"value" argument is out of bounds');if(r+n>t.length)throw RangeError("Index out of range")}function E(t,e,r,n,i,s){if(r+n>t.length||r<0)throw RangeError("Index out of range")}function _(t,e,r,n,s){return e=+e,r>>>=0,s||E(t,e,r,4,34028234663852886e22,-34028234663852886e22),i.write(t,e,r,n,23,4),r+4}function A(t,e,r,n,s){return e=+e,r>>>=0,s||E(t,e,r,8,17976931348623157e292,-17976931348623157e292),i.write(t,e,r,n,52,8),r+8}e.Buffer=a,e.SlowBuffer=function(t){return+t!=t&&(t=0),a.alloc(+t)},e.INSPECT_MAX_BYTES=50,e.kMaxLength=**********,a.TYPED_ARRAY_SUPPORT=function(){try{var t=new Uint8Array(1),e={foo:function(){return 42}};return Object.setPrototypeOf(e,Uint8Array.prototype),Object.setPrototypeOf(t,e),42===t.foo()}catch(t){return!1}}(),a.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(a.prototype,"parent",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.buffer}}),Object.defineProperty(a.prototype,"offset",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.byteOffset}}),a.poolSize=8192,a.from=function(t,e,r){return u(t,e,r)},Object.setPrototypeOf(a.prototype,Uint8Array.prototype),Object.setPrototypeOf(a,Uint8Array),a.alloc=function(t,e,r){return(l(t),t<=0)?o(t):void 0!==e?"string"==typeof r?o(t).fill(e,r):o(t).fill(e):o(t)},a.allocUnsafe=function(t){return c(t)},a.allocUnsafeSlow=function(t){return c(t)},a.isBuffer=function(t){return null!=t&&!0===t._isBuffer&&t!==a.prototype},a.compare=function(t,e){if(x(t,Uint8Array)&&(t=a.from(t,t.offset,t.byteLength)),x(e,Uint8Array)&&(e=a.from(e,e.offset,e.byteLength)),!a.isBuffer(t)||!a.isBuffer(e))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(t===e)return 0;for(var r=t.length,n=e.length,i=0,s=Math.min(r,n);i<s;++i)if(t[i]!==e[i]){r=t[i],n=e[i];break}return r<n?-1:n<r?1:0},a.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},a.concat=function(t,e){if(!Array.isArray(t))throw TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return a.alloc(0);if(void 0===e)for(r=0,e=0;r<t.length;++r)e+=t[r].length;var r,n=a.allocUnsafe(e),i=0;for(r=0;r<t.length;++r){var s=t[r];if(x(s,Uint8Array)&&(s=a.from(s)),!a.isBuffer(s))throw TypeError('"list" argument must be an Array of Buffers');s.copy(n,i),i+=s.length}return n},a.byteLength=p,a.prototype._isBuffer=!0,a.prototype.swap16=function(){var t=this.length;if(t%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(var e=0;e<t;e+=2)y(this,e,e+1);return this},a.prototype.swap32=function(){var t=this.length;if(t%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(var e=0;e<t;e+=4)y(this,e,e+3),y(this,e+1,e+2);return this},a.prototype.swap64=function(){var t=this.length;if(t%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(var e=0;e<t;e+=8)y(this,e,e+7),y(this,e+1,e+6),y(this,e+2,e+5),y(this,e+3,e+4);return this},a.prototype.toString=function(){var t=this.length;return 0===t?"":0==arguments.length?b(this,0,t):d.apply(this,arguments)},a.prototype.toLocaleString=a.prototype.toString,a.prototype.equals=function(t){if(!a.isBuffer(t))throw TypeError("Argument must be a Buffer");return this===t||0===a.compare(this,t)},a.prototype.inspect=function(){var t="",r=e.INSPECT_MAX_BYTES;return t=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(t+=" ... "),"<Buffer "+t+">"},s&&(a.prototype[s]=a.prototype.inspect),a.prototype.compare=function(t,e,r,n,i){if(x(t,Uint8Array)&&(t=a.from(t,t.offset,t.byteLength)),!a.isBuffer(t))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof t);if(void 0===e&&(e=0),void 0===r&&(r=t?t.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),e<0||r>t.length||n<0||i>this.length)throw RangeError("out of range index");if(n>=i&&e>=r)return 0;if(n>=i)return -1;if(e>=r)return 1;if(e>>>=0,r>>>=0,n>>>=0,i>>>=0,this===t)return 0;for(var s=i-n,o=r-e,u=Math.min(s,o),l=this.slice(n,i),c=t.slice(e,r),h=0;h<u;++h)if(l[h]!==c[h]){s=l[h],o=c[h];break}return s<o?-1:o<s?1:0},a.prototype.includes=function(t,e,r){return -1!==this.indexOf(t,e,r)},a.prototype.indexOf=function(t,e,r){return g(this,t,e,r,!0)},a.prototype.lastIndexOf=function(t,e,r){return g(this,t,e,r,!1)},a.prototype.write=function(t,e,r,n){if(void 0===e)n="utf8",r=this.length,e=0;else if(void 0===r&&"string"==typeof e)n=e,r=this.length,e=0;else if(isFinite(e))e>>>=0,isFinite(r)?(r>>>=0,void 0===n&&(n="utf8")):(n=r,r=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var i,s,o,a,u,l,c,h,f,p,d,y,g=this.length-e;if((void 0===r||r>g)&&(r=g),t.length>0&&(r<0||e<0)||e>this.length)throw RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var m=!1;;)switch(n){case"hex":return function(t,e,r,n){r=Number(r)||0;var i=t.length-r;n?(n=Number(n))>i&&(n=i):n=i;var s=e.length;n>s/2&&(n=s/2);for(var o=0;o<n;++o){var a=parseInt(e.substr(2*o,2),16);if(a!=a)break;t[r+o]=a}return o}(this,t,e,r);case"utf8":case"utf-8":return u=e,l=r,S(R(t,this.length-u),this,u,l);case"ascii":return c=e,h=r,S(k(t),this,c,h);case"latin1":case"binary":return i=this,s=t,o=e,a=r,S(k(s),i,o,a);case"base64":return f=e,p=r,S(T(t),this,f,p);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return d=e,y=r,S(function(t,e){for(var r,n,i=[],s=0;s<t.length&&!((e-=2)<0);++s)n=(r=t.charCodeAt(s))>>8,i.push(r%256),i.push(n);return i}(t,this.length-d),this,d,y);default:if(m)throw TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),m=!0}},a.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},a.prototype.slice=function(t,e){var r=this.length;t=~~t,e=void 0===e?r:~~e,t<0?(t+=r)<0&&(t=0):t>r&&(t=r),e<0?(e+=r)<0&&(e=0):e>r&&(e=r),e<t&&(e=t);var n=this.subarray(t,e);return Object.setPrototypeOf(n,a.prototype),n},a.prototype.readUIntLE=function(t,e,r){t>>>=0,e>>>=0,r||w(t,e,this.length);for(var n=this[t],i=1,s=0;++s<e&&(i*=256);)n+=this[t+s]*i;return n},a.prototype.readUIntBE=function(t,e,r){t>>>=0,e>>>=0,r||w(t,e,this.length);for(var n=this[t+--e],i=1;e>0&&(i*=256);)n+=this[t+--e]*i;return n},a.prototype.readUInt8=function(t,e){return t>>>=0,e||w(t,1,this.length),this[t]},a.prototype.readUInt16LE=function(t,e){return t>>>=0,e||w(t,2,this.length),this[t]|this[t+1]<<8},a.prototype.readUInt16BE=function(t,e){return t>>>=0,e||w(t,2,this.length),this[t]<<8|this[t+1]},a.prototype.readUInt32LE=function(t,e){return t>>>=0,e||w(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},a.prototype.readUInt32BE=function(t,e){return t>>>=0,e||w(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},a.prototype.readIntLE=function(t,e,r){t>>>=0,e>>>=0,r||w(t,e,this.length);for(var n=this[t],i=1,s=0;++s<e&&(i*=256);)n+=this[t+s]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*e)),n},a.prototype.readIntBE=function(t,e,r){t>>>=0,e>>>=0,r||w(t,e,this.length);for(var n=e,i=1,s=this[t+--n];n>0&&(i*=256);)s+=this[t+--n]*i;return s>=(i*=128)&&(s-=Math.pow(2,8*e)),s},a.prototype.readInt8=function(t,e){return(t>>>=0,e||w(t,1,this.length),128&this[t])?-((255-this[t]+1)*1):this[t]},a.prototype.readInt16LE=function(t,e){t>>>=0,e||w(t,2,this.length);var r=this[t]|this[t+1]<<8;return 32768&r?4294901760|r:r},a.prototype.readInt16BE=function(t,e){t>>>=0,e||w(t,2,this.length);var r=this[t+1]|this[t]<<8;return 32768&r?4294901760|r:r},a.prototype.readInt32LE=function(t,e){return t>>>=0,e||w(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},a.prototype.readInt32BE=function(t,e){return t>>>=0,e||w(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},a.prototype.readFloatLE=function(t,e){return t>>>=0,e||w(t,4,this.length),i.read(this,t,!0,23,4)},a.prototype.readFloatBE=function(t,e){return t>>>=0,e||w(t,4,this.length),i.read(this,t,!1,23,4)},a.prototype.readDoubleLE=function(t,e){return t>>>=0,e||w(t,8,this.length),i.read(this,t,!0,52,8)},a.prototype.readDoubleBE=function(t,e){return t>>>=0,e||w(t,8,this.length),i.read(this,t,!1,52,8)},a.prototype.writeUIntLE=function(t,e,r,n){if(t=+t,e>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;v(this,t,e,r,i,0)}var s=1,o=0;for(this[e]=255&t;++o<r&&(s*=256);)this[e+o]=t/s&255;return e+r},a.prototype.writeUIntBE=function(t,e,r,n){if(t=+t,e>>>=0,r>>>=0,!n){var i=Math.pow(2,8*r)-1;v(this,t,e,r,i,0)}var s=r-1,o=1;for(this[e+s]=255&t;--s>=0&&(o*=256);)this[e+s]=t/o&255;return e+r},a.prototype.writeUInt8=function(t,e,r){return t=+t,e>>>=0,r||v(this,t,e,1,255,0),this[e]=255&t,e+1},a.prototype.writeUInt16LE=function(t,e,r){return t=+t,e>>>=0,r||v(this,t,e,2,65535,0),this[e]=255&t,this[e+1]=t>>>8,e+2},a.prototype.writeUInt16BE=function(t,e,r){return t=+t,e>>>=0,r||v(this,t,e,2,65535,0),this[e]=t>>>8,this[e+1]=255&t,e+2},a.prototype.writeUInt32LE=function(t,e,r){return t=+t,e>>>=0,r||v(this,t,e,4,4294967295,0),this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t,e+4},a.prototype.writeUInt32BE=function(t,e,r){return t=+t,e>>>=0,r||v(this,t,e,4,4294967295,0),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},a.prototype.writeIntLE=function(t,e,r,n){if(t=+t,e>>>=0,!n){var i=Math.pow(2,8*r-1);v(this,t,e,r,i-1,-i)}var s=0,o=1,a=0;for(this[e]=255&t;++s<r&&(o*=256);)t<0&&0===a&&0!==this[e+s-1]&&(a=1),this[e+s]=(t/o>>0)-a&255;return e+r},a.prototype.writeIntBE=function(t,e,r,n){if(t=+t,e>>>=0,!n){var i=Math.pow(2,8*r-1);v(this,t,e,r,i-1,-i)}var s=r-1,o=1,a=0;for(this[e+s]=255&t;--s>=0&&(o*=256);)t<0&&0===a&&0!==this[e+s+1]&&(a=1),this[e+s]=(t/o>>0)-a&255;return e+r},a.prototype.writeInt8=function(t,e,r){return t=+t,e>>>=0,r||v(this,t,e,1,127,-128),t<0&&(t=255+t+1),this[e]=255&t,e+1},a.prototype.writeInt16LE=function(t,e,r){return t=+t,e>>>=0,r||v(this,t,e,2,32767,-32768),this[e]=255&t,this[e+1]=t>>>8,e+2},a.prototype.writeInt16BE=function(t,e,r){return t=+t,e>>>=0,r||v(this,t,e,2,32767,-32768),this[e]=t>>>8,this[e+1]=255&t,e+2},a.prototype.writeInt32LE=function(t,e,r){return t=+t,e>>>=0,r||v(this,t,e,4,**********,-2147483648),this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24,e+4},a.prototype.writeInt32BE=function(t,e,r){return t=+t,e>>>=0,r||v(this,t,e,4,**********,-2147483648),t<0&&(t=4294967295+t+1),this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t,e+4},a.prototype.writeFloatLE=function(t,e,r){return _(this,t,e,!0,r)},a.prototype.writeFloatBE=function(t,e,r){return _(this,t,e,!1,r)},a.prototype.writeDoubleLE=function(t,e,r){return A(this,t,e,!0,r)},a.prototype.writeDoubleBE=function(t,e,r){return A(this,t,e,!1,r)},a.prototype.copy=function(t,e,r,n){if(!a.isBuffer(t))throw TypeError("argument should be a Buffer");if(r||(r=0),n||0===n||(n=this.length),e>=t.length&&(e=t.length),e||(e=0),n>0&&n<r&&(n=r),n===r||0===t.length||0===this.length)return 0;if(e<0)throw RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw RangeError("Index out of range");if(n<0)throw RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),t.length-e<n-r&&(n=t.length-e+r);var i=n-r;if(this===t&&"function"==typeof Uint8Array.prototype.copyWithin)this.copyWithin(e,r,n);else if(this===t&&r<e&&e<n)for(var s=i-1;s>=0;--s)t[s+e]=this[s+r];else Uint8Array.prototype.set.call(t,this.subarray(r,n),e);return i},a.prototype.fill=function(t,e,r,n){if("string"==typeof t){if("string"==typeof e?(n=e,e=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),void 0!==n&&"string"!=typeof n)throw TypeError("encoding must be a string");if("string"==typeof n&&!a.isEncoding(n))throw TypeError("Unknown encoding: "+n);if(1===t.length){var i,s=t.charCodeAt(0);("utf8"===n&&s<128||"latin1"===n)&&(t=s)}}else"number"==typeof t?t&=255:"boolean"==typeof t&&(t=Number(t));if(e<0||this.length<e||this.length<r)throw RangeError("Out of range index");if(r<=e)return this;if(e>>>=0,r=void 0===r?this.length:r>>>0,t||(t=0),"number"==typeof t)for(i=e;i<r;++i)this[i]=t;else{var o=a.isBuffer(t)?t:a.from(t,n),u=o.length;if(0===u)throw TypeError('The value "'+t+'" is invalid for argument "value"');for(i=0;i<r-e;++i)this[i+e]=o[i%u]}return this};var O=/[^+/0-9A-Za-z-_]/g;function R(t,e){e=e||1/0;for(var r,n=t.length,i=null,s=[],o=0;o<n;++o){if((r=t.charCodeAt(o))>55295&&r<57344){if(!i){if(r>56319||o+1===n){(e-=3)>-1&&s.push(239,191,189);continue}i=r;continue}if(r<56320){(e-=3)>-1&&s.push(239,191,189),i=r;continue}r=(i-55296<<10|r-56320)+65536}else i&&(e-=3)>-1&&s.push(239,191,189);if(i=null,r<128){if((e-=1)<0)break;s.push(r)}else if(r<2048){if((e-=2)<0)break;s.push(r>>6|192,63&r|128)}else if(r<65536){if((e-=3)<0)break;s.push(r>>12|224,r>>6&63|128,63&r|128)}else if(r<1114112){if((e-=4)<0)break;s.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}else throw Error("Invalid code point")}return s}function k(t){for(var e=[],r=0;r<t.length;++r)e.push(255&t.charCodeAt(r));return e}function T(t){return n.toByteArray(function(t){if((t=(t=t.split("=")[0]).trim().replace(O,"")).length<2)return"";for(;t.length%4!=0;)t+="=";return t}(t))}function S(t,e,r,n){for(var i=0;i<n&&!(i+r>=e.length)&&!(i>=t.length);++i)e[i+r]=t[i];return i}function x(t,e){return t instanceof e||null!=t&&null!=t.constructor&&null!=t.constructor.name&&t.constructor.name===e.name}var C=function(){for(var t="0123456789abcdef",e=Array(256),r=0;r<16;++r)for(var n=16*r,i=0;i<16;++i)e[n+i]=t[r]+t[i];return e}()},783:function(t,e){/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */e.read=function(t,e,r,n,i){var s,o,a=8*i-n-1,u=(1<<a)-1,l=u>>1,c=-7,h=r?i-1:0,f=r?-1:1,p=t[e+h];for(h+=f,s=p&(1<<-c)-1,p>>=-c,c+=a;c>0;s=256*s+t[e+h],h+=f,c-=8);for(o=s&(1<<-c)-1,s>>=-c,c+=n;c>0;o=256*o+t[e+h],h+=f,c-=8);if(0===s)s=1-l;else{if(s===u)return o?NaN:1/0*(p?-1:1);o+=Math.pow(2,n),s-=l}return(p?-1:1)*o*Math.pow(2,s-n)},e.write=function(t,e,r,n,i,s){var o,a,u,l=8*s-i-1,c=(1<<l)-1,h=c>>1,f=23===i?5960464477539062e-23:0,p=n?0:s-1,d=n?1:-1,y=e<0||0===e&&1/e<0?1:0;for(isNaN(e=Math.abs(e))||e===1/0?(a=isNaN(e)?1:0,o=c):(o=Math.floor(Math.log(e)/Math.LN2),e*(u=Math.pow(2,-o))<1&&(o--,u*=2),o+h>=1?e+=f/u:e+=f*Math.pow(2,1-h),e*u>=2&&(o++,u/=2),o+h>=c?(a=0,o=c):o+h>=1?(a=(e*u-1)*Math.pow(2,i),o+=h):(a=e*Math.pow(2,h-1)*Math.pow(2,i),o=0));i>=8;t[r+p]=255&a,p+=d,a/=256,i-=8);for(o=o<<i|a,l+=i;l>0;t[r+p]=255&o,p+=d,o/=256,l-=8);t[r+p-d]|=128*y}}},r={};function n(t){var i=r[t];if(void 0!==i)return i.exports;var s=r[t]={exports:{}},o=!0;try{e[t](s,s.exports,n),o=!1}finally{o&&delete r[t]}return s.exports}n.ab="//";var i=n(72);t.exports=i}()},3445:function(t){t.exports={style:{fontFamily:"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'",fontStyle:"normal"},className:"__className_e8ce0c"}},4829:function(t,e,r){"use strict";r.d(e,{Z:function(){return eg}});var n,i,s,o,a,u,l,c,h,f,p={};function d(t,e){return function(){return t.apply(e,arguments)}}r.r(p),r.d(p,{hasBrowserEnv:function(){return tw},hasStandardBrowserEnv:function(){return tE},hasStandardBrowserWebWorkerEnv:function(){return t_},navigator:function(){return tv},origin:function(){return tA}});var y=r(2601);let{toString:g}=Object.prototype,{getPrototypeOf:m}=Object,{iterator:b,toStringTag:w}=Symbol,v=(a=Object.create(null),t=>{let e=g.call(t);return a[e]||(a[e]=e.slice(8,-1).toLowerCase())}),E=t=>(t=t.toLowerCase(),e=>v(e)===t),_=t=>e=>typeof e===t,{isArray:A}=Array,O=_("undefined");function R(t){return null!==t&&!O(t)&&null!==t.constructor&&!O(t.constructor)&&S(t.constructor.isBuffer)&&t.constructor.isBuffer(t)}let k=E("ArrayBuffer"),T=_("string"),S=_("function"),x=_("number"),C=t=>null!==t&&"object"==typeof t,B=t=>{if("object"!==v(t))return!1;let e=m(t);return(null===e||e===Object.prototype||null===Object.getPrototypeOf(e))&&!(w in t)&&!(b in t)},N=E("Date"),L=E("File"),U=E("Blob"),j=E("FileList"),P=E("URLSearchParams"),[I,D,F,q]=["ReadableStream","Request","Response","Headers"].map(E);function M(t,e,{allOwnKeys:r=!1}={}){let n,i;if(null!=t){if("object"!=typeof t&&(t=[t]),A(t))for(n=0,i=t.length;n<i;n++)e.call(null,t[n],n,t);else{let i;if(R(t))return;let s=r?Object.getOwnPropertyNames(t):Object.keys(t),o=s.length;for(n=0;n<o;n++)i=s[n],e.call(null,t[i],i,t)}}}function z(t,e){let r;if(R(t))return null;e=e.toLowerCase();let n=Object.keys(t),i=n.length;for(;i-- >0;)if(e===(r=n[i]).toLowerCase())return r;return null}let H="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,V=t=>!O(t)&&t!==H,$=(u="undefined"!=typeof Uint8Array&&m(Uint8Array),t=>u&&t instanceof u),W=E("HTMLFormElement"),J=(({hasOwnProperty:t})=>(e,r)=>t.call(e,r))(Object.prototype),K=E("RegExp"),Y=(t,e)=>{let r=Object.getOwnPropertyDescriptors(t),n={};M(r,(r,i)=>{let s;!1!==(s=e(r,i,t))&&(n[i]=s||r)}),Object.defineProperties(t,n)},X=E("AsyncFunction"),Q=(n="function"==typeof setImmediate,i=S(H.postMessage),n?setImmediate:i?(s=`axios@${Math.random()}`,o=[],H.addEventListener("message",({source:t,data:e})=>{t===H&&e===s&&o.length&&o.shift()()},!1),t=>{o.push(t),H.postMessage(s,"*")}):t=>setTimeout(t)),G="undefined"!=typeof queueMicrotask?queueMicrotask.bind(H):void 0!==y&&y.nextTick||Q;var Z={isArray:A,isArrayBuffer:k,isBuffer:R,isFormData:t=>{let e;return t&&("function"==typeof FormData&&t instanceof FormData||S(t.append)&&("formdata"===(e=v(t))||"object"===e&&S(t.toString)&&"[object FormData]"===t.toString()))},isArrayBufferView:function(t){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(t):t&&t.buffer&&k(t.buffer)},isString:T,isNumber:x,isBoolean:t=>!0===t||!1===t,isObject:C,isPlainObject:B,isEmptyObject:t=>{if(!C(t)||R(t))return!1;try{return 0===Object.keys(t).length&&Object.getPrototypeOf(t)===Object.prototype}catch(t){return!1}},isReadableStream:I,isRequest:D,isResponse:F,isHeaders:q,isUndefined:O,isDate:N,isFile:L,isBlob:U,isRegExp:K,isFunction:S,isStream:t=>C(t)&&S(t.pipe),isURLSearchParams:P,isTypedArray:$,isFileList:j,forEach:M,merge:function t(){let{caseless:e}=V(this)&&this||{},r={},n=(n,i)=>{let s=e&&z(r,i)||i;B(r[s])&&B(n)?r[s]=t(r[s],n):B(n)?r[s]=t({},n):A(n)?r[s]=n.slice():r[s]=n};for(let t=0,e=arguments.length;t<e;t++)arguments[t]&&M(arguments[t],n);return r},extend:(t,e,r,{allOwnKeys:n}={})=>(M(e,(e,n)=>{r&&S(e)?t[n]=d(e,r):t[n]=e},{allOwnKeys:n}),t),trim:t=>t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:t=>(65279===t.charCodeAt(0)&&(t=t.slice(1)),t),inherits:(t,e,r,n)=>{t.prototype=Object.create(e.prototype,n),t.prototype.constructor=t,Object.defineProperty(t,"super",{value:e.prototype}),r&&Object.assign(t.prototype,r)},toFlatObject:(t,e,r,n)=>{let i,s,o;let a={};if(e=e||{},null==t)return e;do{for(s=(i=Object.getOwnPropertyNames(t)).length;s-- >0;)o=i[s],(!n||n(o,t,e))&&!a[o]&&(e[o]=t[o],a[o]=!0);t=!1!==r&&m(t)}while(t&&(!r||r(t,e))&&t!==Object.prototype);return e},kindOf:v,kindOfTest:E,endsWith:(t,e,r)=>{t=String(t),(void 0===r||r>t.length)&&(r=t.length),r-=e.length;let n=t.indexOf(e,r);return -1!==n&&n===r},toArray:t=>{if(!t)return null;if(A(t))return t;let e=t.length;if(!x(e))return null;let r=Array(e);for(;e-- >0;)r[e]=t[e];return r},forEachEntry:(t,e)=>{let r;let n=(t&&t[b]).call(t);for(;(r=n.next())&&!r.done;){let n=r.value;e.call(t,n[0],n[1])}},matchAll:(t,e)=>{let r;let n=[];for(;null!==(r=t.exec(e));)n.push(r);return n},isHTMLForm:W,hasOwnProperty:J,hasOwnProp:J,reduceDescriptors:Y,freezeMethods:t=>{Y(t,(e,r)=>{if(S(t)&&-1!==["arguments","caller","callee"].indexOf(r))return!1;if(S(t[r])){if(e.enumerable=!1,"writable"in e){e.writable=!1;return}e.set||(e.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},toObjectSet:(t,e)=>{let r={};return(t=>{t.forEach(t=>{r[t]=!0})})(A(t)?t:String(t).split(e)),r},toCamelCase:t=>t.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(t,e,r){return e.toUpperCase()+r}),noop:()=>{},toFiniteNumber:(t,e)=>null!=t&&Number.isFinite(t=+t)?t:e,findKey:z,global:H,isContextDefined:V,isSpecCompliantForm:function(t){return!!(t&&S(t.append)&&"FormData"===t[w]&&t[b])},toJSONObject:t=>{let e=Array(10),r=(t,n)=>{if(C(t)){if(e.indexOf(t)>=0)return;if(R(t))return t;if(!("toJSON"in t)){e[n]=t;let i=A(t)?[]:{};return M(t,(t,e)=>{let s=r(t,n+1);O(s)||(i[e]=s)}),e[n]=void 0,i}}return t};return r(t,0)},isAsyncFn:X,isThenable:t=>t&&(C(t)||S(t))&&S(t.then)&&S(t.catch),setImmediate:Q,asap:G,isIterable:t=>null!=t&&S(t[b])};function tt(t,e,r,n,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=Error().stack,this.message=t,this.name="AxiosError",e&&(this.code=e),r&&(this.config=r),n&&(this.request=n),i&&(this.response=i,this.status=i.status?i.status:null)}Z.inherits(tt,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:Z.toJSONObject(this.config),code:this.code,status:this.status}}});let te=tt.prototype,tr={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(t=>{tr[t]={value:t}}),Object.defineProperties(tt,tr),Object.defineProperty(te,"isAxiosError",{value:!0}),tt.from=(t,e,r,n,i,s)=>{let o=Object.create(te);return Z.toFlatObject(t,o,function(t){return t!==Error.prototype},t=>"isAxiosError"!==t),tt.call(o,t.message,e,r,n,i),o.cause=t,o.name=t.name,s&&Object.assign(o,s),o};var tn=r(263).Buffer;function ti(t){return Z.isPlainObject(t)||Z.isArray(t)}function ts(t){return Z.endsWith(t,"[]")?t.slice(0,-2):t}function to(t,e,r){return t?t.concat(e).map(function(t,e){return t=ts(t),!r&&e?"["+t+"]":t}).join(r?".":""):e}let ta=Z.toFlatObject(Z,{},null,function(t){return/^is[A-Z]/.test(t)});var tu=function(t,e,r){if(!Z.isObject(t))throw TypeError("target must be an object");e=e||new FormData;let n=(r=Z.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(t,e){return!Z.isUndefined(e[t])})).metaTokens,i=r.visitor||l,s=r.dots,o=r.indexes,a=(r.Blob||"undefined"!=typeof Blob&&Blob)&&Z.isSpecCompliantForm(e);if(!Z.isFunction(i))throw TypeError("visitor must be a function");function u(t){if(null===t)return"";if(Z.isDate(t))return t.toISOString();if(Z.isBoolean(t))return t.toString();if(!a&&Z.isBlob(t))throw new tt("Blob is not supported. Use a Buffer instead.");return Z.isArrayBuffer(t)||Z.isTypedArray(t)?a&&"function"==typeof Blob?new Blob([t]):tn.from(t):t}function l(t,r,i){let a=t;if(t&&!i&&"object"==typeof t){if(Z.endsWith(r,"{}"))r=n?r:r.slice(0,-2),t=JSON.stringify(t);else{var l;if(Z.isArray(t)&&(l=t,Z.isArray(l)&&!l.some(ti))||(Z.isFileList(t)||Z.endsWith(r,"[]"))&&(a=Z.toArray(t)))return r=ts(r),a.forEach(function(t,n){Z.isUndefined(t)||null===t||e.append(!0===o?to([r],n,s):null===o?r:r+"[]",u(t))}),!1}}return!!ti(t)||(e.append(to(i,r,s),u(t)),!1)}let c=[],h=Object.assign(ta,{defaultVisitor:l,convertValue:u,isVisitable:ti});if(!Z.isObject(t))throw TypeError("data must be an object");return!function t(r,n){if(!Z.isUndefined(r)){if(-1!==c.indexOf(r))throw Error("Circular reference detected in "+n.join("."));c.push(r),Z.forEach(r,function(r,s){!0===(!(Z.isUndefined(r)||null===r)&&i.call(e,r,Z.isString(s)?s.trim():s,n,h))&&t(r,n?n.concat(s):[s])}),c.pop()}}(t),e};function tl(t){let e={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\x00"};return encodeURIComponent(t).replace(/[!'()~]|%20|%00/g,function(t){return e[t]})}function tc(t,e){this._pairs=[],t&&tu(t,this,e)}let th=tc.prototype;function tf(t){return encodeURIComponent(t).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function tp(t,e,r){let n;if(!e)return t;let i=r&&r.encode||tf;Z.isFunction(r)&&(r={serialize:r});let s=r&&r.serialize;if(n=s?s(e,r):Z.isURLSearchParams(e)?e.toString():new tc(e,r).toString(i)){let e=t.indexOf("#");-1!==e&&(t=t.slice(0,e)),t+=(-1===t.indexOf("?")?"?":"&")+n}return t}th.append=function(t,e){this._pairs.push([t,e])},th.toString=function(t){let e=t?function(e){return t.call(this,e,tl)}:tl;return this._pairs.map(function(t){return e(t[0])+"="+e(t[1])},"").join("&")};class td{constructor(){this.handlers=[]}use(t,e,r){return this.handlers.push({fulfilled:t,rejected:e,synchronous:!!r&&r.synchronous,runWhen:r?r.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){Z.forEach(this.handlers,function(e){null!==e&&t(e)})}}var ty={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},tg="undefined"!=typeof URLSearchParams?URLSearchParams:tc,tm="undefined"!=typeof FormData?FormData:null,tb="undefined"!=typeof Blob?Blob:null;let tw="undefined"!=typeof window&&"undefined"!=typeof document,tv="object"==typeof navigator&&navigator||void 0,tE=tw&&(!tv||0>["ReactNative","NativeScript","NS"].indexOf(tv.product)),t_="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,tA=tw&&window.location.href||"http://localhost";var tO={...p,isBrowser:!0,classes:{URLSearchParams:tg,FormData:tm,Blob:tb},protocols:["http","https","file","blob","url","data"]},tR=function(t){if(Z.isFormData(t)&&Z.isFunction(t.entries)){let e={};return Z.forEachEntry(t,(t,r)=>{!function t(e,r,n,i){let s=e[i++];if("__proto__"===s)return!0;let o=Number.isFinite(+s),a=i>=e.length;return(s=!s&&Z.isArray(n)?n.length:s,a)?Z.hasOwnProp(n,s)?n[s]=[n[s],r]:n[s]=r:(n[s]&&Z.isObject(n[s])||(n[s]=[]),t(e,r,n[s],i)&&Z.isArray(n[s])&&(n[s]=function(t){let e,r;let n={},i=Object.keys(t),s=i.length;for(e=0;e<s;e++)n[r=i[e]]=t[r];return n}(n[s]))),!o}(Z.matchAll(/\w+|\[(\w*)]/g,t).map(t=>"[]"===t[0]?"":t[1]||t[0]),r,e,0)}),e}return null};let tk={transitional:ty,adapter:["xhr","http","fetch"],transformRequest:[function(t,e){let r;let n=e.getContentType()||"",i=n.indexOf("application/json")>-1,s=Z.isObject(t);if(s&&Z.isHTMLForm(t)&&(t=new FormData(t)),Z.isFormData(t))return i?JSON.stringify(tR(t)):t;if(Z.isArrayBuffer(t)||Z.isBuffer(t)||Z.isStream(t)||Z.isFile(t)||Z.isBlob(t)||Z.isReadableStream(t))return t;if(Z.isArrayBufferView(t))return t.buffer;if(Z.isURLSearchParams(t))return e.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();if(s){if(n.indexOf("application/x-www-form-urlencoded")>-1){var o,a;return(o=t,a=this.formSerializer,tu(o,new tO.classes.URLSearchParams,{visitor:function(t,e,r,n){return tO.isNode&&Z.isBuffer(t)?(this.append(e,t.toString("base64")),!1):n.defaultVisitor.apply(this,arguments)},...a})).toString()}if((r=Z.isFileList(t))||n.indexOf("multipart/form-data")>-1){let e=this.env&&this.env.FormData;return tu(r?{"files[]":t}:t,e&&new e,this.formSerializer)}}return s||i?(e.setContentType("application/json",!1),function(t,e,r){if(Z.isString(t))try{return(0,JSON.parse)(t),Z.trim(t)}catch(t){if("SyntaxError"!==t.name)throw t}return(0,JSON.stringify)(t)}(t)):t}],transformResponse:[function(t){let e=this.transitional||tk.transitional,r=e&&e.forcedJSONParsing,n="json"===this.responseType;if(Z.isResponse(t)||Z.isReadableStream(t))return t;if(t&&Z.isString(t)&&(r&&!this.responseType||n)){let r=e&&e.silentJSONParsing;try{return JSON.parse(t)}catch(t){if(!r&&n){if("SyntaxError"===t.name)throw tt.from(t,tt.ERR_BAD_RESPONSE,this,null,this.response);throw t}}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:tO.classes.FormData,Blob:tO.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};Z.forEach(["delete","get","head","post","put","patch"],t=>{tk.headers[t]={}});let tT=Z.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]);var tS=t=>{let e,r,n;let i={};return t&&t.split("\n").forEach(function(t){n=t.indexOf(":"),e=t.substring(0,n).trim().toLowerCase(),r=t.substring(n+1).trim(),!e||i[e]&&tT[e]||("set-cookie"===e?i[e]?i[e].push(r):i[e]=[r]:i[e]=i[e]?i[e]+", "+r:r)}),i};let tx=Symbol("internals");function tC(t){return t&&String(t).trim().toLowerCase()}function tB(t){return!1===t||null==t?t:Z.isArray(t)?t.map(tB):String(t)}let tN=t=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(t.trim());function tL(t,e,r,n,i){if(Z.isFunction(n))return n.call(this,e,r);if(i&&(e=r),Z.isString(e)){if(Z.isString(n))return -1!==e.indexOf(n);if(Z.isRegExp(n))return n.test(e)}}class tU{constructor(t){t&&this.set(t)}set(t,e,r){let n=this;function i(t,e,r){let i=tC(e);if(!i)throw Error("header name must be a non-empty string");let s=Z.findKey(n,i);s&&void 0!==n[s]&&!0!==r&&(void 0!==r||!1===n[s])||(n[s||e]=tB(t))}let s=(t,e)=>Z.forEach(t,(t,r)=>i(t,r,e));if(Z.isPlainObject(t)||t instanceof this.constructor)s(t,e);else if(Z.isString(t)&&(t=t.trim())&&!tN(t))s(tS(t),e);else if(Z.isObject(t)&&Z.isIterable(t)){let r={},n,i;for(let e of t){if(!Z.isArray(e))throw TypeError("Object iterator must return a key-value pair");r[i=e[0]]=(n=r[i])?Z.isArray(n)?[...n,e[1]]:[n,e[1]]:e[1]}s(r,e)}else null!=t&&i(e,t,r);return this}get(t,e){if(t=tC(t)){let r=Z.findKey(this,t);if(r){let t=this[r];if(!e)return t;if(!0===e)return function(t){let e;let r=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;for(;e=n.exec(t);)r[e[1]]=e[2];return r}(t);if(Z.isFunction(e))return e.call(this,t,r);if(Z.isRegExp(e))return e.exec(t);throw TypeError("parser must be boolean|regexp|function")}}}has(t,e){if(t=tC(t)){let r=Z.findKey(this,t);return!!(r&&void 0!==this[r]&&(!e||tL(this,this[r],r,e)))}return!1}delete(t,e){let r=this,n=!1;function i(t){if(t=tC(t)){let i=Z.findKey(r,t);i&&(!e||tL(r,r[i],i,e))&&(delete r[i],n=!0)}}return Z.isArray(t)?t.forEach(i):i(t),n}clear(t){let e=Object.keys(this),r=e.length,n=!1;for(;r--;){let i=e[r];(!t||tL(this,this[i],i,t,!0))&&(delete this[i],n=!0)}return n}normalize(t){let e=this,r={};return Z.forEach(this,(n,i)=>{let s=Z.findKey(r,i);if(s){e[s]=tB(n),delete e[i];return}let o=t?i.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,e,r)=>e.toUpperCase()+r):String(i).trim();o!==i&&delete e[i],e[o]=tB(n),r[o]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){let e=Object.create(null);return Z.forEach(this,(r,n)=>{null!=r&&!1!==r&&(e[n]=t&&Z.isArray(r)?r.join(", "):r)}),e}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,e])=>t+": "+e).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...e){let r=new this(t);return e.forEach(t=>r.set(t)),r}static accessor(t){let e=(this[tx]=this[tx]={accessors:{}}).accessors,r=this.prototype;function n(t){let n=tC(t);e[n]||(!function(t,e){let r=Z.toCamelCase(" "+e);["get","set","has"].forEach(n=>{Object.defineProperty(t,n+r,{value:function(t,r,i){return this[n].call(this,e,t,r,i)},configurable:!0})})}(r,t),e[n]=!0)}return Z.isArray(t)?t.forEach(n):n(t),this}}function tj(t,e){let r=this||tk,n=e||r,i=tU.from(n.headers),s=n.data;return Z.forEach(t,function(t){s=t.call(r,s,i.normalize(),e?e.status:void 0)}),i.normalize(),s}function tP(t){return!!(t&&t.__CANCEL__)}function tI(t,e,r){tt.call(this,null==t?"canceled":t,tt.ERR_CANCELED,e,r),this.name="CanceledError"}function tD(t,e,r){let n=r.config.validateStatus;!r.status||!n||n(r.status)?t(r):e(new tt("Request failed with status code "+r.status,[tt.ERR_BAD_REQUEST,tt.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}tU.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),Z.reduceDescriptors(tU.prototype,({value:t},e)=>{let r=e[0].toUpperCase()+e.slice(1);return{get:()=>t,set(t){this[r]=t}}}),Z.freezeMethods(tU),Z.inherits(tI,tt,{__CANCEL__:!0});var tF=function(t,e){let r;let n=Array(t=t||10),i=Array(t),s=0,o=0;return e=void 0!==e?e:1e3,function(a){let u=Date.now(),l=i[o];r||(r=u),n[s]=a,i[s]=u;let c=o,h=0;for(;c!==s;)h+=n[c++],c%=t;if((s=(s+1)%t)===o&&(o=(o+1)%t),u-r<e)return;let f=l&&u-l;return f?Math.round(1e3*h/f):void 0}},tq=function(t,e){let r,n,i=0,s=1e3/e,o=(e,s=Date.now())=>{i=s,r=null,n&&(clearTimeout(n),n=null),t(...e)};return[(...t)=>{let e=Date.now(),a=e-i;a>=s?o(t,e):(r=t,n||(n=setTimeout(()=>{n=null,o(r)},s-a)))},()=>r&&o(r)]};let tM=(t,e,r=3)=>{let n=0,i=tF(50,250);return tq(r=>{let s=r.loaded,o=r.lengthComputable?r.total:void 0,a=s-n,u=i(a);n=s,t({loaded:s,total:o,progress:o?s/o:void 0,bytes:a,rate:u||void 0,estimated:u&&o&&s<=o?(o-s)/u:void 0,event:r,lengthComputable:null!=o,[e?"download":"upload"]:!0})},r)},tz=(t,e)=>{let r=null!=t;return[n=>e[0]({lengthComputable:r,total:t,loaded:n}),e[1]]},tH=t=>(...e)=>Z.asap(()=>t(...e));var tV=tO.hasStandardBrowserEnv?(l=new URL(tO.origin),c=tO.navigator&&/(msie|trident)/i.test(tO.navigator.userAgent),t=>(t=new URL(t,tO.origin),l.protocol===t.protocol&&l.host===t.host&&(c||l.port===t.port))):()=>!0,t$=tO.hasStandardBrowserEnv?{write(t,e,r,n,i,s){let o=[t+"="+encodeURIComponent(e)];Z.isNumber(r)&&o.push("expires="+new Date(r).toGMTString()),Z.isString(n)&&o.push("path="+n),Z.isString(i)&&o.push("domain="+i),!0===s&&o.push("secure"),document.cookie=o.join("; ")},read(t){let e=document.cookie.match(RegExp("(^|;\\s*)("+t+")=([^;]*)"));return e?decodeURIComponent(e[3]):null},remove(t){this.write(t,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function tW(t,e,r){let n=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e);return t&&(n||!1==r)?e?t.replace(/\/?\/$/,"")+"/"+e.replace(/^\/+/,""):t:e}let tJ=t=>t instanceof tU?{...t}:t;function tK(t,e){e=e||{};let r={};function n(t,e,r,n){return Z.isPlainObject(t)&&Z.isPlainObject(e)?Z.merge.call({caseless:n},t,e):Z.isPlainObject(e)?Z.merge({},e):Z.isArray(e)?e.slice():e}function i(t,e,r,i){return Z.isUndefined(e)?Z.isUndefined(t)?void 0:n(void 0,t,r,i):n(t,e,r,i)}function s(t,e){if(!Z.isUndefined(e))return n(void 0,e)}function o(t,e){return Z.isUndefined(e)?Z.isUndefined(t)?void 0:n(void 0,t):n(void 0,e)}function a(r,i,s){return s in e?n(r,i):s in t?n(void 0,r):void 0}let u={url:s,method:s,data:s,baseURL:o,transformRequest:o,transformResponse:o,paramsSerializer:o,timeout:o,timeoutMessage:o,withCredentials:o,withXSRFToken:o,adapter:o,responseType:o,xsrfCookieName:o,xsrfHeaderName:o,onUploadProgress:o,onDownloadProgress:o,decompress:o,maxContentLength:o,maxBodyLength:o,beforeRedirect:o,transport:o,httpAgent:o,httpsAgent:o,cancelToken:o,socketPath:o,responseEncoding:o,validateStatus:a,headers:(t,e,r)=>i(tJ(t),tJ(e),r,!0)};return Z.forEach(Object.keys({...t,...e}),function(n){let s=u[n]||i,o=s(t[n],e[n],n);Z.isUndefined(o)&&s!==a||(r[n]=o)}),r}var tY=t=>{let e;let r=tK({},t),{data:n,withXSRFToken:i,xsrfHeaderName:s,xsrfCookieName:o,headers:a,auth:u}=r;if(r.headers=a=tU.from(a),r.url=tp(tW(r.baseURL,r.url,r.allowAbsoluteUrls),t.params,t.paramsSerializer),u&&a.set("Authorization","Basic "+btoa((u.username||"")+":"+(u.password?unescape(encodeURIComponent(u.password)):""))),Z.isFormData(n)){if(tO.hasStandardBrowserEnv||tO.hasStandardBrowserWebWorkerEnv)a.setContentType(void 0);else if(!1!==(e=a.getContentType())){let[t,...r]=e?e.split(";").map(t=>t.trim()).filter(Boolean):[];a.setContentType([t||"multipart/form-data",...r].join("; "))}}if(tO.hasStandardBrowserEnv&&(i&&Z.isFunction(i)&&(i=i(r)),i||!1!==i&&tV(r.url))){let t=s&&o&&t$.read(o);t&&a.set(s,t)}return r},tX="undefined"!=typeof XMLHttpRequest&&function(t){return new Promise(function(e,r){let n,i,s,o,a;let u=tY(t),l=u.data,c=tU.from(u.headers).normalize(),{responseType:h,onUploadProgress:f,onDownloadProgress:p}=u;function d(){o&&o(),a&&a(),u.cancelToken&&u.cancelToken.unsubscribe(n),u.signal&&u.signal.removeEventListener("abort",n)}let y=new XMLHttpRequest;function g(){if(!y)return;let n=tU.from("getAllResponseHeaders"in y&&y.getAllResponseHeaders());tD(function(t){e(t),d()},function(t){r(t),d()},{data:h&&"text"!==h&&"json"!==h?y.response:y.responseText,status:y.status,statusText:y.statusText,headers:n,config:t,request:y}),y=null}y.open(u.method.toUpperCase(),u.url,!0),y.timeout=u.timeout,"onloadend"in y?y.onloadend=g:y.onreadystatechange=function(){y&&4===y.readyState&&(0!==y.status||y.responseURL&&0===y.responseURL.indexOf("file:"))&&setTimeout(g)},y.onabort=function(){y&&(r(new tt("Request aborted",tt.ECONNABORTED,t,y)),y=null)},y.onerror=function(){r(new tt("Network Error",tt.ERR_NETWORK,t,y)),y=null},y.ontimeout=function(){let e=u.timeout?"timeout of "+u.timeout+"ms exceeded":"timeout exceeded",n=u.transitional||ty;u.timeoutErrorMessage&&(e=u.timeoutErrorMessage),r(new tt(e,n.clarifyTimeoutError?tt.ETIMEDOUT:tt.ECONNABORTED,t,y)),y=null},void 0===l&&c.setContentType(null),"setRequestHeader"in y&&Z.forEach(c.toJSON(),function(t,e){y.setRequestHeader(e,t)}),Z.isUndefined(u.withCredentials)||(y.withCredentials=!!u.withCredentials),h&&"json"!==h&&(y.responseType=u.responseType),p&&([s,a]=tM(p,!0),y.addEventListener("progress",s)),f&&y.upload&&([i,o]=tM(f),y.upload.addEventListener("progress",i),y.upload.addEventListener("loadend",o)),(u.cancelToken||u.signal)&&(n=e=>{y&&(r(!e||e.type?new tI(null,t,y):e),y.abort(),y=null)},u.cancelToken&&u.cancelToken.subscribe(n),u.signal&&(u.signal.aborted?n():u.signal.addEventListener("abort",n)));let m=function(t){let e=/^([-+\w]{1,25})(:?\/\/|:)/.exec(t);return e&&e[1]||""}(u.url);if(m&&-1===tO.protocols.indexOf(m)){r(new tt("Unsupported protocol "+m+":",tt.ERR_BAD_REQUEST,t));return}y.send(l||null)})},tQ=(t,e)=>{let{length:r}=t=t?t.filter(Boolean):[];if(e||r){let r,n=new AbortController,i=function(t){if(!r){r=!0,o();let e=t instanceof Error?t:this.reason;n.abort(e instanceof tt?e:new tI(e instanceof Error?e.message:e))}},s=e&&setTimeout(()=>{s=null,i(new tt(`timeout ${e} of ms exceeded`,tt.ETIMEDOUT))},e),o=()=>{t&&(s&&clearTimeout(s),s=null,t.forEach(t=>{t.unsubscribe?t.unsubscribe(i):t.removeEventListener("abort",i)}),t=null)};t.forEach(t=>t.addEventListener("abort",i));let{signal:a}=n;return a.unsubscribe=()=>Z.asap(o),a}};let tG=function*(t,e){let r,n=t.byteLength;if(!e||n<e){yield t;return}let i=0;for(;i<n;)r=i+e,yield t.slice(i,r),i=r},tZ=async function*(t,e){for await(let r of t0(t))yield*tG(r,e)},t0=async function*(t){if(t[Symbol.asyncIterator]){yield*t;return}let e=t.getReader();try{for(;;){let{done:t,value:r}=await e.read();if(t)break;yield r}}finally{await e.cancel()}},t1=(t,e,r,n)=>{let i;let s=tZ(t,e),o=0,a=t=>{!i&&(i=!0,n&&n(t))};return new ReadableStream({async pull(t){try{let{done:e,value:n}=await s.next();if(e){a(),t.close();return}let i=n.byteLength;if(r){let t=o+=i;r(t)}t.enqueue(new Uint8Array(n))}catch(t){throw a(t),t}},cancel:t=>(a(t),s.return())},{highWaterMark:2})},t2="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,t4=t2&&"function"==typeof ReadableStream,t6=t2&&("function"==typeof TextEncoder?(h=new TextEncoder,t=>h.encode(t)):async t=>new Uint8Array(await new Response(t).arrayBuffer())),t3=(t,...e)=>{try{return!!t(...e)}catch(t){return!1}},t5=t4&&t3(()=>{let t=!1,e=new Request(tO.origin,{body:new ReadableStream,method:"POST",get duplex(){return t=!0,"half"}}).headers.has("Content-Type");return t&&!e}),t8=t4&&t3(()=>Z.isReadableStream(new Response("").body)),t7={stream:t8&&(t=>t.body)};t2&&(f=new Response,["text","arrayBuffer","blob","formData","stream"].forEach(t=>{t7[t]||(t7[t]=Z.isFunction(f[t])?e=>e[t]():(e,r)=>{throw new tt(`Response type '${t}' is not supported`,tt.ERR_NOT_SUPPORT,r)})}));let t9=async t=>{if(null==t)return 0;if(Z.isBlob(t))return t.size;if(Z.isSpecCompliantForm(t)){let e=new Request(tO.origin,{method:"POST",body:t});return(await e.arrayBuffer()).byteLength}return Z.isArrayBufferView(t)||Z.isArrayBuffer(t)?t.byteLength:(Z.isURLSearchParams(t)&&(t+=""),Z.isString(t))?(await t6(t)).byteLength:void 0},et=async(t,e)=>{let r=Z.toFiniteNumber(t.getContentLength());return null==r?t9(e):r},ee={http:null,xhr:tX,fetch:t2&&(async t=>{let e,r,{url:n,method:i,data:s,signal:o,cancelToken:a,timeout:u,onDownloadProgress:l,onUploadProgress:c,responseType:h,headers:f,withCredentials:p="same-origin",fetchOptions:d}=tY(t);h=h?(h+"").toLowerCase():"text";let y=tQ([o,a&&a.toAbortSignal()],u),g=y&&y.unsubscribe&&(()=>{y.unsubscribe()});try{if(c&&t5&&"get"!==i&&"head"!==i&&0!==(r=await et(f,s))){let t,e=new Request(n,{method:"POST",body:s,duplex:"half"});if(Z.isFormData(s)&&(t=e.headers.get("content-type"))&&f.setContentType(t),e.body){let[t,n]=tz(r,tM(tH(c)));s=t1(e.body,65536,t,n)}}Z.isString(p)||(p=p?"include":"omit");let o="credentials"in Request.prototype;e=new Request(n,{...d,signal:y,method:i.toUpperCase(),headers:f.normalize().toJSON(),body:s,duplex:"half",credentials:o?p:void 0});let a=await fetch(e,d),u=t8&&("stream"===h||"response"===h);if(t8&&(l||u&&g)){let t={};["status","statusText","headers"].forEach(e=>{t[e]=a[e]});let e=Z.toFiniteNumber(a.headers.get("content-length")),[r,n]=l&&tz(e,tM(tH(l),!0))||[];a=new Response(t1(a.body,65536,r,()=>{n&&n(),g&&g()}),t)}h=h||"text";let m=await t7[Z.findKey(t7,h)||"text"](a,t);return!u&&g&&g(),await new Promise((r,n)=>{tD(r,n,{data:m,headers:tU.from(a.headers),status:a.status,statusText:a.statusText,config:t,request:e})})}catch(r){if(g&&g(),r&&"TypeError"===r.name&&/Load failed|fetch/i.test(r.message))throw Object.assign(new tt("Network Error",tt.ERR_NETWORK,t,e),{cause:r.cause||r});throw tt.from(r,r&&r.code,t,e)}})};Z.forEach(ee,(t,e)=>{if(t){try{Object.defineProperty(t,"name",{value:e})}catch(t){}Object.defineProperty(t,"adapterName",{value:e})}});let er=t=>`- ${t}`,en=t=>Z.isFunction(t)||null===t||!1===t;var ei={getAdapter:t=>{let e,r;let{length:n}=t=Z.isArray(t)?t:[t],i={};for(let s=0;s<n;s++){let n;if(r=e=t[s],!en(e)&&void 0===(r=ee[(n=String(e)).toLowerCase()]))throw new tt(`Unknown adapter '${n}'`);if(r)break;i[n||"#"+s]=r}if(!r){let t=Object.entries(i).map(([t,e])=>`adapter ${t} `+(!1===e?"is not supported by the environment":"is not available in the build"));throw new tt("There is no suitable adapter to dispatch the request "+(n?t.length>1?"since :\n"+t.map(er).join("\n"):" "+er(t[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return r},adapters:ee};function es(t){if(t.cancelToken&&t.cancelToken.throwIfRequested(),t.signal&&t.signal.aborted)throw new tI(null,t)}function eo(t){return es(t),t.headers=tU.from(t.headers),t.data=tj.call(t,t.transformRequest),-1!==["post","put","patch"].indexOf(t.method)&&t.headers.setContentType("application/x-www-form-urlencoded",!1),ei.getAdapter(t.adapter||tk.adapter)(t).then(function(e){return es(t),e.data=tj.call(t,t.transformResponse,e),e.headers=tU.from(e.headers),e},function(e){return!tP(e)&&(es(t),e&&e.response&&(e.response.data=tj.call(t,t.transformResponse,e.response),e.response.headers=tU.from(e.response.headers))),Promise.reject(e)})}let ea="1.11.0",eu={};["object","boolean","number","function","string","symbol"].forEach((t,e)=>{eu[t]=function(r){return typeof r===t||"a"+(e<1?"n ":" ")+t}});let el={};eu.transitional=function(t,e,r){function n(t,e){return"[Axios v"+ea+"] Transitional option '"+t+"'"+e+(r?". "+r:"")}return(r,i,s)=>{if(!1===t)throw new tt(n(i," has been removed"+(e?" in "+e:"")),tt.ERR_DEPRECATED);return e&&!el[i]&&(el[i]=!0,console.warn(n(i," has been deprecated since v"+e+" and will be removed in the near future"))),!t||t(r,i,s)}},eu.spelling=function(t){return(e,r)=>(console.warn(`${r} is likely a misspelling of ${t}`),!0)};var ec={assertOptions:function(t,e,r){if("object"!=typeof t)throw new tt("options must be an object",tt.ERR_BAD_OPTION_VALUE);let n=Object.keys(t),i=n.length;for(;i-- >0;){let s=n[i],o=e[s];if(o){let e=t[s],r=void 0===e||o(e,s,t);if(!0!==r)throw new tt("option "+s+" must be "+r,tt.ERR_BAD_OPTION_VALUE);continue}if(!0!==r)throw new tt("Unknown option "+s,tt.ERR_BAD_OPTION)}},validators:eu};let eh=ec.validators;class ef{constructor(t){this.defaults=t||{},this.interceptors={request:new td,response:new td}}async request(t,e){try{return await this._request(t,e)}catch(t){if(t instanceof Error){let e={};Error.captureStackTrace?Error.captureStackTrace(e):e=Error();let r=e.stack?e.stack.replace(/^.+\n/,""):"";try{t.stack?r&&!String(t.stack).endsWith(r.replace(/^.+\n.+\n/,""))&&(t.stack+="\n"+r):t.stack=r}catch(t){}}throw t}}_request(t,e){let r,n;"string"==typeof t?(e=e||{}).url=t:e=t||{};let{transitional:i,paramsSerializer:s,headers:o}=e=tK(this.defaults,e);void 0!==i&&ec.assertOptions(i,{silentJSONParsing:eh.transitional(eh.boolean),forcedJSONParsing:eh.transitional(eh.boolean),clarifyTimeoutError:eh.transitional(eh.boolean)},!1),null!=s&&(Z.isFunction(s)?e.paramsSerializer={serialize:s}:ec.assertOptions(s,{encode:eh.function,serialize:eh.function},!0)),void 0!==e.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?e.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:e.allowAbsoluteUrls=!0),ec.assertOptions(e,{baseUrl:eh.spelling("baseURL"),withXsrfToken:eh.spelling("withXSRFToken")},!0),e.method=(e.method||this.defaults.method||"get").toLowerCase();let a=o&&Z.merge(o.common,o[e.method]);o&&Z.forEach(["delete","get","head","post","put","patch","common"],t=>{delete o[t]}),e.headers=tU.concat(a,o);let u=[],l=!0;this.interceptors.request.forEach(function(t){("function"!=typeof t.runWhen||!1!==t.runWhen(e))&&(l=l&&t.synchronous,u.unshift(t.fulfilled,t.rejected))});let c=[];this.interceptors.response.forEach(function(t){c.push(t.fulfilled,t.rejected)});let h=0;if(!l){let t=[eo.bind(this),void 0];for(t.unshift(...u),t.push(...c),n=t.length,r=Promise.resolve(e);h<n;)r=r.then(t[h++],t[h++]);return r}n=u.length;let f=e;for(h=0;h<n;){let t=u[h++],e=u[h++];try{f=t(f)}catch(t){e.call(this,t);break}}try{r=eo.call(this,f)}catch(t){return Promise.reject(t)}for(h=0,n=c.length;h<n;)r=r.then(c[h++],c[h++]);return r}getUri(t){return tp(tW((t=tK(this.defaults,t)).baseURL,t.url,t.allowAbsoluteUrls),t.params,t.paramsSerializer)}}Z.forEach(["delete","get","head","options"],function(t){ef.prototype[t]=function(e,r){return this.request(tK(r||{},{method:t,url:e,data:(r||{}).data}))}}),Z.forEach(["post","put","patch"],function(t){function e(e){return function(r,n,i){return this.request(tK(i||{},{method:t,headers:e?{"Content-Type":"multipart/form-data"}:{},url:r,data:n}))}}ef.prototype[t]=e(),ef.prototype[t+"Form"]=e(!0)});class ep{constructor(t){let e;if("function"!=typeof t)throw TypeError("executor must be a function.");this.promise=new Promise(function(t){e=t});let r=this;this.promise.then(t=>{if(!r._listeners)return;let e=r._listeners.length;for(;e-- >0;)r._listeners[e](t);r._listeners=null}),this.promise.then=t=>{let e;let n=new Promise(t=>{r.subscribe(t),e=t}).then(t);return n.cancel=function(){r.unsubscribe(e)},n},t(function(t,n,i){r.reason||(r.reason=new tI(t,n,i),e(r.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;let e=this._listeners.indexOf(t);-1!==e&&this._listeners.splice(e,1)}toAbortSignal(){let t=new AbortController,e=e=>{t.abort(e)};return this.subscribe(e),t.signal.unsubscribe=()=>this.unsubscribe(e),t.signal}static source(){let t;return{token:new ep(function(e){t=e}),cancel:t}}}let ed={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(ed).forEach(([t,e])=>{ed[e]=t});let ey=function t(e){let r=new ef(e),n=d(ef.prototype.request,r);return Z.extend(n,ef.prototype,r,{allOwnKeys:!0}),Z.extend(n,r,null,{allOwnKeys:!0}),n.create=function(r){return t(tK(e,r))},n}(tk);ey.Axios=ef,ey.CanceledError=tI,ey.CancelToken=ep,ey.isCancel=tP,ey.VERSION=ea,ey.toFormData=tu,ey.AxiosError=tt,ey.Cancel=ey.CanceledError,ey.all=function(t){return Promise.all(t)},ey.spread=function(t){return function(e){return t.apply(null,e)}},ey.isAxiosError=function(t){return Z.isObject(t)&&!0===t.isAxiosError},ey.mergeConfig=tK,ey.AxiosHeaders=tU,ey.formToJSON=t=>tR(Z.isHTMLForm(t)?new FormData(t):t),ey.getAdapter=ei.getAdapter,ey.HttpStatusCode=ed,ey.default=ey;var eg=ey},5925:function(t,e,r){"use strict";let n,i;r.r(e),r.d(e,{CheckmarkIcon:function(){return Q},ErrorIcon:function(){return W},LoaderIcon:function(){return K},ToastBar:function(){return tu},ToastIcon:function(){return tr},Toaster:function(){return tf},default:function(){return tp},resolveValue:function(){return O},toast:function(){return q},useToaster:function(){return z},useToasterStore:function(){return I}});var s,o=r(2265);let a={data:""},u=t=>"object"==typeof window?((t?t.querySelector("#_goober"):window._goober)||Object.assign((t||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:t||a,l=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,c=/\/\*[^]*?\*\/|  +/g,h=/\n+/g,f=(t,e)=>{let r="",n="",i="";for(let s in t){let o=t[s];"@"==s[0]?"i"==s[1]?r=s+" "+o+";":n+="f"==s[1]?f(o,s):s+"{"+f(o,"k"==s[1]?"":e)+"}":"object"==typeof o?n+=f(o,e?e.replace(/([^,])+/g,t=>s.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,e=>/&/.test(e)?e.replace(/&/g,t):t?t+" "+e:e)):s):null!=o&&(s=/^--/.test(s)?s:s.replace(/[A-Z]/g,"-$&").toLowerCase(),i+=f.p?f.p(s,o):s+":"+o+";")}return r+(e&&i?e+"{"+i+"}":i)+n},p={},d=t=>{if("object"==typeof t){let e="";for(let r in t)e+=r+d(t[r]);return e}return t},y=(t,e,r,n,i)=>{var s;let o=d(t),a=p[o]||(p[o]=(t=>{let e=0,r=11;for(;e<t.length;)r=101*r+t.charCodeAt(e++)>>>0;return"go"+r})(o));if(!p[a]){let e=o!==t?t:(t=>{let e,r,n=[{}];for(;e=l.exec(t.replace(c,""));)e[4]?n.shift():e[3]?(r=e[3].replace(h," ").trim(),n.unshift(n[0][r]=n[0][r]||{})):n[0][e[1]]=e[2].replace(h," ").trim();return n[0]})(t);p[a]=f(i?{["@keyframes "+a]:e}:e,r?"":"."+a)}let u=r&&p.g?p.g:null;return r&&(p.g=p[a]),s=p[a],u?e.data=e.data.replace(u,s):-1===e.data.indexOf(s)&&(e.data=n?s+e.data:e.data+s),a},g=(t,e,r)=>t.reduce((t,n,i)=>{let s=e[i];if(s&&s.call){let t=s(r),e=t&&t.props&&t.props.className||/^go/.test(t)&&t;s=e?"."+e:t&&"object"==typeof t?t.props?"":f(t,""):!1===t?"":t}return t+n+(null==s?"":s)},"");function m(t){let e=this||{},r=t.call?t(e.p):t;return y(r.unshift?r.raw?g(r,[].slice.call(arguments,1),e.p):r.reduce((t,r)=>Object.assign(t,r&&r.call?r(e.p):r),{}):r,u(e.target),e.g,e.o,e.k)}m.bind({g:1});let b,w,v,E=m.bind({k:1});function _(t,e){let r=this||{};return function(){let n=arguments;function i(s,o){let a=Object.assign({},s),u=a.className||i.className;r.p=Object.assign({theme:w&&w()},a),r.o=/ *go\d+/.test(u),a.className=m.apply(r,n)+(u?" "+u:""),e&&(a.ref=o);let l=t;return t[0]&&(l=a.as||t,delete a.as),v&&l[0]&&v(a),b(l,a)}return e?e(i):i}}var A=t=>"function"==typeof t,O=(t,e)=>A(t)?t(e):t,R=(n=0,()=>(++n).toString()),k=()=>{if(void 0===i&&"u">typeof window){let t=matchMedia("(prefers-reduced-motion: reduce)");i=!t||t.matches}return i},T="default",S=(t,e)=>{let{toastLimit:r}=t.settings;switch(e.type){case 0:return{...t,toasts:[e.toast,...t.toasts].slice(0,r)};case 1:return{...t,toasts:t.toasts.map(t=>t.id===e.toast.id?{...t,...e.toast}:t)};case 2:let{toast:n}=e;return S(t,{type:t.toasts.find(t=>t.id===n.id)?1:0,toast:n});case 3:let{toastId:i}=e;return{...t,toasts:t.toasts.map(t=>t.id===i||void 0===i?{...t,dismissed:!0,visible:!1}:t)};case 4:return void 0===e.toastId?{...t,toasts:[]}:{...t,toasts:t.toasts.filter(t=>t.id!==e.toastId)};case 5:return{...t,pausedAt:e.time};case 6:let s=e.time-(t.pausedAt||0);return{...t,pausedAt:void 0,toasts:t.toasts.map(t=>({...t,pauseDuration:t.pauseDuration+s}))}}},x=[],C={toasts:[],pausedAt:void 0,settings:{toastLimit:20}},B={},N=(t,e=T)=>{B[e]=S(B[e]||C,t),x.forEach(([t,r])=>{t===e&&r(B[e])})},L=t=>Object.keys(B).forEach(e=>N(t,e)),U=t=>Object.keys(B).find(e=>B[e].toasts.some(e=>e.id===t)),j=(t=T)=>e=>{N(e,t)},P={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},I=(t={},e=T)=>{let[r,n]=(0,o.useState)(B[e]||C),i=(0,o.useRef)(B[e]);(0,o.useEffect)(()=>(i.current!==B[e]&&n(B[e]),x.push([e,n]),()=>{let t=x.findIndex(([t])=>t===e);t>-1&&x.splice(t,1)}),[e]);let s=r.toasts.map(e=>{var r,n,i;return{...t,...t[e.type],...e,removeDelay:e.removeDelay||(null==(r=t[e.type])?void 0:r.removeDelay)||(null==t?void 0:t.removeDelay),duration:e.duration||(null==(n=t[e.type])?void 0:n.duration)||(null==t?void 0:t.duration)||P[e.type],style:{...t.style,...null==(i=t[e.type])?void 0:i.style,...e.style}}});return{...r,toasts:s}},D=(t,e="blank",r)=>({createdAt:Date.now(),visible:!0,dismissed:!1,type:e,ariaProps:{role:"status","aria-live":"polite"},message:t,pauseDuration:0,...r,id:(null==r?void 0:r.id)||R()}),F=t=>(e,r)=>{let n=D(e,t,r);return j(n.toasterId||U(n.id))({type:2,toast:n}),n.id},q=(t,e)=>F("blank")(t,e);q.error=F("error"),q.success=F("success"),q.loading=F("loading"),q.custom=F("custom"),q.dismiss=(t,e)=>{let r={type:3,toastId:t};e?j(e)(r):L(r)},q.dismissAll=t=>q.dismiss(void 0,t),q.remove=(t,e)=>{let r={type:4,toastId:t};e?j(e)(r):L(r)},q.removeAll=t=>q.remove(void 0,t),q.promise=(t,e,r)=>{let n=q.loading(e.loading,{...r,...null==r?void 0:r.loading});return"function"==typeof t&&(t=t()),t.then(t=>{let i=e.success?O(e.success,t):void 0;return i?q.success(i,{id:n,...r,...null==r?void 0:r.success}):q.dismiss(n),t}).catch(t=>{let i=e.error?O(e.error,t):void 0;i?q.error(i,{id:n,...r,...null==r?void 0:r.error}):q.dismiss(n)}),t};var M=1e3,z=(t,e="default")=>{let{toasts:r,pausedAt:n}=I(t,e),i=(0,o.useRef)(new Map).current,s=(0,o.useCallback)((t,e=M)=>{if(i.has(t))return;let r=setTimeout(()=>{i.delete(t),a({type:4,toastId:t})},e);i.set(t,r)},[]);(0,o.useEffect)(()=>{if(n)return;let t=Date.now(),i=r.map(r=>{if(r.duration===1/0)return;let n=(r.duration||0)+r.pauseDuration-(t-r.createdAt);if(n<0){r.visible&&q.dismiss(r.id);return}return setTimeout(()=>q.dismiss(r.id,e),n)});return()=>{i.forEach(t=>t&&clearTimeout(t))}},[r,n,e]);let a=(0,o.useCallback)(j(e),[e]),u=(0,o.useCallback)(()=>{a({type:5,time:Date.now()})},[a]),l=(0,o.useCallback)((t,e)=>{a({type:1,toast:{id:t,height:e}})},[a]),c=(0,o.useCallback)(()=>{n&&a({type:6,time:Date.now()})},[n,a]),h=(0,o.useCallback)((t,e)=>{let{reverseOrder:n=!1,gutter:i=8,defaultPosition:s}=e||{},o=r.filter(e=>(e.position||s)===(t.position||s)&&e.height),a=o.findIndex(e=>e.id===t.id),u=o.filter((t,e)=>e<a&&t.visible).length;return o.filter(t=>t.visible).slice(...n?[u+1]:[0,u]).reduce((t,e)=>t+(e.height||0)+i,0)},[r]);return(0,o.useEffect)(()=>{r.forEach(t=>{if(t.dismissed)s(t.id,t.removeDelay);else{let e=i.get(t.id);e&&(clearTimeout(e),i.delete(t.id))}})},[r,s]),{toasts:r,handlers:{updateHeight:l,startPause:u,endPause:c,calculateOffset:h}}},H=E`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,V=E`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,$=E`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,W=_("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${t=>t.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${H} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${V} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${t=>t.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${$} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,J=E`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,K=_("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${t=>t.secondary||"#e0e0e0"};
  border-right-color: ${t=>t.primary||"#616161"};
  animation: ${J} 1s linear infinite;
`,Y=E`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,X=E`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,Q=_("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${t=>t.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Y} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${X} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${t=>t.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,G=_("div")`
  position: absolute;
`,Z=_("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,tt=E`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,te=_("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${tt} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,tr=({toast:t})=>{let{icon:e,type:r,iconTheme:n}=t;return void 0!==e?"string"==typeof e?o.createElement(te,null,e):e:"blank"===r?null:o.createElement(Z,null,o.createElement(K,{...n}),"loading"!==r&&o.createElement(G,null,"error"===r?o.createElement(W,{...n}):o.createElement(Q,{...n})))},tn=t=>`
0% {transform: translate3d(0,${-200*t}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,ti=t=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${-150*t}%,-1px) scale(.6); opacity:0;}
`,ts=_("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,to=_("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,ta=(t,e)=>{let r=t.includes("top")?1:-1,[n,i]=k()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[tn(r),ti(r)];return{animation:e?`${E(n)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${E(i)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},tu=o.memo(({toast:t,position:e,style:r,children:n})=>{let i=t.height?ta(t.position||e||"top-center",t.visible):{opacity:0},s=o.createElement(tr,{toast:t}),a=o.createElement(to,{...t.ariaProps},O(t.message,t));return o.createElement(ts,{className:t.className,style:{...i,...r,...t.style}},"function"==typeof n?n({icon:s,message:a}):o.createElement(o.Fragment,null,s,a))});s=o.createElement,f.p=void 0,b=s,w=void 0,v=void 0;var tl=({id:t,className:e,style:r,onHeightUpdate:n,children:i})=>{let s=o.useCallback(e=>{if(e){let r=()=>{n(t,e.getBoundingClientRect().height)};r(),new MutationObserver(r).observe(e,{subtree:!0,childList:!0,characterData:!0})}},[t,n]);return o.createElement("div",{ref:s,className:e,style:r},i)},tc=(t,e)=>{let r=t.includes("top"),n=t.includes("center")?{justifyContent:"center"}:t.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:k()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${e*(r?1:-1)}px)`,...r?{top:0}:{bottom:0},...n}},th=m`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,tf=({reverseOrder:t,position:e="top-center",toastOptions:r,gutter:n,children:i,toasterId:s,containerStyle:a,containerClassName:u})=>{let{toasts:l,handlers:c}=z(r,s);return o.createElement("div",{"data-rht-toaster":s||"",style:{position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none",...a},className:u,onMouseEnter:c.startPause,onMouseLeave:c.endPause},l.map(r=>{let s=r.position||e,a=tc(s,c.calculateOffset(r,{reverseOrder:t,gutter:n,defaultPosition:e}));return o.createElement(tl,{id:r.id,key:r.id,onHeightUpdate:c.updateHeight,className:r.visible?th:"",style:a},"custom"===r.type?O(r.message,r):i?i(r):o.createElement(tu,{toast:r,position:s}))}))},tp=q},4337:function(t,e,r){"use strict";let n,i;r.d(e,{io:function(){return tk}});var s,o,a={};r.r(a),r.d(a,{Decoder:function(){return tb},Encoder:function(){return tg},PacketType:function(){return o},protocol:function(){return ty}});let u=Object.create(null);u.open="0",u.close="1",u.ping="2",u.pong="3",u.message="4",u.upgrade="5",u.noop="6";let l=Object.create(null);Object.keys(u).forEach(t=>{l[u[t]]=t});let c={type:"error",data:"parser error"},h="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===Object.prototype.toString.call(Blob),f="function"==typeof ArrayBuffer,p=t=>"function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(t):t&&t.buffer instanceof ArrayBuffer,d=({type:t,data:e},r,n)=>h&&e instanceof Blob?r?n(e):y(e,n):f&&(e instanceof ArrayBuffer||p(e))?r?n(e):y(new Blob([e]),n):n(u[t]+(e||"")),y=(t,e)=>{let r=new FileReader;return r.onload=function(){e("b"+(r.result.split(",")[1]||""))},r.readAsDataURL(t)};function g(t){return t instanceof Uint8Array?t:t instanceof ArrayBuffer?new Uint8Array(t):new Uint8Array(t.buffer,t.byteOffset,t.byteLength)}let m="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",b="undefined"==typeof Uint8Array?[]:new Uint8Array(256);for(let t=0;t<m.length;t++)b[m.charCodeAt(t)]=t;let w=t=>{let e=.75*t.length,r=t.length,n,i=0,s,o,a,u;"="===t[t.length-1]&&(e--,"="===t[t.length-2]&&e--);let l=new ArrayBuffer(e),c=new Uint8Array(l);for(n=0;n<r;n+=4)s=b[t.charCodeAt(n)],o=b[t.charCodeAt(n+1)],a=b[t.charCodeAt(n+2)],u=b[t.charCodeAt(n+3)],c[i++]=s<<2|o>>4,c[i++]=(15&o)<<4|a>>2,c[i++]=(3&a)<<6|63&u;return l},v="function"==typeof ArrayBuffer,E=(t,e)=>{if("string"!=typeof t)return{type:"message",data:A(t,e)};let r=t.charAt(0);return"b"===r?{type:"message",data:_(t.substring(1),e)}:l[r]?t.length>1?{type:l[r],data:t.substring(1)}:{type:l[r]}:c},_=(t,e)=>v?A(w(t),e):{base64:!0,data:t},A=(t,e)=>"blob"===e?t instanceof Blob?t:new Blob([t]):t instanceof ArrayBuffer?t:t.buffer,O=(t,e)=>{let r=t.length,n=Array(r),i=0;t.forEach((t,s)=>{d(t,!1,t=>{n[s]=t,++i===r&&e(n.join("\x1e"))})})},R=(t,e)=>{let r=t.split("\x1e"),n=[];for(let t=0;t<r.length;t++){let i=E(r[t],e);if(n.push(i),"error"===i.type)break}return n};function k(t){return t.reduce((t,e)=>t+e.length,0)}function T(t,e){if(t[0].length===e)return t.shift();let r=new Uint8Array(e),n=0;for(let i=0;i<e;i++)r[i]=t[0][n++],n===t[0].length&&(t.shift(),n=0);return t.length&&n<t[0].length&&(t[0]=t[0].slice(n)),r}function S(t){if(t)return function(t){for(var e in S.prototype)t[e]=S.prototype[e];return t}(t)}S.prototype.on=S.prototype.addEventListener=function(t,e){return this._callbacks=this._callbacks||{},(this._callbacks["$"+t]=this._callbacks["$"+t]||[]).push(e),this},S.prototype.once=function(t,e){function r(){this.off(t,r),e.apply(this,arguments)}return r.fn=e,this.on(t,r),this},S.prototype.off=S.prototype.removeListener=S.prototype.removeAllListeners=S.prototype.removeEventListener=function(t,e){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var r,n=this._callbacks["$"+t];if(!n)return this;if(1==arguments.length)return delete this._callbacks["$"+t],this;for(var i=0;i<n.length;i++)if((r=n[i])===e||r.fn===e){n.splice(i,1);break}return 0===n.length&&delete this._callbacks["$"+t],this},S.prototype.emit=function(t){this._callbacks=this._callbacks||{};for(var e=Array(arguments.length-1),r=this._callbacks["$"+t],n=1;n<arguments.length;n++)e[n-1]=arguments[n];if(r){r=r.slice(0);for(var n=0,i=r.length;n<i;++n)r[n].apply(this,e)}return this},S.prototype.emitReserved=S.prototype.emit,S.prototype.listeners=function(t){return this._callbacks=this._callbacks||{},this._callbacks["$"+t]||[]},S.prototype.hasListeners=function(t){return!!this.listeners(t).length};let x="function"==typeof Promise&&"function"==typeof Promise.resolve?t=>Promise.resolve().then(t):(t,e)=>e(t,0),C="undefined"!=typeof self?self:"undefined"!=typeof window?window:Function("return this")();function B(t,...e){return e.reduce((e,r)=>(t.hasOwnProperty(r)&&(e[r]=t[r]),e),{})}let N=C.setTimeout,L=C.clearTimeout;function U(t,e){e.useNativeTimers?(t.setTimeoutFn=N.bind(C),t.clearTimeoutFn=L.bind(C)):(t.setTimeoutFn=C.setTimeout.bind(C),t.clearTimeoutFn=C.clearTimeout.bind(C))}function j(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}class P extends Error{constructor(t,e,r){super(t),this.description=e,this.context=r,this.type="TransportError"}}class I extends S{constructor(t){super(),this.writable=!1,U(this,t),this.opts=t,this.query=t.query,this.socket=t.socket,this.supportsBinary=!t.forceBase64}onError(t,e,r){return super.emitReserved("error",new P(t,e,r)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return("opening"===this.readyState||"open"===this.readyState)&&(this.doClose(),this.onClose()),this}send(t){"open"===this.readyState&&this.write(t)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(t){let e=E(t,this.socket.binaryType);this.onPacket(e)}onPacket(t){super.emitReserved("packet",t)}onClose(t){this.readyState="closed",super.emitReserved("close",t)}pause(t){}createUri(t,e={}){return t+"://"+this._hostname()+this._port()+this.opts.path+this._query(e)}_hostname(){let t=this.opts.hostname;return -1===t.indexOf(":")?t:"["+t+"]"}_port(){return this.opts.port&&(this.opts.secure&&Number(443!==this.opts.port)||!this.opts.secure&&80!==Number(this.opts.port))?":"+this.opts.port:""}_query(t){let e=function(t){let e="";for(let r in t)t.hasOwnProperty(r)&&(e.length&&(e+="&"),e+=encodeURIComponent(r)+"="+encodeURIComponent(t[r]));return e}(t);return e.length?"?"+e:""}}class D extends I{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(t){this.readyState="pausing";let e=()=>{this.readyState="paused",t()};if(this._polling||!this.writable){let t=0;this._polling&&(t++,this.once("pollComplete",function(){--t||e()})),this.writable||(t++,this.once("drain",function(){--t||e()}))}else e()}_poll(){this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(t){R(t,this.socket.binaryType).forEach(t=>{if("opening"===this.readyState&&"open"===t.type&&this.onOpen(),"close"===t.type)return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(t)}),"closed"!==this.readyState&&(this._polling=!1,this.emitReserved("pollComplete"),"open"===this.readyState&&this._poll())}doClose(){let t=()=>{this.write([{type:"close"}])};"open"===this.readyState?t():this.once("open",t)}write(t){this.writable=!1,O(t,t=>{this.doWrite(t,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){let t=this.opts.secure?"https":"http",e=this.query||{};return!1!==this.opts.timestampRequests&&(e[this.opts.timestampParam]=j()),this.supportsBinary||e.sid||(e.b64=1),this.createUri(t,e)}}let F=!1;try{F="undefined"!=typeof XMLHttpRequest&&"withCredentials"in new XMLHttpRequest}catch(t){}let q=F;function M(){}class z extends D{constructor(t){if(super(t),"undefined"!=typeof location){let e="https:"===location.protocol,r=location.port;r||(r=e?"443":"80"),this.xd="undefined"!=typeof location&&t.hostname!==location.hostname||r!==t.port}}doWrite(t,e){let r=this.request({method:"POST",data:t});r.on("success",e),r.on("error",(t,e)=>{this.onError("xhr post error",t,e)})}doPoll(){let t=this.request();t.on("data",this.onData.bind(this)),t.on("error",(t,e)=>{this.onError("xhr poll error",t,e)}),this.pollXhr=t}}class H extends S{constructor(t,e,r){super(),this.createRequest=t,U(this,r),this._opts=r,this._method=r.method||"GET",this._uri=e,this._data=void 0!==r.data?r.data:null,this._create()}_create(){var t;let e=B(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");e.xdomain=!!this._opts.xd;let r=this._xhr=this.createRequest(e);try{r.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders)for(let t in r.setDisableHeaderCheck&&r.setDisableHeaderCheck(!0),this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(t)&&r.setRequestHeader(t,this._opts.extraHeaders[t])}catch(t){}if("POST"===this._method)try{r.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch(t){}try{r.setRequestHeader("Accept","*/*")}catch(t){}null===(t=this._opts.cookieJar)||void 0===t||t.addCookies(r),"withCredentials"in r&&(r.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(r.timeout=this._opts.requestTimeout),r.onreadystatechange=()=>{var t;3===r.readyState&&(null===(t=this._opts.cookieJar)||void 0===t||t.parseCookies(r.getResponseHeader("set-cookie"))),4===r.readyState&&(200===r.status||1223===r.status?this._onLoad():this.setTimeoutFn(()=>{this._onError("number"==typeof r.status?r.status:0)},0))},r.send(this._data)}catch(t){this.setTimeoutFn(()=>{this._onError(t)},0);return}"undefined"!=typeof document&&(this._index=H.requestsCount++,H.requests[this._index]=this)}_onError(t){this.emitReserved("error",t,this._xhr),this._cleanup(!0)}_cleanup(t){if(void 0!==this._xhr&&null!==this._xhr){if(this._xhr.onreadystatechange=M,t)try{this._xhr.abort()}catch(t){}"undefined"!=typeof document&&delete H.requests[this._index],this._xhr=null}}_onLoad(){let t=this._xhr.responseText;null!==t&&(this.emitReserved("data",t),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}}if(H.requestsCount=0,H.requests={},"undefined"!=typeof document){if("function"==typeof attachEvent)attachEvent("onunload",V);else if("function"==typeof addEventListener){let t="onpagehide"in C?"pagehide":"unload";addEventListener(t,V,!1)}}function V(){for(let t in H.requests)H.requests.hasOwnProperty(t)&&H.requests[t].abort()}let $=function(){let t=J({xdomain:!1});return t&&null!==t.responseType}();class W extends z{constructor(t){super(t);let e=t&&t.forceBase64;this.supportsBinary=$&&!e}request(t={}){return Object.assign(t,{xd:this.xd},this.opts),new H(J,this.uri(),t)}}function J(t){let e=t.xdomain;try{if("undefined"!=typeof XMLHttpRequest&&(!e||q))return new XMLHttpRequest}catch(t){}if(!e)try{return new C[["Active"].concat("Object").join("X")]("Microsoft.XMLHTTP")}catch(t){}}let K="undefined"!=typeof navigator&&"string"==typeof navigator.product&&"reactnative"===navigator.product.toLowerCase();class Y extends I{get name(){return"websocket"}doOpen(){let t=this.uri(),e=this.opts.protocols,r=K?{}:B(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(r.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(t,e,r)}catch(t){return this.emitReserved("error",t)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=t=>this.onClose({description:"websocket connection closed",context:t}),this.ws.onmessage=t=>this.onData(t.data),this.ws.onerror=t=>this.onError("websocket error",t)}write(t){this.writable=!1;for(let e=0;e<t.length;e++){let r=t[e],n=e===t.length-1;d(r,this.supportsBinary,t=>{try{this.doWrite(r,t)}catch(t){}n&&x(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){void 0!==this.ws&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){let t=this.opts.secure?"wss":"ws",e=this.query||{};return this.opts.timestampRequests&&(e[this.opts.timestampParam]=j()),this.supportsBinary||(e.b64=1),this.createUri(t,e)}}let X=C.WebSocket||C.MozWebSocket;class Q extends Y{createSocket(t,e,r){return K?new X(t,e,r):e?new X(t,e):new X(t)}doWrite(t,e){this.ws.send(e)}}class G extends I{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(t){return this.emitReserved("error",t)}this._transport.closed.then(()=>{this.onClose()}).catch(t=>{this.onError("webtransport error",t)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(t=>{let e=function(t,e){i||(i=new TextDecoder);let r=[],n=0,s=-1,o=!1;return new TransformStream({transform(a,u){for(r.push(a);;){if(0===n){if(1>k(r))break;let t=T(r,1);o=(128&t[0])==128,n=(s=127&t[0])<126?3:126===s?1:2}else if(1===n){if(2>k(r))break;let t=T(r,2);s=new DataView(t.buffer,t.byteOffset,t.length).getUint16(0),n=3}else if(2===n){if(8>k(r))break;let t=T(r,8),e=new DataView(t.buffer,t.byteOffset,t.length),i=e.getUint32(0);if(i>2097151){u.enqueue(c);break}s=4294967296*i+e.getUint32(4),n=3}else{if(k(r)<s)break;let t=T(r,s);u.enqueue(E(o?t:i.decode(t),e)),n=0}if(0===s||s>t){u.enqueue(c);break}}}})}(Number.MAX_SAFE_INTEGER,this.socket.binaryType),r=t.readable.pipeThrough(e).getReader(),s=new TransformStream({transform(t,e){var r;r=r=>{let n;let i=r.length;if(i<126)n=new Uint8Array(1),new DataView(n.buffer).setUint8(0,i);else if(i<65536){n=new Uint8Array(3);let t=new DataView(n.buffer);t.setUint8(0,126),t.setUint16(1,i)}else{n=new Uint8Array(9);let t=new DataView(n.buffer);t.setUint8(0,127),t.setBigUint64(1,BigInt(i))}t.data&&"string"!=typeof t.data&&(n[0]|=128),e.enqueue(n),e.enqueue(r)},h&&t.data instanceof Blob?t.data.arrayBuffer().then(g).then(r):f&&(t.data instanceof ArrayBuffer||p(t.data))?r(g(t.data)):d(t,!1,t=>{n||(n=new TextEncoder),r(n.encode(t))})}});s.readable.pipeTo(t.writable),this._writer=s.writable.getWriter();let o=()=>{r.read().then(({done:t,value:e})=>{t||(this.onPacket(e),o())}).catch(t=>{})};o();let a={type:"open"};this.query.sid&&(a.data=`{"sid":"${this.query.sid}"}`),this._writer.write(a).then(()=>this.onOpen())})})}write(t){this.writable=!1;for(let e=0;e<t.length;e++){let r=t[e],n=e===t.length-1;this._writer.write(r).then(()=>{n&&x(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var t;null===(t=this._transport)||void 0===t||t.close()}}let Z={websocket:Q,webtransport:G,polling:W},tt=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,te=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function tr(t){if(t.length>8e3)throw"URI too long";let e=t,r=t.indexOf("["),n=t.indexOf("]");-1!=r&&-1!=n&&(t=t.substring(0,r)+t.substring(r,n).replace(/:/g,";")+t.substring(n,t.length));let i=tt.exec(t||""),s={},o=14;for(;o--;)s[te[o]]=i[o]||"";return -1!=r&&-1!=n&&(s.source=e,s.host=s.host.substring(1,s.host.length-1).replace(/;/g,":"),s.authority=s.authority.replace("[","").replace("]","").replace(/;/g,":"),s.ipv6uri=!0),s.pathNames=function(t,e){let r=e.replace(/\/{2,9}/g,"/").split("/");return("/"==e.slice(0,1)||0===e.length)&&r.splice(0,1),"/"==e.slice(-1)&&r.splice(r.length-1,1),r}(0,s.path),s.queryKey=function(t,e){let r={};return e.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(t,e,n){e&&(r[e]=n)}),r}(0,s.query),s}let tn="function"==typeof addEventListener&&"function"==typeof removeEventListener,ti=[];tn&&addEventListener("offline",()=>{ti.forEach(t=>t())},!1);class ts extends S{constructor(t,e){if(super(),this.binaryType="arraybuffer",this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,t&&"object"==typeof t&&(e=t,t=null),t){let r=tr(t);e.hostname=r.host,e.secure="https"===r.protocol||"wss"===r.protocol,e.port=r.port,r.query&&(e.query=r.query)}else e.host&&(e.hostname=tr(e.host).host);U(this,e),this.secure=null!=e.secure?e.secure:"undefined"!=typeof location&&"https:"===location.protocol,e.hostname&&!e.port&&(e.port=this.secure?"443":"80"),this.hostname=e.hostname||("undefined"!=typeof location?location.hostname:"localhost"),this.port=e.port||("undefined"!=typeof location&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},e.transports.forEach(t=>{let e=t.prototype.name;this.transports.push(e),this._transportsByName[e]=t}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},e),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),"string"==typeof this.opts.query&&(this.opts.query=function(t){let e={},r=t.split("&");for(let t=0,n=r.length;t<n;t++){let n=r[t].split("=");e[decodeURIComponent(n[0])]=decodeURIComponent(n[1])}return e}(this.opts.query)),tn&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),"localhost"!==this.hostname&&(this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},ti.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=void 0),this._open()}createTransport(t){let e=Object.assign({},this.opts.query);e.EIO=4,e.transport=t,this.id&&(e.sid=this.id);let r=Object.assign({},this.opts,{query:e,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[t]);return new this._transportsByName[t](r)}_open(){if(0===this.transports.length){this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);return}let t=this.opts.rememberUpgrade&&ts.priorWebsocketSuccess&&-1!==this.transports.indexOf("websocket")?"websocket":this.transports[0];this.readyState="opening";let e=this.createTransport(t);e.open(),this.setTransport(e)}setTransport(t){this.transport&&this.transport.removeAllListeners(),this.transport=t,t.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",t=>this._onClose("transport close",t))}onOpen(){this.readyState="open",ts.priorWebsocketSuccess="websocket"===this.transport.name,this.emitReserved("open"),this.flush()}_onPacket(t){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState)switch(this.emitReserved("packet",t),this.emitReserved("heartbeat"),t.type){case"open":this.onHandshake(JSON.parse(t.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":let e=Error("server error");e.code=t.data,this._onError(e);break;case"message":this.emitReserved("data",t.data),this.emitReserved("message",t.data)}}onHandshake(t){this.emitReserved("handshake",t),this.id=t.sid,this.transport.query.sid=t.sid,this._pingInterval=t.pingInterval,this._pingTimeout=t.pingTimeout,this._maxPayload=t.maxPayload,this.onOpen(),"closed"!==this.readyState&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);let t=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+t,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},t),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,0===this.writeBuffer.length?this.emitReserved("drain"):this.flush()}flush(){if("closed"!==this.readyState&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){let t=this._getWritablePackets();this.transport.send(t),this._prevBufferLen=t.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&"polling"===this.transport.name&&this.writeBuffer.length>1))return this.writeBuffer;let t=1;for(let e=0;e<this.writeBuffer.length;e++){let r=this.writeBuffer[e].data;if(r&&(t+="string"==typeof r?function(t){let e=0,r=0;for(let n=0,i=t.length;n<i;n++)(e=t.charCodeAt(n))<128?r+=1:e<2048?r+=2:e<55296||e>=57344?r+=3:(n++,r+=4);return r}(r):Math.ceil(1.33*(r.byteLength||r.size))),e>0&&t>this._maxPayload)return this.writeBuffer.slice(0,e);t+=2}return this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;let t=Date.now()>this._pingTimeoutTime;return t&&(this._pingTimeoutTime=0,x(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),t}write(t,e,r){return this._sendPacket("message",t,e,r),this}send(t,e,r){return this._sendPacket("message",t,e,r),this}_sendPacket(t,e,r,n){if("function"==typeof e&&(n=e,e=void 0),"function"==typeof r&&(n=r,r=null),"closing"===this.readyState||"closed"===this.readyState)return;(r=r||{}).compress=!1!==r.compress;let i={type:t,data:e,options:r};this.emitReserved("packetCreate",i),this.writeBuffer.push(i),n&&this.once("flush",n),this.flush()}close(){let t=()=>{this._onClose("forced close"),this.transport.close()},e=()=>{this.off("upgrade",e),this.off("upgradeError",e),t()},r=()=>{this.once("upgrade",e),this.once("upgradeError",e)};return("opening"===this.readyState||"open"===this.readyState)&&(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?r():t()}):this.upgrading?r():t()),this}_onError(t){if(ts.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&"opening"===this.readyState)return this.transports.shift(),this._open();this.emitReserved("error",t),this._onClose("transport error",t)}_onClose(t,e){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState){if(this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),tn&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){let t=ti.indexOf(this._offlineEventListener);-1!==t&&ti.splice(t,1)}this.readyState="closed",this.id=null,this.emitReserved("close",t,e),this.writeBuffer=[],this._prevBufferLen=0}}}ts.protocol=4;class to extends ts{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),"open"===this.readyState&&this.opts.upgrade)for(let t=0;t<this._upgrades.length;t++)this._probe(this._upgrades[t])}_probe(t){let e=this.createTransport(t),r=!1;ts.priorWebsocketSuccess=!1;let n=()=>{r||(e.send([{type:"ping",data:"probe"}]),e.once("packet",t=>{if(!r){if("pong"===t.type&&"probe"===t.data)this.upgrading=!0,this.emitReserved("upgrading",e),e&&(ts.priorWebsocketSuccess="websocket"===e.name,this.transport.pause(()=>{r||"closed"===this.readyState||(l(),this.setTransport(e),e.send([{type:"upgrade"}]),this.emitReserved("upgrade",e),e=null,this.upgrading=!1,this.flush())}));else{let t=Error("probe error");t.transport=e.name,this.emitReserved("upgradeError",t)}}}))};function i(){r||(r=!0,l(),e.close(),e=null)}let s=t=>{let r=Error("probe error: "+t);r.transport=e.name,i(),this.emitReserved("upgradeError",r)};function o(){s("transport closed")}function a(){s("socket closed")}function u(t){e&&t.name!==e.name&&i()}let l=()=>{e.removeListener("open",n),e.removeListener("error",s),e.removeListener("close",o),this.off("close",a),this.off("upgrading",u)};e.once("open",n),e.once("error",s),e.once("close",o),this.once("close",a),this.once("upgrading",u),-1!==this._upgrades.indexOf("webtransport")&&"webtransport"!==t?this.setTimeoutFn(()=>{r||e.open()},200):e.open()}onHandshake(t){this._upgrades=this._filterUpgrades(t.upgrades),super.onHandshake(t)}_filterUpgrades(t){let e=[];for(let r=0;r<t.length;r++)~this.transports.indexOf(t[r])&&e.push(t[r]);return e}}class ta extends to{constructor(t,e={}){let r="object"==typeof t?t:e;(!r.transports||r.transports&&"string"==typeof r.transports[0])&&(r.transports=(r.transports||["polling","websocket","webtransport"]).map(t=>Z[t]).filter(t=>!!t)),super(t,r)}}ta.protocol;let tu="function"==typeof ArrayBuffer,tl=t=>"function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(t):t.buffer instanceof ArrayBuffer,tc=Object.prototype.toString,th="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===tc.call(Blob),tf="function"==typeof File||"undefined"!=typeof File&&"[object FileConstructor]"===tc.call(File);function tp(t){return tu&&(t instanceof ArrayBuffer||tl(t))||th&&t instanceof Blob||tf&&t instanceof File}let td=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"],ty=5;(s=o||(o={}))[s.CONNECT=0]="CONNECT",s[s.DISCONNECT=1]="DISCONNECT",s[s.EVENT=2]="EVENT",s[s.ACK=3]="ACK",s[s.CONNECT_ERROR=4]="CONNECT_ERROR",s[s.BINARY_EVENT=5]="BINARY_EVENT",s[s.BINARY_ACK=6]="BINARY_ACK";class tg{constructor(t){this.replacer=t}encode(t){return(t.type===o.EVENT||t.type===o.ACK)&&function t(e,r){if(!e||"object"!=typeof e)return!1;if(Array.isArray(e)){for(let r=0,n=e.length;r<n;r++)if(t(e[r]))return!0;return!1}if(tp(e))return!0;if(e.toJSON&&"function"==typeof e.toJSON&&1==arguments.length)return t(e.toJSON(),!0);for(let r in e)if(Object.prototype.hasOwnProperty.call(e,r)&&t(e[r]))return!0;return!1}(t)?this.encodeAsBinary({type:t.type===o.EVENT?o.BINARY_EVENT:o.BINARY_ACK,nsp:t.nsp,data:t.data,id:t.id}):[this.encodeAsString(t)]}encodeAsString(t){let e=""+t.type;return(t.type===o.BINARY_EVENT||t.type===o.BINARY_ACK)&&(e+=t.attachments+"-"),t.nsp&&"/"!==t.nsp&&(e+=t.nsp+","),null!=t.id&&(e+=t.id),null!=t.data&&(e+=JSON.stringify(t.data,this.replacer)),e}encodeAsBinary(t){let e=function(t){let e=[],r=t.data;return t.data=function t(e,r){if(!e)return e;if(tp(e)){let t={_placeholder:!0,num:r.length};return r.push(e),t}if(Array.isArray(e)){let n=Array(e.length);for(let i=0;i<e.length;i++)n[i]=t(e[i],r);return n}if("object"==typeof e&&!(e instanceof Date)){let n={};for(let i in e)Object.prototype.hasOwnProperty.call(e,i)&&(n[i]=t(e[i],r));return n}return e}(r,e),t.attachments=e.length,{packet:t,buffers:e}}(t),r=this.encodeAsString(e.packet),n=e.buffers;return n.unshift(r),n}}function tm(t){return"[object Object]"===Object.prototype.toString.call(t)}class tb extends S{constructor(t){super(),this.reviver=t}add(t){let e;if("string"==typeof t){if(this.reconstructor)throw Error("got plaintext data when reconstructing a packet");let r=(e=this.decodeString(t)).type===o.BINARY_EVENT;r||e.type===o.BINARY_ACK?(e.type=r?o.EVENT:o.ACK,this.reconstructor=new tw(e),0===e.attachments&&super.emitReserved("decoded",e)):super.emitReserved("decoded",e)}else if(tp(t)||t.base64){if(this.reconstructor)(e=this.reconstructor.takeBinaryData(t))&&(this.reconstructor=null,super.emitReserved("decoded",e));else throw Error("got binary data when not reconstructing a packet")}else throw Error("Unknown type: "+t)}decodeString(t){let e=0,r={type:Number(t.charAt(0))};if(void 0===o[r.type])throw Error("unknown packet type "+r.type);if(r.type===o.BINARY_EVENT||r.type===o.BINARY_ACK){let n=e+1;for(;"-"!==t.charAt(++e)&&e!=t.length;);let i=t.substring(n,e);if(i!=Number(i)||"-"!==t.charAt(e))throw Error("Illegal attachments");r.attachments=Number(i)}if("/"===t.charAt(e+1)){let n=e+1;for(;++e&&","!==t.charAt(e)&&e!==t.length;);r.nsp=t.substring(n,e)}else r.nsp="/";let n=t.charAt(e+1);if(""!==n&&Number(n)==n){let n=e+1;for(;++e;){let r=t.charAt(e);if(null==r||Number(r)!=r){--e;break}if(e===t.length)break}r.id=Number(t.substring(n,e+1))}if(t.charAt(++e)){let n=this.tryParse(t.substr(e));if(tb.isPayloadValid(r.type,n))r.data=n;else throw Error("invalid payload")}return r}tryParse(t){try{return JSON.parse(t,this.reviver)}catch(t){return!1}}static isPayloadValid(t,e){switch(t){case o.CONNECT:return tm(e);case o.DISCONNECT:return void 0===e;case o.CONNECT_ERROR:return"string"==typeof e||tm(e);case o.EVENT:case o.BINARY_EVENT:return Array.isArray(e)&&("number"==typeof e[0]||"string"==typeof e[0]&&-1===td.indexOf(e[0]));case o.ACK:case o.BINARY_ACK:return Array.isArray(e)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class tw{constructor(t){this.packet=t,this.buffers=[],this.reconPack=t}takeBinaryData(t){if(this.buffers.push(t),this.buffers.length===this.reconPack.attachments){var e,r;let t=(e=this.reconPack,r=this.buffers,e.data=function t(e,r){if(!e)return e;if(e&&!0===e._placeholder){if("number"==typeof e.num&&e.num>=0&&e.num<r.length)return r[e.num];throw Error("illegal attachments")}if(Array.isArray(e))for(let n=0;n<e.length;n++)e[n]=t(e[n],r);else if("object"==typeof e)for(let n in e)Object.prototype.hasOwnProperty.call(e,n)&&(e[n]=t(e[n],r));return e}(e.data,r),delete e.attachments,e);return this.finishedReconstruction(),t}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}function tv(t,e,r){return t.on(e,r),function(){t.off(e,r)}}let tE=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class t_ extends S{constructor(t,e,r){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=t,this.nsp=e,r&&r.auth&&(this.auth=r.auth),this._opts=Object.assign({},r),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;let t=this.io;this.subs=[tv(t,"open",this.onopen.bind(this)),tv(t,"packet",this.onpacket.bind(this)),tv(t,"error",this.onerror.bind(this)),tv(t,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected||(this.subEvents(),this.io._reconnecting||this.io.open(),"open"===this.io._readyState&&this.onopen()),this}open(){return this.connect()}send(...t){return t.unshift("message"),this.emit.apply(this,t),this}emit(t,...e){var r,n,i;if(tE.hasOwnProperty(t))throw Error('"'+t.toString()+'" is a reserved event name');if(e.unshift(t),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(e),this;let s={type:o.EVENT,data:e};if(s.options={},s.options.compress=!1!==this.flags.compress,"function"==typeof e[e.length-1]){let t=this.ids++,r=e.pop();this._registerAckCallback(t,r),s.id=t}let a=null===(n=null===(r=this.io.engine)||void 0===r?void 0:r.transport)||void 0===n?void 0:n.writable,u=this.connected&&!(null===(i=this.io.engine)||void 0===i?void 0:i._hasPingExpired());return this.flags.volatile&&!a||(u?(this.notifyOutgoingListeners(s),this.packet(s)):this.sendBuffer.push(s)),this.flags={},this}_registerAckCallback(t,e){var r;let n=null!==(r=this.flags.timeout)&&void 0!==r?r:this._opts.ackTimeout;if(void 0===n){this.acks[t]=e;return}let i=this.io.setTimeoutFn(()=>{delete this.acks[t];for(let e=0;e<this.sendBuffer.length;e++)this.sendBuffer[e].id===t&&this.sendBuffer.splice(e,1);e.call(this,Error("operation has timed out"))},n),s=(...t)=>{this.io.clearTimeoutFn(i),e.apply(this,t)};s.withError=!0,this.acks[t]=s}emitWithAck(t,...e){return new Promise((r,n)=>{let i=(t,e)=>t?n(t):r(e);i.withError=!0,e.push(i),this.emit(t,...e)})}_addToQueue(t){let e;"function"==typeof t[t.length-1]&&(e=t.pop());let r={id:this._queueSeq++,tryCount:0,pending:!1,args:t,flags:Object.assign({fromQueue:!0},this.flags)};t.push((t,...n)=>{if(r===this._queue[0])return null!==t?r.tryCount>this._opts.retries&&(this._queue.shift(),e&&e(t)):(this._queue.shift(),e&&e(null,...n)),r.pending=!1,this._drainQueue()}),this._queue.push(r),this._drainQueue()}_drainQueue(t=!1){if(!this.connected||0===this._queue.length)return;let e=this._queue[0];(!e.pending||t)&&(e.pending=!0,e.tryCount++,this.flags=e.flags,this.emit.apply(this,e.args))}packet(t){t.nsp=this.nsp,this.io._packet(t)}onopen(){"function"==typeof this.auth?this.auth(t=>{this._sendConnectPacket(t)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(t){this.packet({type:o.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},t):t})}onerror(t){this.connected||this.emitReserved("connect_error",t)}onclose(t,e){this.connected=!1,delete this.id,this.emitReserved("disconnect",t,e),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(t=>{if(!this.sendBuffer.some(e=>String(e.id)===t)){let e=this.acks[t];delete this.acks[t],e.withError&&e.call(this,Error("socket has been disconnected"))}})}onpacket(t){if(!(t.nsp!==this.nsp))switch(t.type){case o.CONNECT:t.data&&t.data.sid?this.onconnect(t.data.sid,t.data.pid):this.emitReserved("connect_error",Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case o.EVENT:case o.BINARY_EVENT:this.onevent(t);break;case o.ACK:case o.BINARY_ACK:this.onack(t);break;case o.DISCONNECT:this.ondisconnect();break;case o.CONNECT_ERROR:this.destroy();let e=Error(t.data.message);e.data=t.data.data,this.emitReserved("connect_error",e)}}onevent(t){let e=t.data||[];null!=t.id&&e.push(this.ack(t.id)),this.connected?this.emitEvent(e):this.receiveBuffer.push(Object.freeze(e))}emitEvent(t){if(this._anyListeners&&this._anyListeners.length)for(let e of this._anyListeners.slice())e.apply(this,t);super.emit.apply(this,t),this._pid&&t.length&&"string"==typeof t[t.length-1]&&(this._lastOffset=t[t.length-1])}ack(t){let e=this,r=!1;return function(...n){r||(r=!0,e.packet({type:o.ACK,id:t,data:n}))}}onack(t){let e=this.acks[t.id];"function"==typeof e&&(delete this.acks[t.id],e.withError&&t.data.unshift(null),e.apply(this,t.data))}onconnect(t,e){this.id=t,this.recovered=e&&this._pid===e,this._pid=e,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(t=>this.emitEvent(t)),this.receiveBuffer=[],this.sendBuffer.forEach(t=>{this.notifyOutgoingListeners(t),this.packet(t)}),this.sendBuffer=[]}ondisconnect(){this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(t=>t()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&this.packet({type:o.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(t){return this.flags.compress=t,this}get volatile(){return this.flags.volatile=!0,this}timeout(t){return this.flags.timeout=t,this}onAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(t),this}prependAny(t){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(t),this}offAny(t){if(!this._anyListeners)return this;if(t){let e=this._anyListeners;for(let r=0;r<e.length;r++)if(t===e[r]){e.splice(r,1);break}}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(t),this}prependAnyOutgoing(t){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(t),this}offAnyOutgoing(t){if(!this._anyOutgoingListeners)return this;if(t){let e=this._anyOutgoingListeners;for(let r=0;r<e.length;r++)if(t===e[r]){e.splice(r,1);break}}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(t){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length)for(let e of this._anyOutgoingListeners.slice())e.apply(this,t.data)}}function tA(t){t=t||{},this.ms=t.min||100,this.max=t.max||1e4,this.factor=t.factor||2,this.jitter=t.jitter>0&&t.jitter<=1?t.jitter:0,this.attempts=0}tA.prototype.duration=function(){var t=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var e=Math.random(),r=Math.floor(e*this.jitter*t);t=(1&Math.floor(10*e))==0?t-r:t+r}return 0|Math.min(t,this.max)},tA.prototype.reset=function(){this.attempts=0},tA.prototype.setMin=function(t){this.ms=t},tA.prototype.setMax=function(t){this.max=t},tA.prototype.setJitter=function(t){this.jitter=t};class tO extends S{constructor(t,e){var r;super(),this.nsps={},this.subs=[],t&&"object"==typeof t&&(e=t,t=void 0),(e=e||{}).path=e.path||"/socket.io",this.opts=e,U(this,e),this.reconnection(!1!==e.reconnection),this.reconnectionAttempts(e.reconnectionAttempts||1/0),this.reconnectionDelay(e.reconnectionDelay||1e3),this.reconnectionDelayMax(e.reconnectionDelayMax||5e3),this.randomizationFactor(null!==(r=e.randomizationFactor)&&void 0!==r?r:.5),this.backoff=new tA({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(null==e.timeout?2e4:e.timeout),this._readyState="closed",this.uri=t;let n=e.parser||a;this.encoder=new n.Encoder,this.decoder=new n.Decoder,this._autoConnect=!1!==e.autoConnect,this._autoConnect&&this.open()}reconnection(t){return arguments.length?(this._reconnection=!!t,t||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(t){return void 0===t?this._reconnectionAttempts:(this._reconnectionAttempts=t,this)}reconnectionDelay(t){var e;return void 0===t?this._reconnectionDelay:(this._reconnectionDelay=t,null===(e=this.backoff)||void 0===e||e.setMin(t),this)}randomizationFactor(t){var e;return void 0===t?this._randomizationFactor:(this._randomizationFactor=t,null===(e=this.backoff)||void 0===e||e.setJitter(t),this)}reconnectionDelayMax(t){var e;return void 0===t?this._reconnectionDelayMax:(this._reconnectionDelayMax=t,null===(e=this.backoff)||void 0===e||e.setMax(t),this)}timeout(t){return arguments.length?(this._timeout=t,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&0===this.backoff.attempts&&this.reconnect()}open(t){if(~this._readyState.indexOf("open"))return this;this.engine=new ta(this.uri,this.opts);let e=this.engine,r=this;this._readyState="opening",this.skipReconnect=!1;let n=tv(e,"open",function(){r.onopen(),t&&t()}),i=e=>{this.cleanup(),this._readyState="closed",this.emitReserved("error",e),t?t(e):this.maybeReconnectOnOpen()},s=tv(e,"error",i);if(!1!==this._timeout){let t=this._timeout,r=this.setTimeoutFn(()=>{n(),i(Error("timeout")),e.close()},t);this.opts.autoUnref&&r.unref(),this.subs.push(()=>{this.clearTimeoutFn(r)})}return this.subs.push(n),this.subs.push(s),this}connect(t){return this.open(t)}onopen(){this.cleanup(),this._readyState="open",this.emitReserved("open");let t=this.engine;this.subs.push(tv(t,"ping",this.onping.bind(this)),tv(t,"data",this.ondata.bind(this)),tv(t,"error",this.onerror.bind(this)),tv(t,"close",this.onclose.bind(this)),tv(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(t){try{this.decoder.add(t)}catch(t){this.onclose("parse error",t)}}ondecoded(t){x(()=>{this.emitReserved("packet",t)},this.setTimeoutFn)}onerror(t){this.emitReserved("error",t)}socket(t,e){let r=this.nsps[t];return r?this._autoConnect&&!r.active&&r.connect():(r=new t_(this,t,e),this.nsps[t]=r),r}_destroy(t){for(let t of Object.keys(this.nsps))if(this.nsps[t].active)return;this._close()}_packet(t){let e=this.encoder.encode(t);for(let r=0;r<e.length;r++)this.engine.write(e[r],t.options)}cleanup(){this.subs.forEach(t=>t()),this.subs.length=0,this.decoder.destroy()}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(t,e){var r;this.cleanup(),null===(r=this.engine)||void 0===r||r.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",t,e),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;let t=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{let e=this.backoff.duration();this._reconnecting=!0;let r=this.setTimeoutFn(()=>{!t.skipReconnect&&(this.emitReserved("reconnect_attempt",t.backoff.attempts),t.skipReconnect||t.open(e=>{e?(t._reconnecting=!1,t.reconnect(),this.emitReserved("reconnect_error",e)):t.onreconnect()}))},e);this.opts.autoUnref&&r.unref(),this.subs.push(()=>{this.clearTimeoutFn(r)})}}onreconnect(){let t=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",t)}}let tR={};function tk(t,e){let r;"object"==typeof t&&(e=t,t=void 0);let n=function(t,e="",r){let n=t;r=r||"undefined"!=typeof location&&location,null==t&&(t=r.protocol+"//"+r.host),"string"==typeof t&&("/"===t.charAt(0)&&(t="/"===t.charAt(1)?r.protocol+t:r.host+t),/^(https?|wss?):\/\//.test(t)||(t=void 0!==r?r.protocol+"//"+t:"https://"+t),n=tr(t)),!n.port&&(/^(http|ws)$/.test(n.protocol)?n.port="80":/^(http|ws)s$/.test(n.protocol)&&(n.port="443")),n.path=n.path||"/";let i=-1!==n.host.indexOf(":")?"["+n.host+"]":n.host;return n.id=n.protocol+"://"+i+":"+n.port+e,n.href=n.protocol+"://"+i+(r&&r.port===n.port?"":":"+n.port),n}(t,(e=e||{}).path||"/socket.io"),i=n.source,s=n.id,o=n.path,a=tR[s]&&o in tR[s].nsps;return e.forceNew||e["force new connection"]||!1===e.multiplex||a?r=new tO(i,e):(tR[s]||(tR[s]=new tO(i,e)),r=tR[s]),n.query&&!e.query&&(e.query=n.queryKey),r.socket(n.path,e)}Object.assign(tk,{Manager:tO,Socket:t_,io:tk,connect:tk})}}]);