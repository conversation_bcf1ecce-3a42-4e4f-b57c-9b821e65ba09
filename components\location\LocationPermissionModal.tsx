'use client'

import { motion, AnimatePresence } from 'framer-motion'
import { MapPin, Shield, Users, X } from 'lucide-react'
import { Button } from '@/components/ui/Button'

interface LocationPermissionModalProps {
  isOpen: boolean
  onClose: () => void
  onPermissionResponse: (granted: boolean) => void
}

export function LocationPermissionModal({ 
  isOpen, 
  onClose, 
  onPermissionResponse 
}: LocationPermissionModalProps) {
  const handleAllow = () => {
    onPermissionResponse(true)
  }

  const handleDeny = () => {
    onPermissionResponse(false)
  }

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
        {/* Backdrop */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        />

        {/* Modal */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          className="relative w-full max-w-md bg-white rounded-2xl shadow-2xl"
        >
          {/* Header */}
          <div className="p-6 text-center">
            <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <MapPin className="w-8 h-8 text-primary-600" />
            </div>
            
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Enable Location Sharing
            </h2>
            
            <p className="text-gray-600">
              To help you find friends and navigate campus, we need access to your location.
            </p>
          </div>

          {/* Features */}
          <div className="px-6 pb-6">
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Users className="w-4 h-4 text-green-600" />
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">Find Friends</h3>
                  <p className="text-sm text-gray-600">
                    See where your friends are on campus in real-time
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <MapPin className="w-4 h-4 text-blue-600" />
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">Campus Navigation</h3>
                  <p className="text-sm text-gray-600">
                    Get directions and navigate between buildings
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center flex-shrink-0">
                  <Shield className="w-4 h-4 text-purple-600" />
                </div>
                <div>
                  <h3 className="font-medium text-gray-900">Privacy Control</h3>
                  <p className="text-sm text-gray-600">
                    You can turn off location sharing anytime in settings
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Privacy Notice */}
          <div className="px-6 pb-6">
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium text-gray-900 mb-2">Your Privacy Matters</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Your location is only shared with friends you approve</li>
                <li>• You can go invisible or stop sharing anytime</li>
                <li>• Location data is encrypted and secure</li>
                <li>• We never share your data with third parties</li>
              </ul>
            </div>
          </div>

          {/* Actions */}
          <div className="px-6 pb-6 flex flex-col space-y-3">
            <Button
              onClick={handleAllow}
              className="w-full"
              size="lg"
            >
              Allow Location Access
            </Button>
            
            <Button
              onClick={handleDeny}
              variant="outline"
              className="w-full"
              size="lg"
            >
              Not Now
            </Button>
          </div>

          {/* Close Button */}
          <button
            onClick={onClose}
            className="absolute top-4 right-4 p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-gray-500" />
          </button>
        </motion.div>
      </div>
    </AnimatePresence>
  )
}
