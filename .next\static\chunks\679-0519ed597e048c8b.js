(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[679],{2898:function(t,e,r){"use strict";r.d(e,{Z:function(){return o}});var i=r(2265),n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let s=t=>t.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),o=(t,e)=>{let r=(0,i.forwardRef)(({color:r="currentColor",size:o=24,strokeWidth:a=2,absoluteStrokeWidth:l,className:u="",children:h,...c},d)=>(0,i.createElement)("svg",{ref:d,...n,width:o,height:o,stroke:r,strokeWidth:l?24*Number(a)/Number(o):a,className:["lucide",`lucide-${s(t)}`,u].join(" "),...c},[...e.map(([t,e])=>(0,i.createElement)(t,e)),...Array.isArray(h)?h:[h]]));return r.displayName=`${t}`,r}},7216:function(t,e,r){"use strict";r.d(e,{Z:function(){return i}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,r(2898).Z)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},9670:function(t,e,r){"use strict";r.d(e,{Z:function(){return i}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,r(2898).Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1295:function(t,e,r){"use strict";r.d(e,{Z:function(){return i}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,r(2898).Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},6142:function(t,e,r){"use strict";r.d(e,{Z:function(){return i}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,r(2898).Z)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},9036:function(t,e,r){"use strict";r.d(e,{Z:function(){return i}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,r(2898).Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},7972:function(t,e,r){"use strict";r.d(e,{Z:function(){return i}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,r(2898).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},5750:function(t,e,r){"use strict";r.d(e,{Z:function(){return i}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,r(2898).Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},2549:function(t,e,r){"use strict";r.d(e,{Z:function(){return i}});/**
 * @license lucide-react v0.294.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */let i=(0,r(2898).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4033:function(t,e,r){t.exports=r(5313)},7042:function(t,e,r){"use strict";function i(){for(var t,e,r=0,i="",n=arguments.length;r<n;r++)(t=arguments[r])&&(e=function t(e){var r,i,n="";if("string"==typeof e||"number"==typeof e)n+=e;else if("object"==typeof e){if(Array.isArray(e)){var s=e.length;for(r=0;r<s;r++)e[r]&&(i=t(e[r]))&&(n&&(n+=" "),n+=i)}else for(i in e)e[i]&&(n&&(n+=" "),n+=i)}return n}(t))&&(i&&(i+=" "),i+=e);return i}r.d(e,{W:function(){return i}})},2167:function(t,e,r){"use strict";r.d(e,{M:function(){return g}});var i=r(2265),n=r(538);function s(){let t=(0,i.useRef)(!1);return(0,n.L)(()=>(t.current=!0,()=>{t.current=!1}),[]),t}var o=r(2363),a=r(8243),l=r(961);class u extends i.Component{getSnapshotBeforeUpdate(t){let e=this.props.childRef.current;if(e&&t.isPresent&&!this.props.isPresent){let t=this.props.sizeRef.current;t.height=e.offsetHeight||0,t.width=e.offsetWidth||0,t.top=e.offsetTop,t.left=e.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function h({children:t,isPresent:e}){let r=(0,i.useId)(),n=(0,i.useRef)(null),s=(0,i.useRef)({width:0,height:0,top:0,left:0});return(0,i.useInsertionEffect)(()=>{let{width:t,height:i,top:o,left:a}=s.current;if(e||!n.current||!t||!i)return;n.current.dataset.motionPopId=r;let l=document.createElement("style");return document.head.appendChild(l),l.sheet&&l.sheet.insertRule(`
          [data-motion-pop-id="${r}"] {
            position: absolute !important;
            width: ${t}px !important;
            height: ${i}px !important;
            top: ${o}px !important;
            left: ${a}px !important;
          }
        `),()=>{document.head.removeChild(l)}},[e]),i.createElement(u,{isPresent:e,childRef:n,sizeRef:s},i.cloneElement(t,{ref:n}))}let c=({children:t,initial:e,isPresent:r,onExitComplete:n,custom:s,presenceAffectsLayout:o,mode:u})=>{let c=(0,l.h)(d),p=(0,i.useId)(),m=(0,i.useMemo)(()=>({id:p,initial:e,isPresent:r,custom:s,onExitComplete:t=>{for(let e of(c.set(t,!0),c.values()))if(!e)return;n&&n()},register:t=>(c.set(t,!1),()=>c.delete(t))}),o?void 0:[r]);return(0,i.useMemo)(()=>{c.forEach((t,e)=>c.set(e,!1))},[r]),i.useEffect(()=>{r||c.size||!n||n()},[r]),"popLayout"===u&&(t=i.createElement(h,{isPresent:r},t)),i.createElement(a.O.Provider,{value:m},t)};function d(){return new Map}var p=r(781),m=r(6567);let f=t=>t.key||"",g=({children:t,custom:e,initial:r=!0,onExitComplete:a,exitBeforeEnter:l,presenceAffectsLayout:u=!0,mode:h="sync"})=>{var d;(0,m.k)(!l,"Replace exitBeforeEnter with mode='wait'");let g=(0,i.useContext)(p.p).forceRender||function(){let t=s(),[e,r]=(0,i.useState)(0),n=(0,i.useCallback)(()=>{t.current&&r(e+1)},[e]);return[(0,i.useCallback)(()=>o.Wi.postRender(n),[n]),e]}()[0],v=s(),y=function(t){let e=[];return i.Children.forEach(t,t=>{(0,i.isValidElement)(t)&&e.push(t)}),e}(t),b=y,x=(0,i.useRef)(new Map).current,w=(0,i.useRef)(b),k=(0,i.useRef)(new Map).current,P=(0,i.useRef)(!0);if((0,n.L)(()=>{P.current=!1,function(t,e){t.forEach(t=>{let r=f(t);e.set(r,t)})}(y,k),w.current=b}),d=()=>{P.current=!0,k.clear(),x.clear()},(0,i.useEffect)(()=>()=>d(),[]),P.current)return i.createElement(i.Fragment,null,b.map(t=>i.createElement(c,{key:f(t),isPresent:!0,initial:!!r&&void 0,presenceAffectsLayout:u,mode:h},t)));b=[...b];let A=w.current.map(f),T=y.map(f),S=A.length;for(let t=0;t<S;t++){let e=A[t];-1!==T.indexOf(e)||x.has(e)||x.set(e,void 0)}return"wait"===h&&x.size&&(b=[]),x.forEach((t,r)=>{if(-1!==T.indexOf(r))return;let n=k.get(r);if(!n)return;let s=A.indexOf(r),o=t;o||(o=i.createElement(c,{key:f(n),isPresent:!1,onExitComplete:()=>{x.delete(r);let t=Array.from(k.keys()).filter(t=>!T.includes(t));if(t.forEach(t=>k.delete(t)),w.current=y.filter(e=>{let i=f(e);return i===r||t.includes(i)}),!x.size){if(!1===v.current)return;g(),a&&a()}},custom:e,presenceAffectsLayout:u,mode:h},n),x.set(r,o)),b.splice(s,0,o)}),b=b.map(t=>{let e=t.key;return x.has(e)?t:i.createElement(c,{key:f(t),isPresent:!0,presenceAffectsLayout:u,mode:h},t)}),i.createElement(i.Fragment,null,x.size?b:b.map(t=>(0,i.cloneElement)(t)))}},781:function(t,e,r){"use strict";r.d(e,{p:function(){return i}});let i=(0,r(2265).createContext)({})},8243:function(t,e,r){"use strict";r.d(e,{O:function(){return i}});let i=(0,r(2265).createContext)(null)},2363:function(t,e,r){"use strict";r.d(e,{Pn:function(){return a},Wi:function(){return o},frameData:function(){return l},S6:function(){return u}});var i=r(6977);class n{constructor(){this.order=[],this.scheduled=new Set}add(t){if(!this.scheduled.has(t))return this.scheduled.add(t),this.order.push(t),!0}remove(t){let e=this.order.indexOf(t);-1!==e&&(this.order.splice(e,1),this.scheduled.delete(t))}clear(){this.order.length=0,this.scheduled.clear()}}let s=["prepare","read","update","preRender","render","postRender"],{schedule:o,cancel:a,state:l,steps:u}=function(t,e){let r=!1,i=!0,o={delta:0,timestamp:0,isProcessing:!1},a=s.reduce((t,e)=>(t[e]=function(t){let e=new n,r=new n,i=0,s=!1,o=!1,a=new WeakSet,l={schedule:(t,n=!1,o=!1)=>{let l=o&&s,u=l?e:r;return n&&a.add(t),u.add(t)&&l&&s&&(i=e.order.length),t},cancel:t=>{r.remove(t),a.delete(t)},process:n=>{if(s){o=!0;return}if(s=!0,[e,r]=[r,e],r.clear(),i=e.order.length)for(let r=0;r<i;r++){let i=e.order[r];i(n),a.has(i)&&(l.schedule(i),t())}s=!1,o&&(o=!1,l.process(n))}};return l}(()=>r=!0),t),{}),l=t=>a[t].process(o),u=()=>{let n=performance.now();r=!1,o.delta=i?1e3/60:Math.max(Math.min(n-o.timestamp,40),1),o.timestamp=n,o.isProcessing=!0,s.forEach(l),o.isProcessing=!1,r&&e&&(i=!1,t(u))},h=()=>{r=!0,i=!0,o.isProcessing||t(u)};return{schedule:s.reduce((t,e)=>{let i=a[e];return t[e]=(t,e=!1,n=!1)=>(r||h(),i.schedule(t,e,n)),t},{}),cancel:t=>s.forEach(e=>a[e].cancel(t)),state:o,steps:a}}("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:i.Z,!0)},5251:function(t,e,r){"use strict";let i;r.d(e,{E:function(){return nH}});var n,s,o=r(2265);let a=(0,o.createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),l=(0,o.createContext)({});var u=r(8243),h=r(538);let c=(0,o.createContext)({strict:!1}),d=t=>t.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),p="data-"+d("framerAppearId");function m(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}function f(t){return"string"==typeof t||Array.isArray(t)}function g(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}let v=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],y=["initial",...v];function b(t){return g(t.animate)||y.some(e=>f(t[e]))}function x(t){return!!(b(t)||t.variants)}function w(t){return Array.isArray(t)?t.join(" "):t}let k={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},P={};for(let t in k)P[t]={isEnabled:e=>k[t].some(t=>!!e[t])};var A=r(6613),T=r(781);let S=(0,o.createContext)({}),E=Symbol.for("motionComponentSymbol"),V=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function M(t){if("string"!=typeof t||t.includes("-"));else if(V.indexOf(t)>-1||/[A-Z]/.test(t))return!0;return!1}let C={},D=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],R=new Set(D);function j(t,{layout:e,layoutId:r}){return R.has(t)||t.startsWith("origin")||(e||void 0!==r)&&(!!C[t]||"opacity"===t)}let L=t=>!!(t&&t.getVelocity),F={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},B=D.length,z=t=>e=>"string"==typeof e&&e.startsWith(t),O=z("--"),I=z("var(--"),W=(t,e)=>e&&"number"==typeof t?e.transform(t):t,U=(t,e,r)=>Math.min(Math.max(r,t),e),$={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},N={...$,transform:t=>U(0,1,t)},Z={...$,default:1},H=t=>Math.round(1e5*t)/1e5,G=/(-)?([\d]*\.?[\d])+/g,Y=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,X=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function q(t){return"string"==typeof t}let K=t=>({test:e=>q(e)&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),_=K("deg"),J=K("%"),Q=K("px"),tt=K("vh"),te=K("vw"),tr={...J,parse:t=>J.parse(t)/100,transform:t=>J.transform(100*t)},ti={...$,transform:Math.round},tn={borderWidth:Q,borderTopWidth:Q,borderRightWidth:Q,borderBottomWidth:Q,borderLeftWidth:Q,borderRadius:Q,radius:Q,borderTopLeftRadius:Q,borderTopRightRadius:Q,borderBottomRightRadius:Q,borderBottomLeftRadius:Q,width:Q,maxWidth:Q,height:Q,maxHeight:Q,size:Q,top:Q,right:Q,bottom:Q,left:Q,padding:Q,paddingTop:Q,paddingRight:Q,paddingBottom:Q,paddingLeft:Q,margin:Q,marginTop:Q,marginRight:Q,marginBottom:Q,marginLeft:Q,rotate:_,rotateX:_,rotateY:_,rotateZ:_,scale:Z,scaleX:Z,scaleY:Z,scaleZ:Z,skew:_,skewX:_,skewY:_,distance:Q,translateX:Q,translateY:Q,translateZ:Q,x:Q,y:Q,z:Q,perspective:Q,transformPerspective:Q,opacity:N,originX:tr,originY:tr,originZ:Q,zIndex:ti,fillOpacity:N,strokeOpacity:N,numOctaves:ti};function ts(t,e,r,i){let{style:n,vars:s,transform:o,transformOrigin:a}=t,l=!1,u=!1,h=!0;for(let t in e){let r=e[t];if(O(t)){s[t]=r;continue}let i=tn[t],c=W(r,i);if(R.has(t)){if(l=!0,o[t]=c,!h)continue;r!==(i.default||0)&&(h=!1)}else t.startsWith("origin")?(u=!0,a[t]=c):n[t]=c}if(!e.transform&&(l||i?n.transform=function(t,{enableHardwareAcceleration:e=!0,allowTransformNone:r=!0},i,n){let s="";for(let e=0;e<B;e++){let r=D[e];if(void 0!==t[r]){let e=F[r]||r;s+=`${e}(${t[r]}) `}}return e&&!t.z&&(s+="translateZ(0)"),s=s.trim(),n?s=n(t,i?"":s):r&&i&&(s="none"),s}(t.transform,r,h,i):n.transform&&(n.transform="none")),u){let{originX:t="50%",originY:e="50%",originZ:r=0}=a;n.transformOrigin=`${t} ${e} ${r}`}}let to=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function ta(t,e,r){for(let i in e)L(e[i])||j(i,r)||(t[i]=e[i])}let tl=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function tu(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||tl.has(t)}let th=t=>!tu(t);try{(n=require("@emotion/is-prop-valid").default)&&(th=t=>t.startsWith("on")?!tu(t):n(t))}catch(t){}function tc(t,e,r){return"string"==typeof t?t:Q.transform(e+r*t)}let td={offset:"stroke-dashoffset",array:"stroke-dasharray"},tp={offset:"strokeDashoffset",array:"strokeDasharray"};function tm(t,{attrX:e,attrY:r,attrScale:i,originX:n,originY:s,pathLength:o,pathSpacing:a=1,pathOffset:l=0,...u},h,c,d){if(ts(t,u,h,d),c){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:p,style:m,dimensions:f}=t;p.transform&&(f&&(m.transform=p.transform),delete p.transform),f&&(void 0!==n||void 0!==s||m.transform)&&(m.transformOrigin=function(t,e,r){let i=tc(e,t.x,t.width),n=tc(r,t.y,t.height);return`${i} ${n}`}(f,void 0!==n?n:.5,void 0!==s?s:.5)),void 0!==e&&(p.x=e),void 0!==r&&(p.y=r),void 0!==i&&(p.scale=i),void 0!==o&&function(t,e,r=1,i=0,n=!0){t.pathLength=1;let s=n?td:tp;t[s.offset]=Q.transform(-i);let o=Q.transform(e),a=Q.transform(r);t[s.array]=`${o} ${a}`}(p,o,a,l,!1)}let tf=()=>({...to(),attrs:{}}),tg=t=>"string"==typeof t&&"svg"===t.toLowerCase();function tv(t,{style:e,vars:r},i,n){for(let s in Object.assign(t.style,e,n&&n.getProjectionStyles(i)),r)t.style.setProperty(s,r[s])}let ty=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function tb(t,e,r,i){for(let r in tv(t,e,void 0,i),e.attrs)t.setAttribute(ty.has(r)?r:d(r),e.attrs[r])}function tx(t,e){let{style:r}=t,i={};for(let n in r)(L(r[n])||e.style&&L(e.style[n])||j(n,t))&&(i[n]=r[n]);return i}function tw(t,e){let r=tx(t,e);for(let i in t)(L(t[i])||L(e[i]))&&(r[-1!==D.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return r}function tk(t,e,r,i={},n={}){return"function"==typeof e&&(e=e(void 0!==r?r:t.custom,i,n)),"string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e&&(e=e(void 0!==r?r:t.custom,i,n)),e}var tP=r(961);let tA=t=>Array.isArray(t),tT=t=>!!(t&&"object"==typeof t&&t.mix&&t.toValue),tS=t=>tA(t)?t[t.length-1]||0:t;function tE(t){let e=L(t)?t.get():t;return tT(e)?e.toValue():e}let tV=t=>(e,r)=>{let i=(0,o.useContext)(l),n=(0,o.useContext)(u.O),s=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e,onMount:r},i,n,s){let o={latestValues:function(t,e,r,i){let n={},s=i(t,{});for(let t in s)n[t]=tE(s[t]);let{initial:o,animate:a}=t,l=b(t),u=x(t);e&&u&&!l&&!1!==t.inherit&&(void 0===o&&(o=e.initial),void 0===a&&(a=e.animate));let h=!!r&&!1===r.initial,c=(h=h||!1===o)?a:o;return c&&"boolean"!=typeof c&&!g(c)&&(Array.isArray(c)?c:[c]).forEach(e=>{let r=tk(t,e);if(!r)return;let{transitionEnd:i,transition:s,...o}=r;for(let t in o){let e=o[t];if(Array.isArray(e)){let t=h?e.length-1:0;e=e[t]}null!==e&&(n[t]=e)}for(let t in i)n[t]=i[t]}),n}(i,n,s,t),renderState:e()};return r&&(o.mount=t=>r(i,t,o)),o})(t,e,i,n);return r?s():(0,tP.h)(s)};var tM=r(2363);let tC={useVisualState:tV({scrapeMotionValuesFromProps:tw,createRenderState:tf,onMount:(t,e,{renderState:r,latestValues:i})=>{tM.Wi.read(()=>{try{r.dimensions="function"==typeof e.getBBox?e.getBBox():e.getBoundingClientRect()}catch(t){r.dimensions={x:0,y:0,width:0,height:0}}}),tM.Wi.render(()=>{tm(r,i,{enableHardwareAcceleration:!1},tg(e.tagName),t.transformTemplate),tb(e,r)})}})},tD={useVisualState:tV({scrapeMotionValuesFromProps:tx,createRenderState:to})};function tR(t,e,r,i={passive:!0}){return t.addEventListener(e,r,i),()=>t.removeEventListener(e,r)}let tj=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function tL(t,e="page"){return{point:{x:t[e+"X"],y:t[e+"Y"]}}}let tF=t=>e=>tj(e)&&t(e,tL(e));function tB(t,e,r,i){return tR(t,e,tF(r),i)}let tz=(t,e)=>r=>e(t(r)),tO=(...t)=>t.reduce(tz);function tI(t){let e=null;return()=>null===e&&(e=t,()=>{e=null})}let tW=tI("dragHorizontal"),tU=tI("dragVertical");function t$(t){let e=!1;if("y"===t)e=tU();else if("x"===t)e=tW();else{let t=tW(),r=tU();t&&r?e=()=>{t(),r()}:(t&&t(),r&&r())}return e}function tN(){let t=t$(!0);return!t||(t(),!1)}class tZ{constructor(t){this.isMounted=!1,this.node=t}update(){}}function tH(t,e){let r="onHover"+(e?"Start":"End");return tB(t.current,"pointer"+(e?"enter":"leave"),(i,n)=>{if("touch"===i.pointerType||tN())return;let s=t.getProps();t.animationState&&s.whileHover&&t.animationState.setActive("whileHover",e),s[r]&&tM.Wi.update(()=>s[r](i,n))},{passive:!t.getProps()[r]})}class tG extends tZ{mount(){this.unmount=tO(tH(this.node,!0),tH(this.node,!1))}unmount(){}}class tY extends tZ{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=tO(tR(this.node.current,"focus",()=>this.onFocus()),tR(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let tX=(t,e)=>!!e&&(t===e||tX(t,e.parentElement));var tq=r(6977);function tK(t,e){if(!e)return;let r=new PointerEvent("pointer"+t);e(r,tL(r))}class t_ extends tZ{constructor(){super(...arguments),this.removeStartListeners=tq.Z,this.removeEndListeners=tq.Z,this.removeAccessibleListeners=tq.Z,this.startPointerPress=(t,e)=>{if(this.isPressing)return;this.removeEndListeners();let r=this.node.getProps(),i=tB(window,"pointerup",(t,e)=>{if(!this.checkPressEnd())return;let{onTap:r,onTapCancel:i,globalTapTarget:n}=this.node.getProps();tM.Wi.update(()=>{n||tX(this.node.current,t.target)?r&&r(t,e):i&&i(t,e)})},{passive:!(r.onTap||r.onPointerUp)}),n=tB(window,"pointercancel",(t,e)=>this.cancelPress(t,e),{passive:!(r.onTapCancel||r.onPointerCancel)});this.removeEndListeners=tO(i,n),this.startPress(t,e)},this.startAccessiblePress=()=>{let t=tR(this.node.current,"keydown",t=>{"Enter"!==t.key||this.isPressing||(this.removeEndListeners(),this.removeEndListeners=tR(this.node.current,"keyup",t=>{"Enter"===t.key&&this.checkPressEnd()&&tK("up",(t,e)=>{let{onTap:r}=this.node.getProps();r&&tM.Wi.update(()=>r(t,e))})}),tK("down",(t,e)=>{this.startPress(t,e)}))}),e=tR(this.node.current,"blur",()=>{this.isPressing&&tK("cancel",(t,e)=>this.cancelPress(t,e))});this.removeAccessibleListeners=tO(t,e)}}startPress(t,e){this.isPressing=!0;let{onTapStart:r,whileTap:i}=this.node.getProps();i&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),r&&tM.Wi.update(()=>r(t,e))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!tN()}cancelPress(t,e){if(!this.checkPressEnd())return;let{onTapCancel:r}=this.node.getProps();r&&tM.Wi.update(()=>r(t,e))}mount(){let t=this.node.getProps(),e=tB(t.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(t.onTapStart||t.onPointerStart)}),r=tR(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=tO(e,r)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}let tJ=new WeakMap,tQ=new WeakMap,t0=t=>{let e=tJ.get(t.target);e&&e(t)},t1=t=>{t.forEach(t0)},t2={some:0,all:1};class t5 extends tZ{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:r,amount:i="some",once:n}=t,s={root:e?e.current:void 0,rootMargin:r,threshold:"number"==typeof i?i:t2[i]};return function(t,e,r){let i=function({root:t,...e}){let r=t||document;tQ.has(r)||tQ.set(r,{});let i=tQ.get(r),n=JSON.stringify(e);return i[n]||(i[n]=new IntersectionObserver(t1,{root:t,...e})),i[n]}(e);return tJ.set(t,r),i.observe(t),()=>{tJ.delete(t),i.unobserve(t)}}(this.node.current,s,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,n&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:r,onViewportLeave:i}=this.node.getProps(),s=e?r:i;s&&s(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return r=>t[r]!==e[r]}(t,e))&&this.startObserver()}unmount(){}}function t3(t,e){if(!Array.isArray(e))return!1;let r=e.length;if(r!==t.length)return!1;for(let i=0;i<r;i++)if(e[i]!==t[i])return!1;return!0}function t6(t,e,r){let i=t.getProps();return tk(i,e,void 0!==r?r:i.custom,function(t){let e={};return t.values.forEach((t,r)=>e[r]=t.get()),e}(t),function(t){let e={};return t.values.forEach((t,r)=>e[r]=t.getVelocity()),e}(t))}var t4=r(6567);let t9=t=>1e3*t,t8=t=>t/1e3,t7={current:!1},et=t=>Array.isArray(t)&&"number"==typeof t[0],ee=([t,e,r,i])=>`cubic-bezier(${t}, ${e}, ${r}, ${i})`,er={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:ee([0,.65,.55,1]),circOut:ee([.55,0,1,.45]),backIn:ee([.31,.01,.66,-.59]),backOut:ee([.33,1.53,.69,.99])},ei=(t,e,r)=>(((1-3*r+3*e)*t+(3*r-6*e))*t+3*e)*t;function en(t,e,r,i){if(t===e&&r===i)return tq.Z;let n=e=>(function(t,e,r,i,n){let s,o;let a=0;do(s=ei(o=e+(r-e)/2,i,n)-t)>0?r=o:e=o;while(Math.abs(s)>1e-7&&++a<12);return o})(e,0,1,t,r);return t=>0===t||1===t?t:ei(n(t),e,i)}let es=en(.42,0,1,1),eo=en(0,0,.58,1),ea=en(.42,0,.58,1),el=t=>Array.isArray(t)&&"number"!=typeof t[0],eu=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,eh=t=>e=>1-t(1-e),ec=t=>1-Math.sin(Math.acos(t)),ed=eh(ec),ep=eu(ec),em=en(.33,1.53,.69,.99),ef=eh(em),eg=eu(ef),ev={linear:tq.Z,easeIn:es,easeInOut:ea,easeOut:eo,circIn:ec,circInOut:ep,circOut:ed,backIn:ef,backInOut:eg,backOut:em,anticipate:t=>(t*=2)<1?.5*ef(t):.5*(2-Math.pow(2,-10*(t-1)))},ey=t=>{if(Array.isArray(t)){(0,t4.k)(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,r,i,n]=t;return en(e,r,i,n)}return"string"==typeof t?((0,t4.k)(void 0!==ev[t],`Invalid easing type '${t}'`),ev[t]):t},eb=(t,e)=>r=>!!(q(r)&&X.test(r)&&r.startsWith(t)||e&&Object.prototype.hasOwnProperty.call(r,e)),ex=(t,e,r)=>i=>{if(!q(i))return i;let[n,s,o,a]=i.match(G);return{[t]:parseFloat(n),[e]:parseFloat(s),[r]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},ew=t=>U(0,255,t),ek={...$,transform:t=>Math.round(ew(t))},eP={test:eb("rgb","red"),parse:ex("red","green","blue"),transform:({red:t,green:e,blue:r,alpha:i=1})=>"rgba("+ek.transform(t)+", "+ek.transform(e)+", "+ek.transform(r)+", "+H(N.transform(i))+")"},eA={test:eb("#"),parse:function(t){let e="",r="",i="",n="";return t.length>5?(e=t.substring(1,3),r=t.substring(3,5),i=t.substring(5,7),n=t.substring(7,9)):(e=t.substring(1,2),r=t.substring(2,3),i=t.substring(3,4),n=t.substring(4,5),e+=e,r+=r,i+=i,n+=n),{red:parseInt(e,16),green:parseInt(r,16),blue:parseInt(i,16),alpha:n?parseInt(n,16)/255:1}},transform:eP.transform},eT={test:eb("hsl","hue"),parse:ex("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:r,alpha:i=1})=>"hsla("+Math.round(t)+", "+J.transform(H(e))+", "+J.transform(H(r))+", "+H(N.transform(i))+")"},eS={test:t=>eP.test(t)||eA.test(t)||eT.test(t),parse:t=>eP.test(t)?eP.parse(t):eT.test(t)?eT.parse(t):eA.parse(t),transform:t=>q(t)?t:t.hasOwnProperty("red")?eP.transform(t):eT.transform(t)},eE=(t,e,r)=>-r*t+r*e+t;function eV(t,e,r){return(r<0&&(r+=1),r>1&&(r-=1),r<1/6)?t+(e-t)*6*r:r<.5?e:r<2/3?t+(e-t)*(2/3-r)*6:t}let eM=(t,e,r)=>{let i=t*t;return Math.sqrt(Math.max(0,r*(e*e-i)+i))},eC=[eA,eP,eT],eD=t=>eC.find(e=>e.test(t));function eR(t){let e=eD(t);(0,t4.k)(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`);let r=e.parse(t);return e===eT&&(r=function({hue:t,saturation:e,lightness:r,alpha:i}){t/=360,r/=100;let n=0,s=0,o=0;if(e/=100){let i=r<.5?r*(1+e):r+e-r*e,a=2*r-i;n=eV(a,i,t+1/3),s=eV(a,i,t),o=eV(a,i,t-1/3)}else n=s=o=r;return{red:Math.round(255*n),green:Math.round(255*s),blue:Math.round(255*o),alpha:i}}(r)),r}let ej=(t,e)=>{let r=eR(t),i=eR(e),n={...r};return t=>(n.red=eM(r.red,i.red,t),n.green=eM(r.green,i.green,t),n.blue=eM(r.blue,i.blue,t),n.alpha=eE(r.alpha,i.alpha,t),eP.transform(n))},eL={regex:/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,countKey:"Vars",token:"${v}",parse:tq.Z},eF={regex:Y,countKey:"Colors",token:"${c}",parse:eS.parse},eB={regex:G,countKey:"Numbers",token:"${n}",parse:$.parse};function ez(t,{regex:e,countKey:r,token:i,parse:n}){let s=t.tokenised.match(e);s&&(t["num"+r]=s.length,t.tokenised=t.tokenised.replace(e,i),t.values.push(...s.map(n)))}function eO(t){let e=t.toString(),r={value:e,tokenised:e,values:[],numVars:0,numColors:0,numNumbers:0};return r.value.includes("var(--")&&ez(r,eL),ez(r,eF),ez(r,eB),r}function eI(t){return eO(t).values}function eW(t){let{values:e,numColors:r,numVars:i,tokenised:n}=eO(t),s=e.length;return t=>{let e=n;for(let n=0;n<s;n++)e=n<i?e.replace(eL.token,t[n]):n<i+r?e.replace(eF.token,eS.transform(t[n])):e.replace(eB.token,H(t[n]));return e}}let eU=t=>"number"==typeof t?0:t,e$={test:function(t){var e,r;return isNaN(t)&&q(t)&&((null===(e=t.match(G))||void 0===e?void 0:e.length)||0)+((null===(r=t.match(Y))||void 0===r?void 0:r.length)||0)>0},parse:eI,createTransformer:eW,getAnimatableNone:function(t){let e=eI(t);return eW(t)(e.map(eU))}},eN=(t,e)=>r=>`${r>0?e:t}`;function eZ(t,e){return"number"==typeof t?r=>eE(t,e,r):eS.test(t)?ej(t,e):t.startsWith("var(")?eN(t,e):eY(t,e)}let eH=(t,e)=>{let r=[...t],i=r.length,n=t.map((t,r)=>eZ(t,e[r]));return t=>{for(let e=0;e<i;e++)r[e]=n[e](t);return r}},eG=(t,e)=>{let r={...t,...e},i={};for(let n in r)void 0!==t[n]&&void 0!==e[n]&&(i[n]=eZ(t[n],e[n]));return t=>{for(let e in i)r[e]=i[e](t);return r}},eY=(t,e)=>{let r=e$.createTransformer(e),i=eO(t),n=eO(e);return i.numVars===n.numVars&&i.numColors===n.numColors&&i.numNumbers>=n.numNumbers?tO(eH(i.values,n.values),r):((0,t4.K)(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),eN(t,e))},eX=(t,e,r)=>{let i=e-t;return 0===i?1:(r-t)/i},eq=(t,e)=>r=>eE(t,e,r);function eK(t,e,{clamp:r=!0,ease:i,mixer:n}={}){let s=t.length;if((0,t4.k)(s===e.length,"Both input and output ranges must be the same length"),1===s)return()=>e[0];t[0]>t[s-1]&&(t=[...t].reverse(),e=[...e].reverse());let o=function(t,e,r){let i=[],n=r||function(t){if("number"==typeof t);else if("string"==typeof t)return eS.test(t)?ej:eY;else if(Array.isArray(t))return eH;else if("object"==typeof t)return eG;return eq}(t[0]),s=t.length-1;for(let r=0;r<s;r++){let s=n(t[r],t[r+1]);e&&(s=tO(Array.isArray(e)?e[r]||tq.Z:e,s)),i.push(s)}return i}(e,i,n),a=o.length,l=e=>{let r=0;if(a>1)for(;r<t.length-2&&!(e<t[r+1]);r++);let i=eX(t[r],t[r+1],e);return o[r](i)};return r?e=>l(U(t[0],t[s-1],e)):l}function e_({duration:t=300,keyframes:e,times:r,ease:i="easeInOut"}){let n=el(i)?i.map(ey):ey(i),s={done:!1,value:e[0]},o=eK((r&&r.length===e.length?r:function(t){let e=[0];return function(t,e){let r=t[t.length-1];for(let i=1;i<=e;i++){let n=eX(0,e,i);t.push(eE(r,1,n))}}(e,t.length-1),e}(e)).map(e=>e*t),e,{ease:Array.isArray(n)?n:e.map(()=>n||ea).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(s.value=o(e),s.done=e>=t,s)}}function eJ(t,e,r){var i,n;let s=Math.max(e-5,0);return i=r-t(s),(n=e-s)?1e3/n*i:0}function eQ(t,e){return t*Math.sqrt(1-e*e)}let e0=["duration","bounce"],e1=["stiffness","damping","mass"];function e2(t,e){return e.some(e=>void 0!==t[e])}function e5({keyframes:t,restDelta:e,restSpeed:r,...i}){let n;let s=t[0],o=t[t.length-1],a={done:!1,value:s},{stiffness:l,damping:u,mass:h,duration:c,velocity:d,isResolvedFromDuration:p}=function(t){let e={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...t};if(!e2(t,e1)&&e2(t,e0)){let r=function({duration:t=800,bounce:e=.25,velocity:r=0,mass:i=1}){let n,s;(0,t4.K)(t<=t9(10),"Spring duration must be 10 seconds or less");let o=1-e;o=U(.05,1,o),t=U(.01,10,t8(t)),o<1?(n=e=>{let i=e*o,n=i*t;return .001-(i-r)/eQ(e,o)*Math.exp(-n)},s=e=>{let i=e*o*t,s=Math.pow(o,2)*Math.pow(e,2)*t,a=eQ(Math.pow(e,2),o);return(i*r+r-s)*Math.exp(-i)*(-n(e)+.001>0?-1:1)/a}):(n=e=>-.001+Math.exp(-e*t)*((e-r)*t+1),s=e=>t*t*(r-e)*Math.exp(-e*t));let a=function(t,e,r){let i=r;for(let r=1;r<12;r++)i-=t(i)/e(i);return i}(n,s,5/t);if(t=t9(t),isNaN(a))return{stiffness:100,damping:10,duration:t};{let e=Math.pow(a,2)*i;return{stiffness:e,damping:2*o*Math.sqrt(i*e),duration:t}}}(t);(e={...e,...r,mass:1}).isResolvedFromDuration=!0}return e}({...i,velocity:-t8(i.velocity||0)}),m=d||0,f=u/(2*Math.sqrt(l*h)),g=o-s,v=t8(Math.sqrt(l/h)),y=5>Math.abs(g);if(r||(r=y?.01:2),e||(e=y?.005:.5),f<1){let t=eQ(v,f);n=e=>o-Math.exp(-f*v*e)*((m+f*v*g)/t*Math.sin(t*e)+g*Math.cos(t*e))}else if(1===f)n=t=>o-Math.exp(-v*t)*(g+(m+v*g)*t);else{let t=v*Math.sqrt(f*f-1);n=e=>{let r=Math.exp(-f*v*e),i=Math.min(t*e,300);return o-r*((m+f*v*g)*Math.sinh(i)+t*g*Math.cosh(i))/t}}return{calculatedDuration:p&&c||null,next:t=>{let i=n(t);if(p)a.done=t>=c;else{let s=m;0!==t&&(s=f<1?eJ(n,t,i):0);let l=Math.abs(s)<=r,u=Math.abs(o-i)<=e;a.done=l&&u}return a.value=a.done?o:i,a}}}function e3({keyframes:t,velocity:e=0,power:r=.8,timeConstant:i=325,bounceDamping:n=10,bounceStiffness:s=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:h}){let c,d;let p=t[0],m={done:!1,value:p},f=t=>void 0!==a&&t<a||void 0!==l&&t>l,g=t=>void 0===a?l:void 0===l?a:Math.abs(a-t)<Math.abs(l-t)?a:l,v=r*e,y=p+v,b=void 0===o?y:o(y);b!==y&&(v=b-p);let x=t=>-v*Math.exp(-t/i),w=t=>b+x(t),k=t=>{let e=x(t),r=w(t);m.done=Math.abs(e)<=u,m.value=m.done?b:r},P=t=>{f(m.value)&&(c=t,d=e5({keyframes:[m.value,g(m.value)],velocity:eJ(w,t,m.value),damping:n,stiffness:s,restDelta:u,restSpeed:h}))};return P(0),{calculatedDuration:null,next:t=>{let e=!1;return(d||void 0!==c||(e=!0,k(t),P(t)),void 0!==c&&t>c)?d.next(t-c):(e||k(t),m)}}}let e6=t=>{let e=({timestamp:e})=>t(e);return{start:()=>tM.Wi.update(e,!0),stop:()=>(0,tM.Pn)(e),now:()=>tM.frameData.isProcessing?tM.frameData.timestamp:performance.now()}};function e4(t){let e=0,r=t.next(e);for(;!r.done&&e<2e4;)e+=50,r=t.next(e);return e>=2e4?1/0:e}let e9={decay:e3,inertia:e3,tween:e_,keyframes:e_,spring:e5};function e8({autoplay:t=!0,delay:e=0,driver:r=e6,keyframes:i,type:n="keyframes",repeat:s=0,repeatDelay:o=0,repeatType:a="loop",onPlay:l,onStop:u,onComplete:h,onUpdate:c,...d}){let p,m,f,g,v,y=1,b=!1,x=()=>{m=new Promise(t=>{p=t})};x();let w=e9[n]||e_;w!==e_&&"number"!=typeof i[0]&&(g=eK([0,100],i,{clamp:!1}),i=[0,100]);let k=w({...d,keyframes:i});"mirror"===a&&(v=w({...d,keyframes:[...i].reverse(),velocity:-(d.velocity||0)}));let P="idle",A=null,T=null,S=null;null===k.calculatedDuration&&s&&(k.calculatedDuration=e4(k));let{calculatedDuration:E}=k,V=1/0,M=1/0;null!==E&&(M=(V=E+o)*(s+1)-o);let C=0,D=t=>{if(null===T)return;y>0&&(T=Math.min(T,t)),y<0&&(T=Math.min(t-M/y,T));let r=(C=null!==A?A:Math.round(t-T)*y)-e*(y>=0?1:-1),n=y>=0?r<0:r>M;C=Math.max(r,0),"finished"===P&&null===A&&(C=M);let l=C,u=k;if(s){let t=Math.min(C,M)/V,e=Math.floor(t),r=t%1;!r&&t>=1&&(r=1),1===r&&e--,(e=Math.min(e,s+1))%2&&("reverse"===a?(r=1-r,o&&(r-=o/V)):"mirror"===a&&(u=v)),l=U(0,1,r)*V}let h=n?{done:!1,value:i[0]}:u.next(l);g&&(h.value=g(h.value));let{done:d}=h;n||null===E||(d=y>=0?C>=M:C<=0);let p=null===A&&("finished"===P||"running"===P&&d);return c&&c(h.value),p&&L(),h},R=()=>{f&&f.stop(),f=void 0},j=()=>{P="idle",R(),p(),x(),T=S=null},L=()=>{P="finished",h&&h(),R(),p()},F=()=>{if(b)return;f||(f=r(D));let t=f.now();l&&l(),null!==A?T=t-A:T&&"finished"!==P||(T=t),"finished"===P&&x(),S=T,A=null,P="running",f.start()};t&&F();let B={then:(t,e)=>m.then(t,e),get time(){return t8(C)},set time(newTime){C=newTime=t9(newTime),null===A&&f&&0!==y?T=f.now()-newTime/y:A=newTime},get duration(){return t8(null===k.calculatedDuration?e4(k):k.calculatedDuration)},get speed(){return y},set speed(newSpeed){if(newSpeed===y||!f)return;y=newSpeed,B.time=t8(C)},get state(){return P},play:F,pause:()=>{P="paused",A=C},stop:()=>{b=!0,"idle"!==P&&(P="idle",u&&u(),j())},cancel:()=>{null!==S&&D(S),j()},complete:()=>{P="finished"},sample:t=>(T=0,D(t))};return B}let e7=(s=()=>Object.hasOwnProperty.call(Element.prototype,"animate"),()=>(void 0===i&&(i=s()),i)),rt=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),re=(t,e)=>"spring"===e.type||"backgroundColor"===t||!function t(e){return!!(!e||"string"==typeof e&&er[e]||et(e)||Array.isArray(e)&&e.every(t))}(e.ease),rr={type:"spring",stiffness:500,damping:25,restSpeed:10},ri=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),rn={type:"keyframes",duration:.8},rs={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},ro=(t,{keyframes:e})=>e.length>2?rn:R.has(t)?t.startsWith("scale")?ri(e[1]):rr:rs,ra=(t,e)=>"zIndex"!==t&&!!("number"==typeof e||Array.isArray(e)||"string"==typeof e&&(e$.test(e)||"0"===e)&&!e.startsWith("url(")),rl=new Set(["brightness","contrast","saturate","opacity"]);function ru(t){let[e,r]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[i]=r.match(G)||[];if(!i)return t;let n=r.replace(i,""),s=rl.has(e)?1:0;return i!==r&&(s*=100),e+"("+s+n+")"}let rh=/([a-z-]*)\(.*?\)/g,rc={...e$,getAnimatableNone:t=>{let e=t.match(rh);return e?e.map(ru).join(" "):t}},rd={...tn,color:eS,backgroundColor:eS,outlineColor:eS,fill:eS,stroke:eS,borderColor:eS,borderTopColor:eS,borderRightColor:eS,borderBottomColor:eS,borderLeftColor:eS,filter:rc,WebkitFilter:rc},rp=t=>rd[t];function rm(t,e){let r=rp(t);return r!==rc&&(r=e$),r.getAnimatableNone?r.getAnimatableNone(e):void 0}let rf=t=>/^0[^.\s]+$/.test(t);function rg(t,e){return t[e]||t.default||t}let rv={skipAnimations:!1},ry=(t,e,r,i={})=>n=>{let s=rg(i,t)||{},o=s.delay||i.delay||0,{elapsed:a=0}=i;a-=t9(o);let l=function(t,e,r,i){let n,s;let o=ra(e,r);n=Array.isArray(r)?[...r]:[null,r];let a=void 0!==i.from?i.from:t.get(),l=[];for(let t=0;t<n.length;t++){var u;null===n[t]&&(n[t]=0===t?a:n[t-1]),("number"==typeof(u=n[t])?0===u:null!==u?"none"===u||"0"===u||rf(u):void 0)&&l.push(t),"string"==typeof n[t]&&"none"!==n[t]&&"0"!==n[t]&&(s=n[t])}if(o&&l.length&&s)for(let t=0;t<l.length;t++)n[l[t]]=rm(e,s);return n}(e,t,r,s),u=l[0],h=l[l.length-1],c=ra(t,u),d=ra(t,h);(0,t4.K)(c===d,`You are trying to animate ${t} from "${u}" to "${h}". ${u} is not an animatable value - to enable this animation set ${u} to a value animatable to ${h} via the \`style\` property.`);let p={keyframes:l,velocity:e.getVelocity(),ease:"easeOut",...s,delay:-a,onUpdate:t=>{e.set(t),s.onUpdate&&s.onUpdate(t)},onComplete:()=>{n(),s.onComplete&&s.onComplete()}};if(!function({when:t,delay:e,delayChildren:r,staggerChildren:i,staggerDirection:n,repeat:s,repeatType:o,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(s)&&(p={...p,...ro(t,p)}),p.duration&&(p.duration=t9(p.duration)),p.repeatDelay&&(p.repeatDelay=t9(p.repeatDelay)),!c||!d||t7.current||!1===s.type||rv.skipAnimations)return function({keyframes:t,delay:e,onUpdate:r,onComplete:i}){let n=()=>(r&&r(t[t.length-1]),i&&i(),{time:0,speed:1,duration:0,play:tq.Z,pause:tq.Z,stop:tq.Z,then:t=>(t(),Promise.resolve()),cancel:tq.Z,complete:tq.Z});return e?e8({keyframes:[0,1],duration:0,delay:e,onComplete:n}):n()}(t7.current?{...p,delay:0}:p);if(!i.isHandoff&&e.owner&&e.owner.current instanceof HTMLElement&&!e.owner.getProps().onUpdate){let r=function(t,e,{onUpdate:r,onComplete:i,...n}){let s,o;if(!(e7()&&rt.has(e)&&!n.repeatDelay&&"mirror"!==n.repeatType&&0!==n.damping&&"inertia"!==n.type))return!1;let a=!1,l=!1,u=()=>{o=new Promise(t=>{s=t})};u();let{keyframes:h,duration:c=300,ease:d,times:p}=n;if(re(e,n)){let t=e8({...n,repeat:0,delay:0}),e={done:!1,value:h[0]},r=[],i=0;for(;!e.done&&i<2e4;)e=t.sample(i),r.push(e.value),i+=10;p=void 0,h=r,c=i-10,d="linear"}let m=function(t,e,r,{delay:i=0,duration:n,repeat:s=0,repeatType:o="loop",ease:a,times:l}={}){let u={[e]:r};l&&(u.offset=l);let h=function t(e){if(e)return et(e)?ee(e):Array.isArray(e)?e.map(t):er[e]}(a);return Array.isArray(h)&&(u.easing=h),t.animate(u,{delay:i,duration:n,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:s+1,direction:"reverse"===o?"alternate":"normal"})}(t.owner.current,e,h,{...n,duration:c,ease:d,times:p}),f=()=>{l=!1,m.cancel()},g=()=>{l=!0,tM.Wi.update(f),s(),u()};return m.onfinish=()=>{l||(t.set(function(t,{repeat:e,repeatType:r="loop"}){let i=e&&"loop"!==r&&e%2==1?0:t.length-1;return t[i]}(h,n)),i&&i(),g())},{then:(t,e)=>o.then(t,e),attachTimeline:t=>(m.timeline=t,m.onfinish=null,tq.Z),get time(){return t8(m.currentTime||0)},set time(newTime){m.currentTime=t9(newTime)},get speed(){return m.playbackRate},set speed(newSpeed){m.playbackRate=newSpeed},get duration(){return t8(c)},play:()=>{a||(m.play(),(0,tM.Pn)(f))},pause:()=>m.pause(),stop:()=>{if(a=!0,"idle"===m.playState)return;let{currentTime:e}=m;if(e){let r=e8({...n,autoplay:!1});t.setWithVelocity(r.sample(e-10).value,r.sample(e).value,10)}g()},complete:()=>{l||m.finish()},cancel:g}}(e,t,p);if(r)return r}return e8(p)};function rb(t){return!!(L(t)&&t.add)}let rx=t=>/^\-?\d*\.?\d+$/.test(t);function rw(t,e){-1===t.indexOf(e)&&t.push(e)}function rk(t,e){let r=t.indexOf(e);r>-1&&t.splice(r,1)}class rP{constructor(){this.subscriptions=[]}add(t){return rw(this.subscriptions,t),()=>rk(this.subscriptions,t)}notify(t,e,r){let i=this.subscriptions.length;if(i){if(1===i)this.subscriptions[0](t,e,r);else for(let n=0;n<i;n++){let i=this.subscriptions[n];i&&i(t,e,r)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let rA=t=>!isNaN(parseFloat(t)),rT={current:void 0};class rS{constructor(t,e={}){this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(t,e=!0)=>{this.prev=this.current,this.current=t;let{delta:r,timestamp:i}=tM.frameData;this.lastUpdated!==i&&(this.timeDelta=r,this.lastUpdated=i,tM.Wi.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),e&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>tM.Wi.postRender(this.velocityCheck),this.velocityCheck=({timestamp:t})=>{t!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=t,this.canTrackVelocity=rA(this.current),this.owner=e.owner}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new rP);let r=this.events[t].add(e);return"change"===t?()=>{r(),tM.Wi.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,r){this.set(e),this.prev=t,this.timeDelta=r}jump(t){this.updateAndNotify(t),this.prev=t,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return rT.current&&rT.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t,e;return this.canTrackVelocity?(t=parseFloat(this.current)-parseFloat(this.prev),(e=this.timeDelta)?1e3/e*t:0):0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function rE(t,e){return new rS(t,e)}let rV=t=>e=>e.test(t),rM=[$,Q,J,_,te,tt,{test:t=>"auto"===t,parse:t=>t}],rC=t=>rM.find(rV(t)),rD=[...rM,eS,e$],rR=t=>rD.find(rV(t));function rj(t,e,{delay:r=0,transitionOverride:i,type:n}={}){let{transition:s=t.getDefaultTransition(),transitionEnd:o,...a}=t.makeTargetAnimatable(e),l=t.getValue("willChange");i&&(s=i);let u=[],h=n&&t.animationState&&t.animationState.getState()[n];for(let e in a){let i=t.getValue(e),n=a[e];if(!i||void 0===n||h&&function({protectedKeys:t,needsAnimating:e},r){let i=t.hasOwnProperty(r)&&!0!==e[r];return e[r]=!1,i}(h,e))continue;let o={delay:r,elapsed:0,...rg(s||{},e)};if(window.HandoffAppearAnimations){let r=t.getProps()[p];if(r){let t=window.HandoffAppearAnimations(r,e,i,tM.Wi);null!==t&&(o.elapsed=t,o.isHandoff=!0)}}let c=!o.isHandoff&&!function(t,e){let r=t.get();if(!Array.isArray(e))return r!==e;for(let t=0;t<e.length;t++)if(e[t]!==r)return!0}(i,n);if("spring"===o.type&&(i.getVelocity()||o.velocity)&&(c=!1),i.animation&&(c=!1),c)continue;i.start(ry(e,i,n,t.shouldReduceMotion&&R.has(e)?{type:!1}:o));let d=i.animation;rb(l)&&(l.add(e),d.then(()=>l.remove(e))),u.push(d)}return o&&Promise.all(u).then(()=>{o&&function(t,e){let r=t6(t,e),{transitionEnd:i={},transition:n={},...s}=r?t.makeTargetAnimatable(r,!1):{};for(let e in s={...s,...i}){let r=tS(s[e]);t.hasValue(e)?t.getValue(e).set(r):t.addValue(e,rE(r))}}(t,o)}),u}function rL(t,e,r={}){let i=t6(t,e,r.custom),{transition:n=t.getDefaultTransition()||{}}=i||{};r.transitionOverride&&(n=r.transitionOverride);let s=i?()=>Promise.all(rj(t,i,r)):()=>Promise.resolve(),o=t.variantChildren&&t.variantChildren.size?(i=0)=>{let{delayChildren:s=0,staggerChildren:o,staggerDirection:a}=n;return function(t,e,r=0,i=0,n=1,s){let o=[],a=(t.variantChildren.size-1)*i,l=1===n?(t=0)=>t*i:(t=0)=>a-t*i;return Array.from(t.variantChildren).sort(rF).forEach((t,i)=>{t.notify("AnimationStart",e),o.push(rL(t,e,{...s,delay:r+l(i)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(o)}(t,e,s+i,o,a,r)}:()=>Promise.resolve(),{when:a}=n;if(!a)return Promise.all([s(),o(r.delay)]);{let[t,e]="beforeChildren"===a?[s,o]:[o,s];return t().then(()=>e())}}function rF(t,e){return t.sortNodePosition(e)}let rB=[...v].reverse(),rz=v.length;function rO(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}class rI extends tZ{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:r})=>(function(t,e,r={}){let i;if(t.notify("AnimationStart",e),Array.isArray(e))i=Promise.all(e.map(e=>rL(t,e,r)));else if("string"==typeof e)i=rL(t,e,r);else{let n="function"==typeof e?t6(t,e,r.custom):e;i=Promise.all(rj(t,n,r))}return i.then(()=>t.notify("AnimationComplete",e))})(t,e,r))),r={animate:rO(!0),whileInView:rO(),whileHover:rO(),whileTap:rO(),whileDrag:rO(),whileFocus:rO(),exit:rO()},i=!0,n=(e,r)=>{let i=t6(t,r);if(i){let{transition:t,transitionEnd:r,...n}=i;e={...e,...n,...r}}return e};function s(s,o){let a=t.getProps(),l=t.getVariantContext(!0)||{},u=[],h=new Set,c={},d=1/0;for(let e=0;e<rz;e++){var p;let m=rB[e],v=r[m],y=void 0!==a[m]?a[m]:l[m],b=f(y),x=m===o?v.isActive:null;!1===x&&(d=e);let w=y===l[m]&&y!==a[m]&&b;if(w&&i&&t.manuallyAnimateOnMount&&(w=!1),v.protectedKeys={...c},!v.isActive&&null===x||!y&&!v.prevProp||g(y)||"boolean"==typeof y)continue;let k=(p=v.prevProp,("string"==typeof y?y!==p:!!Array.isArray(y)&&!t3(y,p))||m===o&&v.isActive&&!w&&b||e>d&&b),P=!1,A=Array.isArray(y)?y:[y],T=A.reduce(n,{});!1===x&&(T={});let{prevResolvedValues:S={}}=v,E={...S,...T},V=t=>{k=!0,h.has(t)&&(P=!0,h.delete(t)),v.needsAnimating[t]=!0};for(let t in E){let e=T[t],r=S[t];if(!c.hasOwnProperty(t))(tA(e)&&tA(r)?t3(e,r):e===r)?void 0!==e&&h.has(t)?V(t):v.protectedKeys[t]=!0:void 0!==e?V(t):h.add(t)}v.prevProp=y,v.prevResolvedValues=T,v.isActive&&(c={...c,...T}),i&&t.blockInitialAnimation&&(k=!1),k&&(!w||P)&&u.push(...A.map(t=>({animation:t,options:{type:m,...s}})))}if(h.size){let e={};h.forEach(r=>{let i=t.getBaseTarget(r);void 0!==i&&(e[r]=i)}),u.push({animation:e})}let m=!!u.length;return i&&(!1===a.initial||a.initial===a.animate)&&!t.manuallyAnimateOnMount&&(m=!1),i=!1,m?e(u):Promise.resolve()}return{animateChanges:s,setActive:function(e,i,n){var o;if(r[e].isActive===i)return Promise.resolve();null===(o=t.variantChildren)||void 0===o||o.forEach(t=>{var r;return null===(r=t.animationState)||void 0===r?void 0:r.setActive(e,i)}),r[e].isActive=i;let a=s(n,e);for(let t in r)r[t].protectedKeys={};return a},setAnimateFunction:function(r){e=r(t)},getState:()=>r}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();this.unmount(),g(t)&&(this.unmount=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){}}let rW=0;class rU extends tZ{constructor(){super(...arguments),this.id=rW++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e,custom:r}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let n=this.node.animationState.setActive("exit",!t,{custom:null!=r?r:this.node.getProps().custom});e&&!t&&n.then(()=>e(this.id))}mount(){let{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}let r$=(t,e)=>Math.abs(t-e);class rN{constructor(t,e,{transformPagePoint:r,contextWindow:i,dragSnapToOrigin:n=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{var t,e;if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let r=rG(this.lastMoveEventInfo,this.history),i=null!==this.startEvent,n=(t=r.offset,e={x:0,y:0},Math.sqrt(r$(t.x,e.x)**2+r$(t.y,e.y)**2)>=3);if(!i&&!n)return;let{point:s}=r,{timestamp:o}=tM.frameData;this.history.push({...s,timestamp:o});let{onStart:a,onMove:l}=this.handlers;i||(a&&a(this.lastMoveEvent,r),this.startEvent=this.lastMoveEvent),l&&l(this.lastMoveEvent,r)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=rZ(e,this.transformPagePoint),tM.Wi.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:r,onSessionEnd:i,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let s=rG("pointercancel"===t.type?this.lastMoveEventInfo:rZ(e,this.transformPagePoint),this.history);this.startEvent&&r&&r(t,s),i&&i(t,s)},!tj(t))return;this.dragSnapToOrigin=n,this.handlers=e,this.transformPagePoint=r,this.contextWindow=i||window;let s=rZ(tL(t),this.transformPagePoint),{point:o}=s,{timestamp:a}=tM.frameData;this.history=[{...o,timestamp:a}];let{onSessionStart:l}=e;l&&l(t,rG(s,this.history)),this.removeListeners=tO(tB(this.contextWindow,"pointermove",this.handlePointerMove),tB(this.contextWindow,"pointerup",this.handlePointerUp),tB(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),(0,tM.Pn)(this.updatePoint)}}function rZ(t,e){return e?{point:e(t.point)}:t}function rH(t,e){return{x:t.x-e.x,y:t.y-e.y}}function rG({point:t},e){return{point:t,delta:rH(t,rY(e)),offset:rH(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let r=t.length-1,i=null,n=rY(t);for(;r>=0&&(i=t[r],!(n.timestamp-i.timestamp>t9(.1)));)r--;if(!i)return{x:0,y:0};let s=t8(n.timestamp-i.timestamp);if(0===s)return{x:0,y:0};let o={x:(n.x-i.x)/s,y:(n.y-i.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,0)}}function rY(t){return t[t.length-1]}function rX(t){return t.max-t.min}function rq(t,e=0,r=.01){return Math.abs(t-e)<=r}function rK(t,e,r,i=.5){t.origin=i,t.originPoint=eE(e.min,e.max,t.origin),t.scale=rX(r)/rX(e),(rq(t.scale,1,1e-4)||isNaN(t.scale))&&(t.scale=1),t.translate=eE(r.min,r.max,t.origin)-t.originPoint,(rq(t.translate)||isNaN(t.translate))&&(t.translate=0)}function r_(t,e,r,i){rK(t.x,e.x,r.x,i?i.originX:void 0),rK(t.y,e.y,r.y,i?i.originY:void 0)}function rJ(t,e,r){t.min=r.min+e.min,t.max=t.min+rX(e)}function rQ(t,e,r){t.min=e.min-r.min,t.max=t.min+rX(e)}function r0(t,e,r){rQ(t.x,e.x,r.x),rQ(t.y,e.y,r.y)}function r1(t,e,r){return{min:void 0!==e?t.min+e:void 0,max:void 0!==r?t.max+r-(t.max-t.min):void 0}}function r2(t,e){let r=e.min-t.min,i=e.max-t.max;return e.max-e.min<t.max-t.min&&([r,i]=[i,r]),{min:r,max:i}}function r5(t,e,r){return{min:r3(t,e),max:r3(t,r)}}function r3(t,e){return"number"==typeof t?t:t[e]||0}let r6=()=>({translate:0,scale:1,origin:0,originPoint:0}),r4=()=>({x:r6(),y:r6()}),r9=()=>({min:0,max:0}),r8=()=>({x:r9(),y:r9()});function r7(t){return[t("x"),t("y")]}function it({top:t,left:e,right:r,bottom:i}){return{x:{min:e,max:r},y:{min:t,max:i}}}function ie(t){return void 0===t||1===t}function ir({scale:t,scaleX:e,scaleY:r}){return!ie(t)||!ie(e)||!ie(r)}function ii(t){return ir(t)||is(t)||t.z||t.rotate||t.rotateX||t.rotateY}function is(t){var e,r;return(e=t.x)&&"0%"!==e||(r=t.y)&&"0%"!==r}function io(t,e,r,i,n){return void 0!==n&&(t=i+n*(t-i)),i+r*(t-i)+e}function ia(t,e=0,r=1,i,n){t.min=io(t.min,e,r,i,n),t.max=io(t.max,e,r,i,n)}function il(t,{x:e,y:r}){ia(t.x,e.translate,e.scale,e.originPoint),ia(t.y,r.translate,r.scale,r.originPoint)}function iu(t){return Number.isInteger(t)?t:t>1.0000000000001||t<.999999999999?t:1}function ih(t,e){t.min=t.min+e,t.max=t.max+e}function ic(t,e,[r,i,n]){let s=void 0!==e[n]?e[n]:.5,o=eE(t.min,t.max,s);ia(t,e[r],e[i],o,e.scale)}let id=["x","scaleX","originX"],ip=["y","scaleY","originY"];function im(t,e){ic(t.x,e,id),ic(t.y,e,ip)}function ig(t,e){return it(function(t,e){if(!e)return t;let r=e({x:t.left,y:t.top}),i=e({x:t.right,y:t.bottom});return{top:r.y,left:r.x,bottom:i.y,right:i.x}}(t.getBoundingClientRect(),e))}let iv=({current:t})=>t?t.ownerDocument.defaultView:null,iy=new WeakMap;class ib{constructor(t){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=r8(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:r}=this.visualElement;if(r&&!1===r.isPresent)return;let{dragSnapToOrigin:i}=this.getProps();this.panSession=new rN(t,{onSessionStart:t=>{let{dragSnapToOrigin:r}=this.getProps();r?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(tL(t,"page").point)},onStart:(t,e)=>{let{drag:r,dragPropagation:i,onDragStart:n}=this.getProps();if(r&&!i&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=t$(r),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),r7(t=>{let e=this.getAxisMotionValue(t).get()||0;if(J.test(e)){let{projection:r}=this.visualElement;if(r&&r.layout){let i=r.layout.layoutBox[t];if(i){let t=rX(i);e=parseFloat(e)/100*t}}}this.originPoint[t]=e}),n&&tM.Wi.update(()=>n(t,e),!1,!0);let{animationState:s}=this.visualElement;s&&s.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:r,dragDirectionLock:i,onDirectionLock:n,onDrag:s}=this.getProps();if(!r&&!this.openGlobalLock)return;let{offset:o}=e;if(i&&null===this.currentDirection){this.currentDirection=function(t,e=10){let r=null;return Math.abs(t.y)>e?r="y":Math.abs(t.x)>e&&(r="x"),r}(o),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),s&&s(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>r7(t=>{var e;return"paused"===this.getAnimationState(t)&&(null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:i,contextWindow:iv(this.visualElement)})}stop(t,e){let r=this.isDragging;if(this.cancel(),!r)return;let{velocity:i}=e;this.startAnimation(i);let{onDragEnd:n}=this.getProps();n&&tM.Wi.update(()=>n(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:r}=this.getProps();!r&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,r){let{drag:i}=this.getProps();if(!r||!ix(t,i,this.currentDirection))return;let n=this.getAxisMotionValue(t),s=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(s=function(t,{min:e,max:r},i){return void 0!==e&&t<e?t=i?eE(e,t,i.min):Math.max(t,e):void 0!==r&&t>r&&(t=i?eE(r,t,i.max):Math.min(t,r)),t}(s,this.constraints[t],this.elastic[t])),n.set(s)}resolveConstraints(){var t;let{dragConstraints:e,dragElastic:r}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(t=this.visualElement.projection)||void 0===t?void 0:t.layout,n=this.constraints;e&&m(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&i?this.constraints=function(t,{top:e,left:r,bottom:i,right:n}){return{x:r1(t.x,r,n),y:r1(t.y,e,i)}}(i.layoutBox,e):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:r5(t,"left","right"),y:r5(t,"top","bottom")}}(r),n!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&r7(t=>{this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let r={};return void 0!==e.min&&(r.min=e.min-t.min),void 0!==e.max&&(r.max=e.max-t.min),r}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:r}=this.getProps();if(!e||!m(e))return!1;let i=e.current;(0,t4.k)(null!==i,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let s=function(t,e,r){let i=ig(t,r),{scroll:n}=e;return n&&(ih(i.x,n.offset.x),ih(i.y,n.offset.y)),i}(i,n.root,this.visualElement.getTransformPagePoint()),o={x:r2((t=n.layout.layoutBox).x,s.x),y:r2(t.y,s.y)};if(r){let t=r(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=it(t))}return o}startAnimation(t){let{drag:e,dragMomentum:r,dragElastic:i,dragTransition:n,dragSnapToOrigin:s,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(r7(o=>{if(!ix(o,e,this.currentDirection))return;let l=a&&a[o]||{};s&&(l={min:0,max:0});let u={type:"inertia",velocity:r?t[o]:0,bounceStiffness:i?200:1e6,bounceDamping:i?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let r=this.getAxisMotionValue(t);return r.start(ry(t,r,0,e))}stopAnimation(){r7(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){r7(t=>{var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.pause()})}getAnimationState(t){var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.state}getAxisMotionValue(t){let e="_drag"+t.toUpperCase(),r=this.visualElement.getProps();return r[e]||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){r7(e=>{let{drag:r}=this.getProps();if(!ix(e,r,this.currentDirection))return;let{projection:i}=this.visualElement,n=this.getAxisMotionValue(e);if(i&&i.layout){let{min:r,max:s}=i.layout.layoutBox[e];n.set(t[e]-eE(r,s,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:r}=this.visualElement;if(!m(e)||!r||!this.constraints)return;this.stopAnimation();let i={x:0,y:0};r7(t=>{let e=this.getAxisMotionValue(t);if(e){let r=e.get();i[t]=function(t,e){let r=.5,i=rX(t),n=rX(e);return n>i?r=eX(e.min,e.max-i,t.min):i>n&&(r=eX(t.min,t.max-n,e.min)),U(0,1,r)}({min:r,max:r},this.constraints[t])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),r7(e=>{if(!ix(e,t,null))return;let r=this.getAxisMotionValue(e),{min:n,max:s}=this.constraints[e];r.set(eE(n,s,i[e]))})}addListeners(){if(!this.visualElement.current)return;iy.set(this.visualElement,this);let t=tB(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:r=!0}=this.getProps();e&&r&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();m(t)&&(this.constraints=this.resolveRefConstraints())},{projection:r}=this.visualElement,i=r.addEventListener("measure",e);r&&!r.layout&&(r.root&&r.root.updateScroll(),r.updateLayout()),e();let n=tR(window,"resize",()=>this.scalePositionWithinConstraints()),s=r.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(r7(e=>{let r=this.getAxisMotionValue(e);r&&(this.originPoint[e]+=t[e].translate,r.set(r.get()+t[e].translate))}),this.visualElement.render())});return()=>{n(),t(),i(),s&&s()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:r=!1,dragPropagation:i=!1,dragConstraints:n=!1,dragElastic:s=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:r,dragPropagation:i,dragConstraints:n,dragElastic:s,dragMomentum:o}}}function ix(t,e,r){return(!0===e||e===t)&&(null===r||r===t)}class iw extends tZ{constructor(t){super(t),this.removeGroupControls=tq.Z,this.removeListeners=tq.Z,this.controls=new ib(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||tq.Z}unmount(){this.removeGroupControls(),this.removeListeners()}}let ik=t=>(e,r)=>{t&&tM.Wi.update(()=>t(e,r))};class iP extends tZ{constructor(){super(...arguments),this.removePointerDownListener=tq.Z}onPointerDown(t){this.session=new rN(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:iv(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:r,onPanEnd:i}=this.node.getProps();return{onSessionStart:ik(t),onStart:ik(e),onMove:r,onEnd:(t,e)=>{delete this.session,i&&tM.Wi.update(()=>i(t,e))}}}mount(){this.removePointerDownListener=tB(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}let iA={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function iT(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let iS={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!Q.test(t))return t;t=parseFloat(t)}let r=iT(t,e.target.x),i=iT(t,e.target.y);return`${r}% ${i}%`}};class iE extends o.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:r,layoutId:i}=this.props,{projection:n}=t;Object.assign(C,iM),n&&(e.group&&e.group.add(n),r&&r.register&&i&&r.register(n),n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),iA.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:r,drag:i,isPresent:n}=this.props,s=r.projection;return s&&(s.isPresent=n,i||t.layoutDependency!==e||void 0===e?s.willUpdate():this.safeToRemove(),t.isPresent===n||(n?s.promote():s.relegate()||tM.Wi.postRender(()=>{let t=s.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),queueMicrotask(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:r}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(i),r&&r.deregister&&r.deregister(i))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function iV(t){let[e,r]=function(){let t=(0,o.useContext)(u.O);if(null===t)return[!0,null];let{isPresent:e,onExitComplete:r,register:i}=t,n=(0,o.useId)();return(0,o.useEffect)(()=>i(n),[]),!e&&r?[!1,()=>r&&r(n)]:[!0]}(),i=(0,o.useContext)(T.p);return o.createElement(iE,{...t,layoutGroup:i,switchLayoutGroup:(0,o.useContext)(S),isPresent:e,safeToRemove:r})}let iM={borderRadius:{...iS,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:iS,borderTopRightRadius:iS,borderBottomLeftRadius:iS,borderBottomRightRadius:iS,boxShadow:{correct:(t,{treeScale:e,projectionDelta:r})=>{let i=e$.parse(t);if(i.length>5)return t;let n=e$.createTransformer(t),s="number"!=typeof i[0]?1:0,o=r.x.scale*e.x,a=r.y.scale*e.y;i[0+s]/=o,i[1+s]/=a;let l=eE(o,a,.5);return"number"==typeof i[2+s]&&(i[2+s]/=l),"number"==typeof i[3+s]&&(i[3+s]/=l),n(i)}}},iC=["TopLeft","TopRight","BottomLeft","BottomRight"],iD=iC.length,iR=t=>"string"==typeof t?parseFloat(t):t,ij=t=>"number"==typeof t||Q.test(t);function iL(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let iF=iz(0,.5,ed),iB=iz(.5,.95,tq.Z);function iz(t,e,r){return i=>i<t?0:i>e?1:r(eX(t,e,i))}function iO(t,e){t.min=e.min,t.max=e.max}function iI(t,e){iO(t.x,e.x),iO(t.y,e.y)}function iW(t,e,r,i,n){return t-=e,t=i+1/r*(t-i),void 0!==n&&(t=i+1/n*(t-i)),t}function iU(t,e,[r,i,n],s,o){!function(t,e=0,r=1,i=.5,n,s=t,o=t){if(J.test(e)&&(e=parseFloat(e),e=eE(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=eE(s.min,s.max,i);t===s&&(a-=e),t.min=iW(t.min,e,r,a,n),t.max=iW(t.max,e,r,a,n)}(t,e[r],e[i],e[n],e.scale,s,o)}let i$=["x","scaleX","originX"],iN=["y","scaleY","originY"];function iZ(t,e,r,i){iU(t.x,e,i$,r?r.x:void 0,i?i.x:void 0),iU(t.y,e,iN,r?r.y:void 0,i?i.y:void 0)}function iH(t){return 0===t.translate&&1===t.scale}function iG(t){return iH(t.x)&&iH(t.y)}function iY(t,e){return Math.round(t.x.min)===Math.round(e.x.min)&&Math.round(t.x.max)===Math.round(e.x.max)&&Math.round(t.y.min)===Math.round(e.y.min)&&Math.round(t.y.max)===Math.round(e.y.max)}function iX(t){return rX(t.x)/rX(t.y)}class iq{constructor(){this.members=[]}add(t){rw(this.members,t),t.scheduleRender()}remove(t){if(rk(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e;let r=this.members.findIndex(e=>t===e);if(0===r)return!1;for(let t=r;t>=0;t--){let r=this.members[t];if(!1!==r.isPresent){e=r;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,e&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:i}=t.options;!1===i&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:r}=t;e.onExitComplete&&e.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function iK(t,e,r){let i="",n=t.x.translate/e.x,s=t.y.translate/e.y;if((n||s)&&(i=`translate3d(${n}px, ${s}px, 0) `),(1!==e.x||1!==e.y)&&(i+=`scale(${1/e.x}, ${1/e.y}) `),r){let{rotate:t,rotateX:e,rotateY:n}=r;t&&(i+=`rotate(${t}deg) `),e&&(i+=`rotateX(${e}deg) `),n&&(i+=`rotateY(${n}deg) `)}let o=t.x.scale*e.x,a=t.y.scale*e.y;return(1!==o||1!==a)&&(i+=`scale(${o}, ${a})`),i||"none"}let i_=(t,e)=>t.depth-e.depth;class iJ{constructor(){this.children=[],this.isDirty=!1}add(t){rw(this.children,t),this.isDirty=!0}remove(t){rk(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(i_),this.isDirty=!1,this.children.forEach(t)}}let iQ=["","X","Y","Z"],i0={visibility:"hidden"},i1=0,i2={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function i5({attachResizeListener:t,defaultParent:e,measureScroll:r,checkIsScrollRoot:i,resetTransform:n}){return class{constructor(t={},r=null==e?void 0:e()){this.id=i1++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,i2.totalNodes=i2.resolvedTargetDeltas=i2.recalculatedProjection=0,this.nodes.forEach(i4),this.nodes.forEach(ni),this.nodes.forEach(nn),this.nodes.forEach(i9),window.MotionDebug&&window.MotionDebug.record(i2)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=r?r.root||r:this,this.path=r?[...r.path,r]:[],this.parent=r,this.depth=r?r.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new iJ)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new rP),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let r=this.eventHandlers.get(t);r&&r.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e,r=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=e instanceof SVGElement&&"svg"!==e.tagName,this.instance=e;let{layoutId:i,layout:n,visualElement:s}=this.options;if(s&&!s.current&&s.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),r&&(n||i)&&(this.isLayoutDirty=!0),t){let r;let i=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,r&&r(),r=function(t,e){let r=performance.now(),i=({timestamp:n})=>{let s=n-r;s>=e&&((0,tM.Pn)(i),t(s-e))};return tM.Wi.read(i,!0),()=>(0,tM.Pn)(i)}(i,250),iA.hasAnimatedSinceResize&&(iA.hasAnimatedSinceResize=!1,this.nodes.forEach(nr))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&s&&(i||n)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeTargetChanged:r,layout:i})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let n=this.options.transition||s.getDefaultTransition()||nh,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=s.getProps(),l=!this.targetLayout||!iY(this.targetLayout,i)||r,u=!e&&r;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,u);let e={...rg(n,"layout"),onPlay:o,onComplete:a};(s.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||nr(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=i})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,(0,tM.Pn)(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(ns),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:r}=this.options;if(void 0===e&&!r)return;let i=this.getTransformTemplate();this.prevTransformTemplateValue=i?i(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(i7);return}this.isUpdating||this.nodes.forEach(nt),this.isUpdating=!1,this.nodes.forEach(ne),this.nodes.forEach(i3),this.nodes.forEach(i6),this.clearAllSnapshots();let t=performance.now();tM.frameData.delta=U(0,1e3/60,t-tM.frameData.timestamp),tM.frameData.timestamp=t,tM.frameData.isProcessing=!0,tM.S6.update.process(tM.frameData),tM.S6.preRender.process(tM.frameData),tM.S6.render.process(tM.frameData),tM.frameData.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(i8),this.sharedNodes.forEach(no)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,tM.Wi.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){tM.Wi.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=r8(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&(this.scroll={animationId:this.root.animationId,phase:t,isRoot:i(this.instance),offset:r(this.instance)})}resetTransform(){if(!n)return;let t=this.isLayoutDirty||this.shouldResetTransform,e=this.projectionDelta&&!iG(this.projectionDelta),r=this.getTransformTemplate(),i=r?r(this.latestValues,""):void 0,s=i!==this.prevTransformTemplateValue;t&&(e||ii(this.latestValues)||s)&&(n(this.instance,i),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let r=this.measurePageBox(),i=this.removeElementScroll(r);return t&&(i=this.removeTransform(i)),np((e=i).x),np(e.y),{animationId:this.root.animationId,measuredBox:r,layoutBox:i,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return r8();let e=t.measureViewportBox(),{scroll:r}=this.root;return r&&(ih(e.x,r.offset.x),ih(e.y,r.offset.y)),e}removeElementScroll(t){let e=r8();iI(e,t);for(let r=0;r<this.path.length;r++){let i=this.path[r],{scroll:n,options:s}=i;if(i!==this.root&&n&&s.layoutScroll){if(n.isRoot){iI(e,t);let{scroll:r}=this.root;r&&(ih(e.x,-r.offset.x),ih(e.y,-r.offset.y))}ih(e.x,n.offset.x),ih(e.y,n.offset.y)}}return e}applyTransform(t,e=!1){let r=r8();iI(r,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];!e&&i.options.layoutScroll&&i.scroll&&i!==i.root&&im(r,{x:-i.scroll.offset.x,y:-i.scroll.offset.y}),ii(i.latestValues)&&im(r,i.latestValues)}return ii(this.latestValues)&&im(r,this.latestValues),r}removeTransform(t){let e=r8();iI(e,t);for(let t=0;t<this.path.length;t++){let r=this.path[t];if(!r.instance||!ii(r.latestValues))continue;ir(r.latestValues)&&r.updateSnapshot();let i=r8();iI(i,r.measurePageBox()),iZ(e,r.latestValues,r.snapshot?r.snapshot.layoutBox:void 0,i)}return ii(this.latestValues)&&iZ(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==tM.frameData.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){var e,r,i,n;let s=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=s.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=s.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=s.isSharedProjectionDirty);let o=!!this.resumingFrom||this!==s;if(!(t||o&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty)||this.attemptToResolveRelativeTarget))return;let{layout:a,layoutId:l}=this.options;if(this.layout&&(a||l)){if(this.resolvedRelativeTargetAt=tM.frameData.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=r8(),this.relativeTargetOrigin=r8(),r0(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),iI(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=r8(),this.targetWithTransforms=r8()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),r=this.target,i=this.relativeTarget,n=this.relativeParent.target,rJ(r.x,i.x,n.x),rJ(r.y,i.y,n.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):iI(this.target,this.layout.layoutBox),il(this.target,this.targetDelta)):iI(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=r8(),this.relativeTargetOrigin=r8(),r0(this.relativeTargetOrigin,this.target,t.target),iI(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}i2.resolvedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||ir(this.parent.latestValues)||is(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var t;let e=this.getLead(),r=!!this.resumingFrom||this!==e,i=!0;if((this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty))&&(i=!1),r&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===tM.frameData.timestamp&&(i=!1),i)return;let{layout:n,layoutId:s}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(n||s))return;iI(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,a=this.treeScale.y;!function(t,e,r,i=!1){let n,s;let o=r.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){s=(n=r[a]).projectionDelta;let o=n.instance;(!o||!o.style||"contents"!==o.style.display)&&(i&&n.options.layoutScroll&&n.scroll&&n!==n.root&&im(t,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),s&&(e.x*=s.x.scale,e.y*=s.y.scale,il(t,s)),i&&ii(n.latestValues)&&im(t,n.latestValues))}e.x=iu(e.x),e.y=iu(e.y)}}(this.layoutCorrected,this.treeScale,this.path,r),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox);let{target:l}=e;if(!l){this.projectionTransform&&(this.projectionDelta=r4(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=r4(),this.projectionDeltaWithTransform=r4());let u=this.projectionTransform;r_(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.projectionTransform=iK(this.projectionDelta,this.treeScale),(this.projectionTransform!==u||this.treeScale.x!==o||this.treeScale.y!==a)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),i2.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(t,e=!1){let r;let i=this.snapshot,n=i?i.latestValues:{},s={...this.latestValues},o=r4();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=r8(),l=(i?i.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,c=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(nu));this.animationProgress=0,this.mixTargetDelta=e=>{let i=e/1e3;if(na(o.x,t.x,i),na(o.y,t.y,i),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,d,p,m;r0(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,nl(p.x,m.x,a.x,i),nl(p.y,m.y,a.y,i),r&&(u=this.relativeTarget,d=r,u.x.min===d.x.min&&u.x.max===d.x.max&&u.y.min===d.y.min&&u.y.max===d.y.max)&&(this.isProjectionDirty=!1),r||(r=r8()),iI(r,this.relativeTarget)}l&&(this.animationValues=s,function(t,e,r,i,n,s){n?(t.opacity=eE(0,void 0!==r.opacity?r.opacity:1,iF(i)),t.opacityExit=eE(void 0!==e.opacity?e.opacity:1,0,iB(i))):s&&(t.opacity=eE(void 0!==e.opacity?e.opacity:1,void 0!==r.opacity?r.opacity:1,i));for(let n=0;n<iD;n++){let s=`border${iC[n]}Radius`,o=iL(e,s),a=iL(r,s);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||ij(o)===ij(a)?(t[s]=Math.max(eE(iR(o),iR(a),i),0),(J.test(a)||J.test(o))&&(t[s]+="%")):t[s]=a)}(e.rotate||r.rotate)&&(t.rotate=eE(e.rotate||0,r.rotate||0,i))}(s,n,this.latestValues,i,c,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=i},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&((0,tM.Pn)(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=tM.Wi.update(()=>{iA.hasAnimatedSinceResize=!0,this.currentAnimation=function(t,e,r){let i=L(t)?t:rE(t);return i.start(ry("",i,1e3,r)),i.animation}(0,0,{...t,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:r,layout:i,latestValues:n}=t;if(e&&r&&i){if(this!==t&&this.layout&&i&&nm(this.options.animationType,this.layout.layoutBox,i.layoutBox)){r=this.target||r8();let e=rX(this.layout.layoutBox.x);r.x.min=t.target.x.min,r.x.max=r.x.min+e;let i=rX(this.layout.layoutBox.y);r.y.min=t.target.y.min,r.y.max=r.y.min+i}iI(e,r),im(e,n),r_(this.projectionDeltaWithTransform,this.layoutCorrected,e,n)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new iq),this.sharedNodes.get(t).add(e);let r=e.options.initialPromotionConfig;e.promote({transition:r?r.transition:void 0,preserveFollowOpacity:r&&r.shouldPreserveFollowOpacity?r.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){var t;let{layoutId:e}=this.options;return e&&(null===(t=this.getStack())||void 0===t?void 0:t.lead)||this}getPrevLead(){var t;let{layoutId:e}=this.options;return e?null===(t=this.getStack())||void 0===t?void 0:t.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:r}={}){let i=this.getStack();i&&i.promote(this,r),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:r}=t;if((r.rotate||r.rotateX||r.rotateY||r.rotateZ)&&(e=!0),!e)return;let i={};for(let e=0;e<iQ.length;e++){let n="rotate"+iQ[e];r[n]&&(i[n]=r[n],t.setStaticValue(n,0))}for(let e in t.render(),i)t.setStaticValue(e,i[e]);t.scheduleRender()}getProjectionStyles(t){var e,r;if(!this.instance||this.isSVG)return;if(!this.isVisible)return i0;let i={visibility:""},n=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,i.opacity="",i.pointerEvents=tE(null==t?void 0:t.pointerEvents)||"",i.transform=n?n(this.latestValues,""):"none",i;let s=this.getLead();if(!this.projectionDelta||!this.layout||!s.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=tE(null==t?void 0:t.pointerEvents)||""),this.hasProjected&&!ii(this.latestValues)&&(e.transform=n?n({},""):"none",this.hasProjected=!1),e}let o=s.animationValues||s.latestValues;this.applyTransformsToTarget(),i.transform=iK(this.projectionDeltaWithTransform,this.treeScale,o),n&&(i.transform=n(o,i.transform));let{x:a,y:l}=this.projectionDelta;for(let t in i.transformOrigin=`${100*a.origin}% ${100*l.origin}% 0`,s.animationValues?i.opacity=s===this?null!==(r=null!==(e=o.opacity)&&void 0!==e?e:this.latestValues.opacity)&&void 0!==r?r:1:this.preserveOpacity?this.latestValues.opacity:o.opacityExit:i.opacity=s===this?void 0!==o.opacity?o.opacity:"":void 0!==o.opacityExit?o.opacityExit:0,C){if(void 0===o[t])continue;let{correct:e,applyTo:r}=C[t],n="none"===i.transform?o[t]:e(o[t],s);if(r){let t=r.length;for(let e=0;e<t;e++)i[r[e]]=n}else i[t]=n}return this.options.layoutId&&(i.pointerEvents=s===this?tE(null==t?void 0:t.pointerEvents)||"":"none"),i}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>{var e;return null===(e=t.currentAnimation)||void 0===e?void 0:e.stop()}),this.root.nodes.forEach(i7),this.root.sharedNodes.clear()}}}function i3(t){t.updateLayout()}function i6(t){var e;let r=(null===(e=t.resumeFrom)||void 0===e?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&r&&t.hasListeners("didUpdate")){let{layoutBox:e,measuredBox:i}=t.layout,{animationType:n}=t.options,s=r.source!==t.layout.source;"size"===n?r7(t=>{let i=s?r.measuredBox[t]:r.layoutBox[t],n=rX(i);i.min=e[t].min,i.max=i.min+n}):nm(n,r.layoutBox,e)&&r7(i=>{let n=s?r.measuredBox[i]:r.layoutBox[i],o=rX(e[i]);n.max=n.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[i].max=t.relativeTarget[i].min+o)});let o=r4();r_(o,e,r.layoutBox);let a=r4();s?r_(a,t.applyTransform(i,!0),r.measuredBox):r_(a,e,r.layoutBox);let l=!iG(o),u=!1;if(!t.resumeFrom){let i=t.getClosestProjectingParent();if(i&&!i.resumeFrom){let{snapshot:n,layout:s}=i;if(n&&s){let o=r8();r0(o,r.layoutBox,n.layoutBox);let a=r8();r0(a,e,s.layoutBox),iY(o,a)||(u=!0),i.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=i)}}}t.notifyListeners("didUpdate",{layout:e,snapshot:r,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function i4(t){i2.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function i9(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function i8(t){t.clearSnapshot()}function i7(t){t.clearMeasurements()}function nt(t){t.isLayoutDirty=!1}function ne(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function nr(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function ni(t){t.resolveTargetDelta()}function nn(t){t.calcProjection()}function ns(t){t.resetRotation()}function no(t){t.removeLeadSnapshot()}function na(t,e,r){t.translate=eE(e.translate,0,r),t.scale=eE(e.scale,1,r),t.origin=e.origin,t.originPoint=e.originPoint}function nl(t,e,r,i){t.min=eE(e.min,r.min,i),t.max=eE(e.max,r.max,i)}function nu(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let nh={duration:.45,ease:[.4,0,.1,1]},nc=t=>"undefined"!=typeof navigator&&navigator.userAgent.toLowerCase().includes(t),nd=nc("applewebkit/")&&!nc("chrome/")?Math.round:tq.Z;function np(t){t.min=nd(t.min),t.max=nd(t.max)}function nm(t,e,r){return"position"===t||"preserve-aspect"===t&&!rq(iX(e),iX(r),.2)}let nf=i5({attachResizeListener:(t,e)=>tR(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),ng={current:void 0},nv=i5({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!ng.current){let t=new nf({});t.mount(window),t.setOptions({layoutScroll:!0}),ng.current=t}return ng.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position}),ny=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function nb(t,e,r=1){(0,t4.k)(r<=4,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[i,n]=function(t){let e=ny.exec(t);if(!e)return[,];let[,r,i]=e;return[r,i]}(t);if(!i)return;let s=window.getComputedStyle(e).getPropertyValue(i);if(s){let t=s.trim();return rx(t)?parseFloat(t):t}return I(n)?nb(n,e,r+1):n}let nx=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),nw=t=>nx.has(t),nk=t=>Object.keys(t).some(nw),nP=t=>t===$||t===Q,nA=(t,e)=>parseFloat(t.split(", ")[e]),nT=(t,e)=>(r,{transform:i})=>{if("none"===i||!i)return 0;let n=i.match(/^matrix3d\((.+)\)$/);if(n)return nA(n[1],e);{let e=i.match(/^matrix\((.+)\)$/);return e?nA(e[1],t):0}},nS=new Set(["x","y","z"]),nE=D.filter(t=>!nS.has(t)),nV={width:({x:t},{paddingLeft:e="0",paddingRight:r="0"})=>t.max-t.min-parseFloat(e)-parseFloat(r),height:({y:t},{paddingTop:e="0",paddingBottom:r="0"})=>t.max-t.min-parseFloat(e)-parseFloat(r),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:nT(4,13),y:nT(5,14)};nV.translateX=nV.x,nV.translateY=nV.y;let nM=(t,e,r)=>{let i=e.measureViewportBox(),n=e.current,s=getComputedStyle(n),{display:o}=s,a={};"none"===o&&e.setStaticValue("display",t.display||"block"),r.forEach(t=>{a[t]=nV[t](i,s)}),e.render();let l=e.measureViewportBox();return r.forEach(r=>{let i=e.getValue(r);i&&i.jump(a[r]),t[r]=nV[r](l,s)}),t},nC=(t,e,r={},i={})=>{e={...e},i={...i};let n=Object.keys(e).filter(nw),s=[],o=!1,a=[];if(n.forEach(n=>{let l;let u=t.getValue(n);if(!t.hasValue(n))return;let h=r[n],c=rC(h),d=e[n];if(tA(d)){let t=d.length,e=null===d[0]?1:0;c=rC(h=d[e]);for(let r=e;r<t&&null!==d[r];r++)l?(0,t4.k)(rC(d[r])===l,"All keyframes must be of the same type"):(l=rC(d[r]),(0,t4.k)(l===c||nP(c)&&nP(l),"Keyframes must be of the same dimension as the current value"))}else l=rC(d);if(c!==l){if(nP(c)&&nP(l)){let t=u.get();"string"==typeof t&&u.set(parseFloat(t)),"string"==typeof d?e[n]=parseFloat(d):Array.isArray(d)&&l===Q&&(e[n]=d.map(parseFloat))}else(null==c?void 0:c.transform)&&(null==l?void 0:l.transform)&&(0===h||0===d)?0===h?u.set(l.transform(h)):e[n]=c.transform(d):(o||(s=function(t){let e=[];return nE.forEach(r=>{let i=t.getValue(r);void 0!==i&&(e.push([r,i.get()]),i.set(r.startsWith("scale")?1:0))}),e.length&&t.render(),e}(t),o=!0),a.push(n),i[n]=void 0!==i[n]?i[n]:e[n],u.jump(d))}}),!a.length)return{target:e,transitionEnd:i};{let r=a.indexOf("height")>=0?window.pageYOffset:null,n=nM(e,t,a);return s.length&&s.forEach(([e,r])=>{t.getValue(e).set(r)}),t.render(),A.j&&null!==r&&window.scrollTo({top:r}),{target:n,transitionEnd:i}}},nD=(t,e,r,i)=>{var n,s;let o=function(t,{...e},r){let i=t.current;if(!(i instanceof Element))return{target:e,transitionEnd:r};for(let n in r&&(r={...r}),t.values.forEach(t=>{let e=t.get();if(!I(e))return;let r=nb(e,i);r&&t.set(r)}),e){let t=e[n];if(!I(t))continue;let s=nb(t,i);s&&(e[n]=s,r||(r={}),void 0===r[n]&&(r[n]=t))}return{target:e,transitionEnd:r}}(t,e,i);return e=o.target,i=o.transitionEnd,n=e,s=i,nk(n)?nC(t,n,r,s):{target:n,transitionEnd:s}},nR={current:null},nj={current:!1},nL=new WeakMap,nF=Object.keys(P),nB=nF.length,nz=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],nO=y.length;class nI{constructor({parent:t,props:e,presenceContext:r,reducedMotionConfig:i,visualState:n},s={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>tM.Wi.render(this.render,!1,!0);let{latestValues:o,renderState:a}=n;this.latestValues=o,this.baseTarget={...o},this.initialValues=e.initial?{...o}:{},this.renderState=a,this.parent=t,this.props=e,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=s,this.isControllingVariants=b(e),this.isVariantNode=x(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:l,...u}=this.scrapeMotionValuesFromProps(e,{});for(let t in u){let e=u[t];void 0!==o[t]&&L(e)&&(e.set(o[t],!1),rb(l)&&l.add(t))}}scrapeMotionValuesFromProps(t,e){return{}}mount(t){this.current=t,nL.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),nj.current||function(){if(nj.current=!0,A.j){if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>nR.current=t.matches;t.addListener(e),e()}else nR.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||nR.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in nL.delete(this.current),this.projection&&this.projection.unmount(),(0,tM.Pn)(this.notifyUpdate),(0,tM.Pn)(this.render),this.valueSubscriptions.forEach(t=>t()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features)this.features[t].unmount();this.current=null}bindToMotionValue(t,e){let r=R.has(t),i=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&tM.Wi.update(this.notifyUpdate,!1,!0),r&&this.projection&&(this.projection.isTransformDirty=!0)}),n=e.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(t,()=>{i(),n()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}loadFeatures({children:t,...e},r,i,n){let s,o;for(let t=0;t<nB;t++){let r=nF[t],{isEnabled:i,Feature:n,ProjectionNode:a,MeasureLayout:l}=P[r];a&&(s=a),i(e)&&(!this.features[r]&&n&&(this.features[r]=new n(this)),l&&(o=l))}if(("html"===this.type||"svg"===this.type)&&!this.projection&&s){this.projection=new s(this.latestValues,this.parent&&this.parent.projection);let{layoutId:t,layout:r,drag:i,dragConstraints:o,layoutScroll:a,layoutRoot:l}=e;this.projection.setOptions({layoutId:t,layout:r,alwaysMeasureLayout:!!i||o&&m(o),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:"string"==typeof r?r:"both",initialPromotionConfig:n,layoutScroll:a,layoutRoot:l})}return o}updateFeatures(){for(let t in this.features){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):r8()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}makeTargetAnimatable(t,e=!0){return this.makeTargetAnimatableFromInstance(t,this.props,e)}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<nz.length;e++){let r=nz[e];this.propEventSubscriptions[r]&&(this.propEventSubscriptions[r](),delete this.propEventSubscriptions[r]);let i=t["on"+r];i&&(this.propEventSubscriptions[r]=this.on(r,i))}this.prevMotionValues=function(t,e,r){let{willChange:i}=e;for(let n in e){let s=e[n],o=r[n];if(L(s))t.addValue(n,s),rb(i)&&i.add(n);else if(L(o))t.addValue(n,rE(s,{owner:t})),rb(i)&&i.remove(n);else if(o!==s){if(t.hasValue(n)){let e=t.getValue(n);e.hasAnimated||e.set(s)}else{let e=t.getStaticValue(n);t.addValue(n,rE(void 0!==e?e:s,{owner:t}))}}}for(let i in r)void 0===e[i]&&t.removeValue(i);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(t=!1){if(t)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){let t=this.parent&&this.parent.getVariantContext()||{};return void 0!==this.props.initial&&(t.initial=this.props.initial),t}let e={};for(let t=0;t<nO;t++){let r=y[t],i=this.props[r];(f(i)||!1===i)&&(e[r]=i)}return e}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){e!==this.values.get(t)&&(this.removeValue(t),this.bindToMotionValue(t,e)),this.values.set(t,e),this.latestValues[t]=e.get()}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return void 0===r&&void 0!==e&&(r=rE(e,{owner:this}),this.addValue(t,r)),r}readValue(t){var e;return void 0===this.latestValues[t]&&this.current?null!==(e=this.getBaseTargetFromProps(this.props,t))&&void 0!==e?e:this.readValueFromInstance(this.current,t,this.options):this.latestValues[t]}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){var e;let{initial:r}=this.props,i="string"==typeof r||"object"==typeof r?null===(e=tk(this.props,r))||void 0===e?void 0:e[t]:void 0;if(r&&void 0!==i)return i;let n=this.getBaseTargetFromProps(this.props,t);return void 0===n||L(n)?void 0!==this.initialValues[t]&&void 0===i?void 0:this.baseTarget[t]:n}on(t,e){return this.events[t]||(this.events[t]=new rP),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class nW extends nI{sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:r}){delete e[t],delete r[t]}makeTargetAnimatableFromInstance({transition:t,transitionEnd:e,...r},{transformValues:i},n){let s=function(t,e,r){let i={};for(let n in t){let t=function(t,e){if(e)return(e[t]||e.default||e).from}(n,e);if(void 0!==t)i[n]=t;else{let t=r.getValue(n);t&&(i[n]=t.get())}}return i}(r,t||{},this);if(i&&(e&&(e=i(e)),r&&(r=i(r)),s&&(s=i(s))),n){!function(t,e,r){var i,n;let s=Object.keys(e).filter(e=>!t.hasValue(e)),o=s.length;if(o)for(let a=0;a<o;a++){let o=s[a],l=e[o],u=null;Array.isArray(l)&&(u=l[0]),null===u&&(u=null!==(n=null!==(i=r[o])&&void 0!==i?i:t.readValue(o))&&void 0!==n?n:e[o]),null!=u&&("string"==typeof u&&(rx(u)||rf(u))?u=parseFloat(u):!rR(u)&&e$.test(l)&&(u=rm(o,l)),t.addValue(o,rE(u,{owner:t})),void 0===r[o]&&(r[o]=u),null!==u&&t.setBaseTarget(o,u))}}(this,r,s);let t=nD(this,r,s,e);e=t.transitionEnd,r=t.target}return{transition:t,transitionEnd:e,...r}}}class nU extends nW{constructor(){super(...arguments),this.type="html"}readValueFromInstance(t,e){if(R.has(e)){let t=rp(e);return t&&t.default||0}{let r=window.getComputedStyle(t),i=(O(e)?r.getPropertyValue(e):r[e])||0;return"string"==typeof i?i.trim():i}}measureInstanceViewportBox(t,{transformPagePoint:e}){return ig(t,e)}build(t,e,r,i){ts(t,e,r,i.transformTemplate)}scrapeMotionValuesFromProps(t,e){return tx(t,e)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;L(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}renderInstance(t,e,r,i){tv(t,e,r,i)}}class n$ extends nW{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(R.has(e)){let t=rp(e);return t&&t.default||0}return e=ty.has(e)?e:d(e),t.getAttribute(e)}measureInstanceViewportBox(){return r8()}scrapeMotionValuesFromProps(t,e){return tw(t,e)}build(t,e,r,i){tm(t,e,r,this.isSVGTag,i.transformTemplate)}renderInstance(t,e,r,i){tb(t,e,r,i)}mount(t){this.isSVGTag=tg(t.tagName),super.mount(t)}}let nN=(t,e)=>M(t)?new n$(e,{enableHardwareAcceleration:!1}):new nU(e,{enableHardwareAcceleration:!0}),nZ={animation:{Feature:rI},exit:{Feature:rU},inView:{Feature:t5},tap:{Feature:t_},focus:{Feature:tY},hover:{Feature:tG},pan:{Feature:iP},drag:{Feature:iw,ProjectionNode:nv,MeasureLayout:iV},layout:{ProjectionNode:nv,MeasureLayout:iV}},nH=function(t){function e(e,r={}){return function({preloadedFeatures:t,createVisualElement:e,useRender:r,useVisualState:i,Component:n}){t&&function(t){for(let e in t)P[e]={...P[e],...t[e]}}(t);let s=(0,o.forwardRef)(function(s,d){var g;let v;let y={...(0,o.useContext)(a),...s,layoutId:function({layoutId:t}){let e=(0,o.useContext)(T.p).id;return e&&void 0!==t?e+"-"+t:t}(s)},{isStatic:x}=y,k=function(t){let{initial:e,animate:r}=function(t,e){if(b(t)){let{initial:e,animate:r}=t;return{initial:!1===e||f(e)?e:void 0,animate:f(r)?r:void 0}}return!1!==t.inherit?e:{}}(t,(0,o.useContext)(l));return(0,o.useMemo)(()=>({initial:e,animate:r}),[w(e),w(r)])}(s),P=i(s,x);if(!x&&A.j){k.visualElement=function(t,e,r,i){let{visualElement:n}=(0,o.useContext)(l),s=(0,o.useContext)(c),d=(0,o.useContext)(u.O),m=(0,o.useContext)(a).reducedMotion,f=(0,o.useRef)();i=i||s.renderer,!f.current&&i&&(f.current=i(t,{visualState:e,parent:n,props:r,presenceContext:d,blockInitialAnimation:!!d&&!1===d.initial,reducedMotionConfig:m}));let g=f.current;(0,o.useInsertionEffect)(()=>{g&&g.update(r,d)});let v=(0,o.useRef)(!!(r[p]&&!window.HandoffComplete));return(0,h.L)(()=>{g&&(g.render(),v.current&&g.animationState&&g.animationState.animateChanges())}),(0,o.useEffect)(()=>{g&&(g.updateFeatures(),!v.current&&g.animationState&&g.animationState.animateChanges(),v.current&&(v.current=!1,window.HandoffComplete=!0))}),g}(n,P,y,e);let r=(0,o.useContext)(S),i=(0,o.useContext)(c).strict;k.visualElement&&(v=k.visualElement.loadFeatures(y,i,t,r))}return o.createElement(l.Provider,{value:k},v&&k.visualElement?o.createElement(v,{visualElement:k.visualElement,...y}):null,r(n,s,(g=k.visualElement,(0,o.useCallback)(t=>{t&&P.mount&&P.mount(t),g&&(t?g.mount(t):g.unmount()),d&&("function"==typeof d?d(t):m(d)&&(d.current=t))},[g])),P,x,k.visualElement))});return s[E]=n,s}(t(e,r))}if("undefined"==typeof Proxy)return e;let r=new Map;return new Proxy(e,{get:(t,i)=>(r.has(i)||r.set(i,e(i)),r.get(i))})}((t,e)=>(function(t,{forwardMotionProps:e=!1},r,i){return{...M(t)?tC:tD,preloadedFeatures:r,useRender:function(t=!1){return(e,r,i,{latestValues:n},s)=>{let a=(M(e)?function(t,e,r,i){let n=(0,o.useMemo)(()=>{let r=tf();return tm(r,e,{enableHardwareAcceleration:!1},tg(i),t.transformTemplate),{...r.attrs,style:{...r.style}}},[e]);if(t.style){let e={};ta(e,t.style,t),n.style={...e,...n.style}}return n}:function(t,e,r){let i={},n=function(t,e,r){let i=t.style||{},n={};return ta(n,i,t),Object.assign(n,function({transformTemplate:t},e,r){return(0,o.useMemo)(()=>{let i=to();return ts(i,e,{enableHardwareAcceleration:!r},t),Object.assign({},i.vars,i.style)},[e])}(t,e,r)),t.transformValues?t.transformValues(n):n}(t,e,r);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=n,i})(r,n,s,e),l={...function(t,e,r){let i={};for(let n in t)("values"!==n||"object"!=typeof t.values)&&(th(n)||!0===r&&tu(n)||!e&&!tu(n)||t.draggable&&n.startsWith("onDrag"))&&(i[n]=t[n]);return i}(r,"string"==typeof e,t),...a,ref:i},{children:u}=r,h=(0,o.useMemo)(()=>L(u)?u.get():u,[u]);return(0,o.createElement)(e,{...l,children:h})}}(e),createVisualElement:i,Component:t}})(t,e,nZ,nN))},6567:function(t,e,r){"use strict";r.d(e,{K:function(){return n},k:function(){return s}});var i=r(6977);let n=i.Z,s=i.Z},6613:function(t,e,r){"use strict";r.d(e,{j:function(){return i}});let i="undefined"!=typeof document},6977:function(t,e,r){"use strict";r.d(e,{Z:function(){return i}});let i=t=>t},961:function(t,e,r){"use strict";r.d(e,{h:function(){return n}});var i=r(2265);function n(t){let e=(0,i.useRef)(null);return null===e.current&&(e.current=t()),e.current}},538:function(t,e,r){"use strict";r.d(e,{L:function(){return n}});var i=r(2265);let n=r(6613).j?i.useLayoutEffect:i.useEffect},4769:function(t,e,r){"use strict";r.d(e,{m6:function(){return tu}});let i=t=>{let e=a(t),{conflictingClassGroups:r,conflictingClassGroupModifiers:i}=t;return{getClassGroupId:t=>{let r=t.split("-");return""===r[0]&&1!==r.length&&r.shift(),n(r,e)||o(t)},getConflictingClassGroupIds:(t,e)=>{let n=r[t]||[];return e&&i[t]?[...n,...i[t]]:n}}},n=(t,e)=>{if(0===t.length)return e.classGroupId;let r=t[0],i=e.nextPart.get(r),s=i?n(t.slice(1),i):void 0;if(s)return s;if(0===e.validators.length)return;let o=t.join("-");return e.validators.find(({validator:t})=>t(o))?.classGroupId},s=/^\[(.+)\]$/,o=t=>{if(s.test(t)){let e=s.exec(t)[1],r=e?.substring(0,e.indexOf(":"));if(r)return"arbitrary.."+r}},a=t=>{let{theme:e,classGroups:r}=t,i={nextPart:new Map,validators:[]};for(let t in r)l(r[t],i,t,e);return i},l=(t,e,r,i)=>{t.forEach(t=>{if("string"==typeof t){(""===t?e:u(e,t)).classGroupId=r;return}if("function"==typeof t){if(h(t)){l(t(i),e,r,i);return}e.validators.push({validator:t,classGroupId:r});return}Object.entries(t).forEach(([t,n])=>{l(n,u(e,t),r,i)})})},u=(t,e)=>{let r=t;return e.split("-").forEach(t=>{r.nextPart.has(t)||r.nextPart.set(t,{nextPart:new Map,validators:[]}),r=r.nextPart.get(t)}),r},h=t=>t.isThemeGetter,c=t=>{if(t<1)return{get:()=>void 0,set:()=>{}};let e=0,r=new Map,i=new Map,n=(n,s)=>{r.set(n,s),++e>t&&(e=0,i=r,r=new Map)};return{get(t){let e=r.get(t);return void 0!==e?e:void 0!==(e=i.get(t))?(n(t,e),e):void 0},set(t,e){r.has(t)?r.set(t,e):n(t,e)}}},d=t=>{let{prefix:e,experimentalParseClassName:r}=t,i=t=>{let e;let r=[],i=0,n=0,s=0;for(let o=0;o<t.length;o++){let a=t[o];if(0===i&&0===n){if(":"===a){r.push(t.slice(s,o)),s=o+1;continue}if("/"===a){e=o;continue}}"["===a?i++:"]"===a?i--:"("===a?n++:")"===a&&n--}let o=0===r.length?t:t.substring(s),a=p(o);return{modifiers:r,hasImportantModifier:a!==o,baseClassName:a,maybePostfixModifierPosition:e&&e>s?e-s:void 0}};if(e){let t=e+":",r=i;i=e=>e.startsWith(t)?r(e.substring(t.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:e,maybePostfixModifierPosition:void 0}}if(r){let t=i;i=e=>r({className:e,parseClassName:t})}return i},p=t=>t.endsWith("!")?t.substring(0,t.length-1):t.startsWith("!")?t.substring(1):t,m=t=>{let e=Object.fromEntries(t.orderSensitiveModifiers.map(t=>[t,!0]));return t=>{if(t.length<=1)return t;let r=[],i=[];return t.forEach(t=>{"["===t[0]||e[t]?(r.push(...i.sort(),t),i=[]):i.push(t)}),r.push(...i.sort()),r}},f=t=>({cache:c(t.cacheSize),parseClassName:d(t),sortModifiers:m(t),...i(t)}),g=/\s+/,v=(t,e)=>{let{parseClassName:r,getClassGroupId:i,getConflictingClassGroupIds:n,sortModifiers:s}=e,o=[],a=t.trim().split(g),l="";for(let t=a.length-1;t>=0;t-=1){let e=a[t],{isExternal:u,modifiers:h,hasImportantModifier:c,baseClassName:d,maybePostfixModifierPosition:p}=r(e);if(u){l=e+(l.length>0?" "+l:l);continue}let m=!!p,f=i(m?d.substring(0,p):d);if(!f){if(!m||!(f=i(d))){l=e+(l.length>0?" "+l:l);continue}m=!1}let g=s(h).join(":"),v=c?g+"!":g,y=v+f;if(o.includes(y))continue;o.push(y);let b=n(f,m);for(let t=0;t<b.length;++t){let e=b[t];o.push(v+e)}l=e+(l.length>0?" "+l:l)}return l};function y(){let t,e,r=0,i="";for(;r<arguments.length;)(t=arguments[r++])&&(e=b(t))&&(i&&(i+=" "),i+=e);return i}let b=t=>{let e;if("string"==typeof t)return t;let r="";for(let i=0;i<t.length;i++)t[i]&&(e=b(t[i]))&&(r&&(r+=" "),r+=e);return r},x=t=>{let e=e=>e[t]||[];return e.isThemeGetter=!0,e},w=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,k=/^\((?:(\w[\w-]*):)?(.+)\)$/i,P=/^\d+\/\d+$/,A=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,T=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,S=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,E=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,V=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,M=t=>P.test(t),C=t=>!!t&&!Number.isNaN(Number(t)),D=t=>!!t&&Number.isInteger(Number(t)),R=t=>t.endsWith("%")&&C(t.slice(0,-1)),j=t=>A.test(t),L=()=>!0,F=t=>T.test(t)&&!S.test(t),B=()=>!1,z=t=>E.test(t),O=t=>V.test(t),I=t=>!U(t)&&!Y(t),W=t=>tt(t,tn,B),U=t=>w.test(t),$=t=>tt(t,ts,F),N=t=>tt(t,to,C),Z=t=>tt(t,tr,B),H=t=>tt(t,ti,O),G=t=>tt(t,tl,z),Y=t=>k.test(t),X=t=>te(t,ts),q=t=>te(t,ta),K=t=>te(t,tr),_=t=>te(t,tn),J=t=>te(t,ti),Q=t=>te(t,tl,!0),tt=(t,e,r)=>{let i=w.exec(t);return!!i&&(i[1]?e(i[1]):r(i[2]))},te=(t,e,r=!1)=>{let i=k.exec(t);return!!i&&(i[1]?e(i[1]):r)},tr=t=>"position"===t||"percentage"===t,ti=t=>"image"===t||"url"===t,tn=t=>"length"===t||"size"===t||"bg-size"===t,ts=t=>"length"===t,to=t=>"number"===t,ta=t=>"family-name"===t,tl=t=>"shadow"===t,tu=function(t){let e,r,i;let n=function(o){return r=(e=f([].reduce((t,e)=>e(t),t()))).cache.get,i=e.cache.set,n=s,s(o)};function s(t){let n=r(t);if(n)return n;let s=v(t,e);return i(t,s),s}return function(){return n(y.apply(null,arguments))}}(()=>{let t=x("color"),e=x("font"),r=x("text"),i=x("font-weight"),n=x("tracking"),s=x("leading"),o=x("breakpoint"),a=x("container"),l=x("spacing"),u=x("radius"),h=x("shadow"),c=x("inset-shadow"),d=x("text-shadow"),p=x("drop-shadow"),m=x("blur"),f=x("perspective"),g=x("aspect"),v=x("ease"),y=x("animate"),b=()=>["auto","avoid","all","avoid-page","page","left","right","column"],w=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],k=()=>[...w(),Y,U],P=()=>["auto","hidden","clip","visible","scroll"],A=()=>["auto","contain","none"],T=()=>[Y,U,l],S=()=>[M,"full","auto",...T()],E=()=>[D,"none","subgrid",Y,U],V=()=>["auto",{span:["full",D,Y,U]},D,Y,U],F=()=>[D,"auto",Y,U],B=()=>["auto","min","max","fr",Y,U],z=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],O=()=>["start","end","center","stretch","center-safe","end-safe"],tt=()=>["auto",...T()],te=()=>[M,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...T()],tr=()=>[t,Y,U],ti=()=>[...w(),K,Z,{position:[Y,U]}],tn=()=>["no-repeat",{repeat:["","x","y","space","round"]}],ts=()=>["auto","cover","contain",_,W,{size:[Y,U]}],to=()=>[R,X,$],ta=()=>["","none","full",u,Y,U],tl=()=>["",C,X,$],tu=()=>["solid","dashed","dotted","double"],th=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],tc=()=>[C,R,K,Z],td=()=>["","none",m,Y,U],tp=()=>["none",C,Y,U],tm=()=>["none",C,Y,U],tf=()=>[C,Y,U],tg=()=>[M,"full",...T()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[j],breakpoint:[j],color:[L],container:[j],"drop-shadow":[j],ease:["in","out","in-out"],font:[I],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[j],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[j],shadow:[j],spacing:["px",C],text:[j],"text-shadow":[j],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",M,U,Y,g]}],container:["container"],columns:[{columns:[C,U,Y,a]}],"break-after":[{"break-after":b()}],"break-before":[{"break-before":b()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:k()}],overflow:[{overflow:P()}],"overflow-x":[{"overflow-x":P()}],"overflow-y":[{"overflow-y":P()}],overscroll:[{overscroll:A()}],"overscroll-x":[{"overscroll-x":A()}],"overscroll-y":[{"overscroll-y":A()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:S()}],"inset-x":[{"inset-x":S()}],"inset-y":[{"inset-y":S()}],start:[{start:S()}],end:[{end:S()}],top:[{top:S()}],right:[{right:S()}],bottom:[{bottom:S()}],left:[{left:S()}],visibility:["visible","invisible","collapse"],z:[{z:[D,"auto",Y,U]}],basis:[{basis:[M,"full","auto",a,...T()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[C,M,"auto","initial","none",U]}],grow:[{grow:["",C,Y,U]}],shrink:[{shrink:["",C,Y,U]}],order:[{order:[D,"first","last","none",Y,U]}],"grid-cols":[{"grid-cols":E()}],"col-start-end":[{col:V()}],"col-start":[{"col-start":F()}],"col-end":[{"col-end":F()}],"grid-rows":[{"grid-rows":E()}],"row-start-end":[{row:V()}],"row-start":[{"row-start":F()}],"row-end":[{"row-end":F()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":B()}],"auto-rows":[{"auto-rows":B()}],gap:[{gap:T()}],"gap-x":[{"gap-x":T()}],"gap-y":[{"gap-y":T()}],"justify-content":[{justify:[...z(),"normal"]}],"justify-items":[{"justify-items":[...O(),"normal"]}],"justify-self":[{"justify-self":["auto",...O()]}],"align-content":[{content:["normal",...z()]}],"align-items":[{items:[...O(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...O(),{baseline:["","last"]}]}],"place-content":[{"place-content":z()}],"place-items":[{"place-items":[...O(),"baseline"]}],"place-self":[{"place-self":["auto",...O()]}],p:[{p:T()}],px:[{px:T()}],py:[{py:T()}],ps:[{ps:T()}],pe:[{pe:T()}],pt:[{pt:T()}],pr:[{pr:T()}],pb:[{pb:T()}],pl:[{pl:T()}],m:[{m:tt()}],mx:[{mx:tt()}],my:[{my:tt()}],ms:[{ms:tt()}],me:[{me:tt()}],mt:[{mt:tt()}],mr:[{mr:tt()}],mb:[{mb:tt()}],ml:[{ml:tt()}],"space-x":[{"space-x":T()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":T()}],"space-y-reverse":["space-y-reverse"],size:[{size:te()}],w:[{w:[a,"screen",...te()]}],"min-w":[{"min-w":[a,"screen","none",...te()]}],"max-w":[{"max-w":[a,"screen","none","prose",{screen:[o]},...te()]}],h:[{h:["screen","lh",...te()]}],"min-h":[{"min-h":["screen","lh","none",...te()]}],"max-h":[{"max-h":["screen","lh",...te()]}],"font-size":[{text:["base",r,X,$]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[i,Y,N]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",R,U]}],"font-family":[{font:[q,U,e]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[n,Y,U]}],"line-clamp":[{"line-clamp":[C,"none",Y,N]}],leading:[{leading:[s,...T()]}],"list-image":[{"list-image":["none",Y,U]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",Y,U]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:tr()}],"text-color":[{text:tr()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...tu(),"wavy"]}],"text-decoration-thickness":[{decoration:[C,"from-font","auto",Y,$]}],"text-decoration-color":[{decoration:tr()}],"underline-offset":[{"underline-offset":[C,"auto",Y,U]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:T()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",Y,U]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",Y,U]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:ti()}],"bg-repeat":[{bg:tn()}],"bg-size":[{bg:ts()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},D,Y,U],radial:["",Y,U],conic:[D,Y,U]},J,H]}],"bg-color":[{bg:tr()}],"gradient-from-pos":[{from:to()}],"gradient-via-pos":[{via:to()}],"gradient-to-pos":[{to:to()}],"gradient-from":[{from:tr()}],"gradient-via":[{via:tr()}],"gradient-to":[{to:tr()}],rounded:[{rounded:ta()}],"rounded-s":[{"rounded-s":ta()}],"rounded-e":[{"rounded-e":ta()}],"rounded-t":[{"rounded-t":ta()}],"rounded-r":[{"rounded-r":ta()}],"rounded-b":[{"rounded-b":ta()}],"rounded-l":[{"rounded-l":ta()}],"rounded-ss":[{"rounded-ss":ta()}],"rounded-se":[{"rounded-se":ta()}],"rounded-ee":[{"rounded-ee":ta()}],"rounded-es":[{"rounded-es":ta()}],"rounded-tl":[{"rounded-tl":ta()}],"rounded-tr":[{"rounded-tr":ta()}],"rounded-br":[{"rounded-br":ta()}],"rounded-bl":[{"rounded-bl":ta()}],"border-w":[{border:tl()}],"border-w-x":[{"border-x":tl()}],"border-w-y":[{"border-y":tl()}],"border-w-s":[{"border-s":tl()}],"border-w-e":[{"border-e":tl()}],"border-w-t":[{"border-t":tl()}],"border-w-r":[{"border-r":tl()}],"border-w-b":[{"border-b":tl()}],"border-w-l":[{"border-l":tl()}],"divide-x":[{"divide-x":tl()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":tl()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...tu(),"hidden","none"]}],"divide-style":[{divide:[...tu(),"hidden","none"]}],"border-color":[{border:tr()}],"border-color-x":[{"border-x":tr()}],"border-color-y":[{"border-y":tr()}],"border-color-s":[{"border-s":tr()}],"border-color-e":[{"border-e":tr()}],"border-color-t":[{"border-t":tr()}],"border-color-r":[{"border-r":tr()}],"border-color-b":[{"border-b":tr()}],"border-color-l":[{"border-l":tr()}],"divide-color":[{divide:tr()}],"outline-style":[{outline:[...tu(),"none","hidden"]}],"outline-offset":[{"outline-offset":[C,Y,U]}],"outline-w":[{outline:["",C,X,$]}],"outline-color":[{outline:tr()}],shadow:[{shadow:["","none",h,Q,G]}],"shadow-color":[{shadow:tr()}],"inset-shadow":[{"inset-shadow":["none",c,Q,G]}],"inset-shadow-color":[{"inset-shadow":tr()}],"ring-w":[{ring:tl()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:tr()}],"ring-offset-w":[{"ring-offset":[C,$]}],"ring-offset-color":[{"ring-offset":tr()}],"inset-ring-w":[{"inset-ring":tl()}],"inset-ring-color":[{"inset-ring":tr()}],"text-shadow":[{"text-shadow":["none",d,Q,G]}],"text-shadow-color":[{"text-shadow":tr()}],opacity:[{opacity:[C,Y,U]}],"mix-blend":[{"mix-blend":[...th(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":th()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[C]}],"mask-image-linear-from-pos":[{"mask-linear-from":tc()}],"mask-image-linear-to-pos":[{"mask-linear-to":tc()}],"mask-image-linear-from-color":[{"mask-linear-from":tr()}],"mask-image-linear-to-color":[{"mask-linear-to":tr()}],"mask-image-t-from-pos":[{"mask-t-from":tc()}],"mask-image-t-to-pos":[{"mask-t-to":tc()}],"mask-image-t-from-color":[{"mask-t-from":tr()}],"mask-image-t-to-color":[{"mask-t-to":tr()}],"mask-image-r-from-pos":[{"mask-r-from":tc()}],"mask-image-r-to-pos":[{"mask-r-to":tc()}],"mask-image-r-from-color":[{"mask-r-from":tr()}],"mask-image-r-to-color":[{"mask-r-to":tr()}],"mask-image-b-from-pos":[{"mask-b-from":tc()}],"mask-image-b-to-pos":[{"mask-b-to":tc()}],"mask-image-b-from-color":[{"mask-b-from":tr()}],"mask-image-b-to-color":[{"mask-b-to":tr()}],"mask-image-l-from-pos":[{"mask-l-from":tc()}],"mask-image-l-to-pos":[{"mask-l-to":tc()}],"mask-image-l-from-color":[{"mask-l-from":tr()}],"mask-image-l-to-color":[{"mask-l-to":tr()}],"mask-image-x-from-pos":[{"mask-x-from":tc()}],"mask-image-x-to-pos":[{"mask-x-to":tc()}],"mask-image-x-from-color":[{"mask-x-from":tr()}],"mask-image-x-to-color":[{"mask-x-to":tr()}],"mask-image-y-from-pos":[{"mask-y-from":tc()}],"mask-image-y-to-pos":[{"mask-y-to":tc()}],"mask-image-y-from-color":[{"mask-y-from":tr()}],"mask-image-y-to-color":[{"mask-y-to":tr()}],"mask-image-radial":[{"mask-radial":[Y,U]}],"mask-image-radial-from-pos":[{"mask-radial-from":tc()}],"mask-image-radial-to-pos":[{"mask-radial-to":tc()}],"mask-image-radial-from-color":[{"mask-radial-from":tr()}],"mask-image-radial-to-color":[{"mask-radial-to":tr()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":w()}],"mask-image-conic-pos":[{"mask-conic":[C]}],"mask-image-conic-from-pos":[{"mask-conic-from":tc()}],"mask-image-conic-to-pos":[{"mask-conic-to":tc()}],"mask-image-conic-from-color":[{"mask-conic-from":tr()}],"mask-image-conic-to-color":[{"mask-conic-to":tr()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:ti()}],"mask-repeat":[{mask:tn()}],"mask-size":[{mask:ts()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",Y,U]}],filter:[{filter:["","none",Y,U]}],blur:[{blur:td()}],brightness:[{brightness:[C,Y,U]}],contrast:[{contrast:[C,Y,U]}],"drop-shadow":[{"drop-shadow":["","none",p,Q,G]}],"drop-shadow-color":[{"drop-shadow":tr()}],grayscale:[{grayscale:["",C,Y,U]}],"hue-rotate":[{"hue-rotate":[C,Y,U]}],invert:[{invert:["",C,Y,U]}],saturate:[{saturate:[C,Y,U]}],sepia:[{sepia:["",C,Y,U]}],"backdrop-filter":[{"backdrop-filter":["","none",Y,U]}],"backdrop-blur":[{"backdrop-blur":td()}],"backdrop-brightness":[{"backdrop-brightness":[C,Y,U]}],"backdrop-contrast":[{"backdrop-contrast":[C,Y,U]}],"backdrop-grayscale":[{"backdrop-grayscale":["",C,Y,U]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[C,Y,U]}],"backdrop-invert":[{"backdrop-invert":["",C,Y,U]}],"backdrop-opacity":[{"backdrop-opacity":[C,Y,U]}],"backdrop-saturate":[{"backdrop-saturate":[C,Y,U]}],"backdrop-sepia":[{"backdrop-sepia":["",C,Y,U]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":T()}],"border-spacing-x":[{"border-spacing-x":T()}],"border-spacing-y":[{"border-spacing-y":T()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",Y,U]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[C,"initial",Y,U]}],ease:[{ease:["linear","initial",v,Y,U]}],delay:[{delay:[C,Y,U]}],animate:[{animate:["none",y,Y,U]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[f,Y,U]}],"perspective-origin":[{"perspective-origin":k()}],rotate:[{rotate:tp()}],"rotate-x":[{"rotate-x":tp()}],"rotate-y":[{"rotate-y":tp()}],"rotate-z":[{"rotate-z":tp()}],scale:[{scale:tm()}],"scale-x":[{"scale-x":tm()}],"scale-y":[{"scale-y":tm()}],"scale-z":[{"scale-z":tm()}],"scale-3d":["scale-3d"],skew:[{skew:tf()}],"skew-x":[{"skew-x":tf()}],"skew-y":[{"skew-y":tf()}],transform:[{transform:[Y,U,"","none","gpu","cpu"]}],"transform-origin":[{origin:k()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:tg()}],"translate-x":[{"translate-x":tg()}],"translate-y":[{"translate-y":tg()}],"translate-z":[{"translate-z":tg()}],"translate-none":["translate-none"],accent:[{accent:tr()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:tr()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",Y,U]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":T()}],"scroll-mx":[{"scroll-mx":T()}],"scroll-my":[{"scroll-my":T()}],"scroll-ms":[{"scroll-ms":T()}],"scroll-me":[{"scroll-me":T()}],"scroll-mt":[{"scroll-mt":T()}],"scroll-mr":[{"scroll-mr":T()}],"scroll-mb":[{"scroll-mb":T()}],"scroll-ml":[{"scroll-ml":T()}],"scroll-p":[{"scroll-p":T()}],"scroll-px":[{"scroll-px":T()}],"scroll-py":[{"scroll-py":T()}],"scroll-ps":[{"scroll-ps":T()}],"scroll-pe":[{"scroll-pe":T()}],"scroll-pt":[{"scroll-pt":T()}],"scroll-pr":[{"scroll-pr":T()}],"scroll-pb":[{"scroll-pb":T()}],"scroll-pl":[{"scroll-pl":T()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",Y,U]}],fill:[{fill:["none",...tr()]}],"stroke-w":[{stroke:[C,X,$,N]}],stroke:[{stroke:["none",...tr()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})}}]);