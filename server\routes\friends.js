const express = require('express')
const User = require('../models/User')
const FriendRequest = require('../models/FriendRequest')
const { authenticateToken } = require('../middleware/auth')

const router = express.Router()

// All routes require authentication
router.use(authenticateToken)

// Get user's friends
router.get('/', async (req, res) => {
  try {
    const user = await User.findById(req.userId)
      .populate('friends', 'name email image avatar isOnline lastSeen privacySettings')
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      })
    }

    res.json({
      success: true,
      data: user.friends
    })
  } catch (error) {
    console.error('Get friends error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to get friends'
    })
  }
})

// Get friend requests (received)
router.get('/requests', async (req, res) => {
  try {
    const requests = await FriendRequest.getPendingRequests(req.userId)
    
    res.json({
      success: true,
      data: requests
    })
  } catch (error) {
    console.error('Get friend requests error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to get friend requests'
    })
  }
})

// Get sent friend requests
router.get('/requests/sent', async (req, res) => {
  try {
    const requests = await FriendRequest.getSentRequests(req.userId)
    
    res.json({
      success: true,
      data: requests
    })
  } catch (error) {
    console.error('Get sent requests error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to get sent requests'
    })
  }
})

// Send friend request
router.post('/request', async (req, res) => {
  try {
    const { userId, message } = req.body

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: 'User ID is required'
      })
    }

    if (userId === req.userId.toString()) {
      return res.status(400).json({
        success: false,
        message: 'Cannot send friend request to yourself'
      })
    }

    // Check if target user exists
    const targetUser = await User.findById(userId)
    if (!targetUser) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      })
    }

    // Check if users are already friends
    const currentUser = await User.findById(req.userId)
    if (currentUser.isFriendWith(userId)) {
      return res.status(400).json({
        success: false,
        message: 'Already friends with this user'
      })
    }

    // Check if request already exists
    const existingRequest = await FriendRequest.requestExists(req.userId, userId)
    if (existingRequest) {
      return res.status(400).json({
        success: false,
        message: 'Friend request already exists'
      })
    }

    // Check if target user allows friend requests
    if (!targetUser.privacySettings.allowFriendRequests) {
      return res.status(400).json({
        success: false,
        message: 'This user is not accepting friend requests'
      })
    }

    // Create friend request
    const friendRequest = new FriendRequest({
      from: req.userId,
      to: userId,
      message: message || ''
    })

    await friendRequest.save()

    // Populate the request for response
    await friendRequest.populate('to', 'name email image avatar')

    res.status(201).json({
      success: true,
      message: 'Friend request sent successfully',
      data: friendRequest
    })
  } catch (error) {
    console.error('Send friend request error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to send friend request'
    })
  }
})

// Accept friend request
router.post('/request/:requestId/accept', async (req, res) => {
  try {
    const { requestId } = req.params

    const friendRequest = await FriendRequest.findById(requestId)
      .populate('from', 'name email image avatar')

    if (!friendRequest) {
      return res.status(404).json({
        success: false,
        message: 'Friend request not found'
      })
    }

    if (friendRequest.to.toString() !== req.userId.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to accept this request'
      })
    }

    if (friendRequest.status !== 'pending') {
      return res.status(400).json({
        success: false,
        message: 'Friend request is no longer pending'
      })
    }

    // Update request status
    friendRequest.status = 'accepted'
    friendRequest.respondedAt = new Date()
    await friendRequest.save()

    // Add users as friends
    const currentUser = await User.findById(req.userId)
    const requestingUser = await User.findById(friendRequest.from._id)

    await currentUser.addFriend(friendRequest.from._id)
    await requestingUser.addFriend(req.userId)

    res.json({
      success: true,
      message: 'Friend request accepted',
      data: {
        friend: friendRequest.from,
        request: friendRequest
      }
    })
  } catch (error) {
    console.error('Accept friend request error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to accept friend request'
    })
  }
})

// Reject friend request
router.post('/request/:requestId/reject', async (req, res) => {
  try {
    const { requestId } = req.params

    const friendRequest = await FriendRequest.findById(requestId)

    if (!friendRequest) {
      return res.status(404).json({
        success: false,
        message: 'Friend request not found'
      })
    }

    if (friendRequest.to.toString() !== req.userId.toString()) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to reject this request'
      })
    }

    if (friendRequest.status !== 'pending') {
      return res.status(400).json({
        success: false,
        message: 'Friend request is no longer pending'
      })
    }

    // Update request status
    friendRequest.status = 'rejected'
    friendRequest.respondedAt = new Date()
    await friendRequest.save()

    res.json({
      success: true,
      message: 'Friend request rejected'
    })
  } catch (error) {
    console.error('Reject friend request error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to reject friend request'
    })
  }
})

// Remove friend
router.delete('/:friendId', async (req, res) => {
  try {
    const { friendId } = req.params

    const currentUser = await User.findById(req.userId)
    const friend = await User.findById(friendId)

    if (!friend) {
      return res.status(404).json({
        success: false,
        message: 'Friend not found'
      })
    }

    if (!currentUser.isFriendWith(friendId)) {
      return res.status(400).json({
        success: false,
        message: 'Not friends with this user'
      })
    }

    // Remove friendship from both users
    await currentUser.removeFriend(friendId)
    await friend.removeFriend(req.userId)

    res.json({
      success: true,
      message: 'Friend removed successfully'
    })
  } catch (error) {
    console.error('Remove friend error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to remove friend'
    })
  }
})

// Search users
router.get('/search', async (req, res) => {
  try {
    const { query, type = 'name', limit = 20 } = req.query

    if (!query || query.trim().length < 2) {
      return res.status(400).json({
        success: false,
        message: 'Search query must be at least 2 characters'
      })
    }

    const users = await User.searchUsers(query.trim(), req.userId, parseInt(limit))

    res.json({
      success: true,
      data: users
    })
  } catch (error) {
    console.error('Search users error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to search users'
    })
  }
})

// Block user
router.post('/block/:userId', async (req, res) => {
  try {
    const { userId } = req.params

    if (userId === req.userId.toString()) {
      return res.status(400).json({
        success: false,
        message: 'Cannot block yourself'
      })
    }

    const targetUser = await User.findById(userId)
    if (!targetUser) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      })
    }

    const currentUser = await User.findById(req.userId)
    await currentUser.blockUser(userId)

    // Remove any existing friend requests
    await FriendRequest.deleteMany({
      $or: [
        { from: req.userId, to: userId },
        { from: userId, to: req.userId }
      ]
    })

    res.json({
      success: true,
      message: 'User blocked successfully'
    })
  } catch (error) {
    console.error('Block user error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to block user'
    })
  }
})

// Unblock user
router.delete('/block/:userId', async (req, res) => {
  try {
    const { userId } = req.params

    const currentUser = await User.findById(req.userId)
    await currentUser.unblockUser(userId)

    res.json({
      success: true,
      message: 'User unblocked successfully'
    })
  } catch (error) {
    console.error('Unblock user error:', error)
    res.status(500).json({
      success: false,
      message: 'Failed to unblock user'
    })
  }
})

module.exports = router
