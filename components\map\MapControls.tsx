'use client'

import { ZoomIn, ZoomOut, RotateCcw, Users, EyeOff, Eye } from 'lucide-react'
import { But<PERSON> } from '@/components/ui/Button'

interface MapControlsProps {
  onZoomIn: () => void
  onZoomOut: () => void
  onReset: () => void
  showFriends: boolean
  onToggleFriends: () => void
}

export function MapControls({
  onZoomIn,
  onZoomOut,
  onReset,
  showFriends,
  onToggleFriends
}: MapControlsProps) {
  return (
    <div className="absolute bottom-4 right-4 flex flex-col space-y-2">
      {/* Zoom Controls */}
      <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
        <Button
          variant="ghost"
          size="icon"
          onClick={onZoomIn}
          className="rounded-none border-b"
          title="Zoom In"
        >
          <ZoomIn className="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="icon"
          onClick={onZoomOut}
          className="rounded-none"
          title="Zoom Out"
        >
          <ZoomOut className="w-4 h-4" />
        </Button>
      </div>

      {/* View Controls */}
      <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
        <Button
          variant="ghost"
          size="icon"
          onClick={onReset}
          className="rounded-none border-b"
          title="Reset View"
        >
          <RotateCcw className="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="icon"
          onClick={onToggleFriends}
          className={`rounded-none ${showFriends ? 'bg-primary-50 text-primary-600' : ''}`}
          title={showFriends ? 'Hide Friends' : 'Show Friends'}
        >
          {showFriends ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
        </Button>
      </div>
    </div>
  )
}
