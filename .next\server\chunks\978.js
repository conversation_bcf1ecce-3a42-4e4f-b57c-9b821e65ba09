exports.id=978,exports.ids=[978],exports.modules={58359:()=>{},93739:()=>{},45810:(e,t,i)=>{Promise.resolve().then(i.bind(i,6419)),Promise.resolve().then(i.bind(i,44669))},16295:(e,t,i)=>{Promise.resolve().then(i.t.bind(i,2583,23)),Promise.resolve().then(i.t.bind(i,26840,23)),Promise.resolve().then(i.t.bind(i,38771,23)),Promise.resolve().then(i.t.bind(i,13225,23)),Promise.resolve().then(i.t.bind(i,9295,23)),Promise.resolve().then(i.t.bind(i,43982,23))},6419:(e,t,i)=>{"use strict";i.r(t),i.d(t,{Providers:()=>f});var a=i(95344),r=i(47674),n=i(3729),o=i(56413),s=i(92875),l=i(98470),d=i(43158),c=i(67023);let u=i(47665).Z.create({baseURL:"http://localhost:3001/api",timeout:1e4,headers:{"Content-Type":"application/json"}});u.interceptors.request.use(async e=>{try{let t=await (0,r.getSession)();t?.accessToken&&(e.headers.Authorization=`Bearer ${t.accessToken}`)}catch(e){console.error("Error getting session:",e)}return e},e=>Promise.reject(e)),u.interceptors.response.use(e=>e,e=>(e.response?.status,Promise.reject(e)));let g=(0,d.Ue)()((0,c.tJ)((e,t)=>({user:null,isAuthenticated:!1,isLoading:!1,error:null,setUser:t=>{e({user:t,isAuthenticated:!!t,error:null})},setLoading:t=>{e({isLoading:t})},setError:t=>{e({error:t})},login:async(t,i)=>{e({isLoading:!0,error:null});try{let a=await u.post("/auth/login",{email:t,password:i});if(!a.data.success)return e({error:a.data.message||"Login failed",isLoading:!1}),!1;{let t=a.data.data.user;return e({user:t,isAuthenticated:!0,isLoading:!1,error:null}),!0}}catch(t){return e({error:t.response?.data?.message||"Login failed",isLoading:!1}),!1}},register:async(t,i,a)=>{e({isLoading:!0,error:null});try{let r=await u.post("/auth/register",{name:t,email:i,password:a});if(!r.data.success)return e({error:r.data.message||"Registration failed",isLoading:!1}),!1;{let t=r.data.data.user;return e({user:t,isAuthenticated:!0,isLoading:!1,error:null}),!0}}catch(t){return e({error:t.response?.data?.message||"Registration failed",isLoading:!1}),!1}},logout:()=>{e({user:null,isAuthenticated:!1,error:null}),localStorage.removeItem("auth-storage")},updateProfile:async i=>{let{user:a}=t();if(!a)return!1;e({isLoading:!0,error:null});try{let t=await u.put("/auth/profile",i);if(t.data.success)return e({user:{...a,...t.data.data},isLoading:!1,error:null}),!0;return e({error:t.data.message||"Profile update failed",isLoading:!1}),!1}catch(t){return e({error:t.response?.data?.message||"Profile update failed",isLoading:!1}),!1}},updatePrivacySettings:async i=>{let{user:a}=t();if(!a)return!1;e({isLoading:!0,error:null});try{let t=await u.put("/auth/privacy",{privacySettings:i});if(t.data.success)return e({user:{...a,privacySettings:i},isLoading:!1,error:null}),!0;return e({error:t.data.message||"Privacy settings update failed",isLoading:!1}),!1}catch(t){return e({error:t.response?.data?.message||"Privacy settings update failed",isLoading:!1}),!1}},initialize:()=>{let t=localStorage.getItem("auth-storage");if(t)try{let i=JSON.parse(t);i.state?.user&&e({user:i.state.user,isAuthenticated:!0})}catch(e){console.error("Failed to restore auth state:",e)}}}),{name:"auth-storage",partialize:e=>({user:e.user,isAuthenticated:e.isAuthenticated})})),p={zoom:1,center:{x:0,y:0},currentFloor:1,currentBuilding:"main"},m=(0,d.Ue)((e,t)=>({currentLocation:null,friendLocations:[],mapViewState:p,isLocationEnabled:!1,isLoading:!1,error:null,lastUpdate:null,setCurrentLocation:t=>{e({currentLocation:t,lastUpdate:new Date,error:null})},setFriendLocations:t=>{e({friendLocations:t})},updateFriendLocation:i=>{let{friendLocations:a}=t(),r=a.findIndex(e=>e.friend.id===i.friend.id);if(r>=0){let t=[...a];t[r]=i,e({friendLocations:t})}else e({friendLocations:[...a,i]})},removeFriendLocation:i=>{let{friendLocations:a}=t();e({friendLocations:a.filter(e=>e.friend.id!==i)})},setMapViewState:i=>{let{mapViewState:a}=t();e({mapViewState:{...a,...i}})},setLocationEnabled:t=>{e({isLocationEnabled:t})},setLoading:t=>{e({isLoading:t})},setError:t=>{e({error:t})},requestLocationPermission:async()=>{if(!navigator.geolocation)return e({error:"Geolocation is not supported by this browser"}),!1;try{let t=await navigator.permissions.query({name:"geolocation"});if("granted"===t.state)return e({isLocationEnabled:!0,error:null}),!0;if("prompt"===t.state)return!0;return e({error:"Location permission denied. Please enable location access in your browser settings.",isLocationEnabled:!1}),!1}catch(e){return console.error("Error checking location permission:",e),!0}},getCurrentPosition:async()=>new Promise(t=>{if(!navigator.geolocation){e({error:"Geolocation is not supported"}),t(null);return}navigator.geolocation.getCurrentPosition(i=>{e({isLocationEnabled:!0,error:null}),t(i)},i=>{let a="Failed to get location";switch(i.code){case i.PERMISSION_DENIED:a="Location access denied by user";break;case i.POSITION_UNAVAILABLE:a="Location information unavailable";break;case i.TIMEOUT:a="Location request timed out"}e({error:a,isLocationEnabled:!1}),t(null)},{enableHighAccuracy:!0,timeout:1e4,maximumAge:6e4})}),updateLocation:async t=>{e({isLoading:!0,error:null});try{let i=await u.post("/location/update",{coordinates:t,timestamp:new Date().toISOString()});if(!i.data.success)return e({error:i.data.message||"Failed to update location",isLoading:!1}),!1;{let t=i.data.data;return e({currentLocation:t,isLoading:!1,lastUpdate:new Date,error:null}),!0}}catch(t){return e({error:t.response?.data?.message||"Failed to update location",isLoading:!1}),!1}},shareLocation:async t=>{e({isLoading:!0,error:null});try{let i=await u.post("/location/share",{enabled:t});if(i.data.success)return e({isLoading:!1,error:null}),!0;return e({error:i.data.message||"Failed to update location sharing",isLoading:!1}),!1}catch(t){return e({error:t.response?.data?.message||"Failed to update location sharing",isLoading:!1}),!1}},getFriendLocations:async()=>{e({isLoading:!0,error:null});try{let t=await u.get("/location/friends");t.data.success?e({friendLocations:t.data.data,isLoading:!1,error:null}):e({error:t.data.message||"Failed to get friend locations",isLoading:!1})}catch(t){e({error:t.response?.data?.message||"Failed to get friend locations",isLoading:!1})}},initialize:()=>{let{requestLocationPermission:e}=t();e()}})),L=(0,n.createContext)({socket:null,isConnected:!1});function h({children:e}){let[t,i]=(0,n.useState)(null),[o,s]=(0,n.useState)(!1),{data:d}=(0,r.useSession)(),{user:c}=g(),{updateFriendLocation:u,removeFriendLocation:p}=m();return(0,n.useEffect)(()=>{if(!c&&!d)return;let e=(0,l.io)("http://localhost:3001",{auth:{userId:c?.id||d?.user?.id,token:d?.accessToken},transports:["websocket","polling"]});return e.on("connect",()=>{console.log("Connected to server"),s(!0)}),e.on("disconnect",()=>{console.log("Disconnected from server"),s(!1)}),e.on("connect_error",e=>{console.error("Connection error:",e),s(!1)}),e.on("location:update",e=>{console.log("Friend location update:",e)}),e.on("friend:online",e=>{console.log("Friend online status:",e)}),e.on("friend:request",e=>{console.log("New friend request:",e)}),e.on("friend:accepted",e=>{console.log("Friend request accepted:",e)}),e.on("error",e=>{console.error("Socket error:",e)}),i(e),()=>{e.disconnect(),i(null),s(!1)}},[c,d,u,p]),a.jsx(L.Provider,{value:{socket:t,isConnected:o},children:e})}function f({children:e}){let t=(0,o.t)(e=>e.initialize),i=(0,s.o)(e=>e.initialize);return(0,n.useEffect)(()=>{t(),i()},[t,i]),a.jsx(r.SessionProvider,{children:a.jsx(h,{children:e})})}},56413:(e,t,i)=>{"use strict";i.d(t,{t:()=>n});var a=i(43158),r=i(67023);let n=(0,a.Ue)()((0,r.tJ)((e,t)=>({user:null,isAuthenticated:!1,isLoading:!1,error:null,setUser:t=>{e({user:t,isAuthenticated:!!t,error:null})},setLoading:t=>{e({isLoading:t})},setError:t=>{e({error:t})},login:async(t,i)=>(e({isLoading:!0,error:null}),await new Promise(e=>setTimeout(e,1e3)),t&&i)?(e({user:{id:"1",email:t,name:t.split("@")[0],image:void 0,avatar:void 0,isOnline:!0,lastSeen:new Date,privacySettings:{shareLocation:!0,invisibleMode:!1,allowFriendRequests:!0,showOnlineStatus:!0},createdAt:new Date,updatedAt:new Date},isAuthenticated:!0,isLoading:!1,error:null}),!0):(e({error:"Please enter email and password",isLoading:!1}),!1),register:async(t,i,a)=>(e({isLoading:!0,error:null}),await new Promise(e=>setTimeout(e,1e3)),t&&i&&a)?(e({user:{id:"1",email:i,name:t,image:void 0,avatar:void 0,isOnline:!0,lastSeen:new Date,privacySettings:{shareLocation:!0,invisibleMode:!1,allowFriendRequests:!0,showOnlineStatus:!0},createdAt:new Date,updatedAt:new Date},isAuthenticated:!0,isLoading:!1,error:null}),!0):(e({error:"Please fill in all fields",isLoading:!1}),!1),logout:()=>{e({user:null,isAuthenticated:!1,error:null}),localStorage.removeItem("auth-storage")},updateProfile:async i=>{let{user:a}=t();return!!a&&(e({isLoading:!0,error:null}),await new Promise(e=>setTimeout(e,500)),e({user:{...a,...i},isLoading:!1,error:null}),!0)},updatePrivacySettings:async i=>{let{user:a}=t();return!!a&&(e({isLoading:!0,error:null}),await new Promise(e=>setTimeout(e,500)),e({user:{...a,privacySettings:i},isLoading:!1,error:null}),!0)},initialize:()=>{let t=localStorage.getItem("auth-storage");if(t)try{let i=JSON.parse(t);i.state?.user&&e({user:i.state.user,isAuthenticated:!0})}catch(e){console.error("Failed to restore auth state:",e)}}}),{name:"auth-storage",partialize:e=>({user:e.user,isAuthenticated:e.isAuthenticated})}))},92875:(e,t,i)=>{"use strict";i.d(t,{o:()=>o});var a=i(43158);let r={zoom:1,center:{x:0,y:0},currentFloor:1,currentBuilding:"main"},n=[{id:"1",userId:"2",coordinates:{x:150,y:100,floor:1,building:"main"},timestamp:new Date(Date.now()-3e5),isActive:!0,friend:{id:"2",email:"<EMAIL>",name:"Emma Johnson",image:void 0,avatar:void 0,isOnline:!0,lastSeen:new Date(Date.now()-12e4),privacySettings:{shareLocation:!0,invisibleMode:!1,allowFriendRequests:!0,showOnlineStatus:!0},createdAt:new Date,updatedAt:new Date}},{id:"2",userId:"3",coordinates:{x:400,y:200,floor:1,building:"main"},timestamp:new Date(Date.now()-6e4),isActive:!0,friend:{id:"3",email:"<EMAIL>",name:"Alex Chen",image:void 0,avatar:void 0,isOnline:!0,lastSeen:new Date(Date.now()-3e4),privacySettings:{shareLocation:!0,invisibleMode:!1,allowFriendRequests:!0,showOnlineStatus:!0},createdAt:new Date,updatedAt:new Date}}],o=(0,a.Ue)((e,t)=>({currentLocation:{id:"current",userId:"1",coordinates:{x:250,y:150,floor:1,building:"main"},timestamp:new Date,isActive:!0},friendLocations:n,mapViewState:r,isLocationEnabled:!0,isLoading:!1,error:null,lastUpdate:new Date,setCurrentLocation:t=>{e({currentLocation:t,lastUpdate:new Date,error:null})},setFriendLocations:t=>{e({friendLocations:t})},updateFriendLocation:i=>{let{friendLocations:a}=t(),r=a.findIndex(e=>e.friend.id===i.friend.id);if(r>=0){let t=[...a];t[r]=i,e({friendLocations:t})}else e({friendLocations:[...a,i]})},removeFriendLocation:i=>{let{friendLocations:a}=t();e({friendLocations:a.filter(e=>e.friend.id!==i)})},setMapViewState:i=>{let{mapViewState:a}=t();e({mapViewState:{...a,...i}})},setLocationEnabled:t=>{e({isLocationEnabled:t})},setLoading:t=>{e({isLoading:t})},setError:t=>{e({error:t})},requestLocationPermission:async()=>(e({isLoading:!0,error:null}),await new Promise(e=>setTimeout(e,1e3)),e({isLocationEnabled:!0,isLoading:!1,error:null}),!0),getCurrentPosition:async()=>(await new Promise(e=>setTimeout(e,500)),{coords:{latitude:60.1699,longitude:24.9384,accuracy:10,altitude:null,altitudeAccuracy:null,heading:null,speed:null},timestamp:Date.now()}),updateLocation:async t=>(e({isLoading:!0,error:null}),await new Promise(e=>setTimeout(e,500)),e({currentLocation:{id:"current",userId:"1",coordinates:t,timestamp:new Date,isActive:!0},isLoading:!1,lastUpdate:new Date,error:null}),!0),shareLocation:async t=>(e({isLoading:!0,error:null}),await new Promise(e=>setTimeout(e,500)),e({isLoading:!1,error:null}),!0),getFriendLocations:async()=>{e({isLoading:!0,error:null}),await new Promise(e=>setTimeout(e,500)),e({friendLocations:n,isLoading:!1,error:null})},initialize:()=>{e({isLocationEnabled:!0,friendLocations:n})}}))},78062:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>p,metadata:()=>g});var a=i(25036),r=i(80265),n=i.n(r);i(67272);var o=i(86843);let s=(0,o.createProxy)(String.raw`C:\Users\<USER>\OneDrive - Suomalaisen Yhteiskoulun Osakeyhtiö\Desktop\Coding\schoolfinder\app\providers.tsx`),{__esModule:l,$$typeof:d}=s;s.default;let c=(0,o.createProxy)(String.raw`C:\Users\<USER>\OneDrive - Suomalaisen Yhteiskoulun Osakeyhtiö\Desktop\Coding\schoolfinder\app\providers.tsx#Providers`);var u=i(69636);let g={title:"SYK SchoolFinder",description:"Find your friends around SYK campus in real-time",manifest:"/manifest.json",themeColor:"#3b82f6",viewport:"width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no",icons:{icon:"/favicon.ico",apple:"/apple-touch-icon.png"}};function p({children:e}){return(0,a.jsxs)("html",{lang:"en",children:[(0,a.jsxs)("head",{children:[a.jsx("meta",{name:"apple-mobile-web-app-capable",content:"yes"}),a.jsx("meta",{name:"apple-mobile-web-app-status-bar-style",content:"default"}),a.jsx("meta",{name:"apple-mobile-web-app-title",content:"SYK SchoolFinder"}),a.jsx("meta",{name:"mobile-web-app-capable",content:"yes"}),a.jsx("link",{rel:"apple-touch-icon",href:"/apple-touch-icon.png"})]}),a.jsx("body",{className:n().className,children:(0,a.jsxs)(c,{children:[e,a.jsx(u.x7,{position:"top-center",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"}}})]})})]})}},67272:()=>{}};