'use client'

import { SessionProvider } from 'next-auth/react'
import { useEffect } from 'react'
import { useAuthStore } from '@/store/authStore.demo'
import { useLocationStore } from '@/store/locationStore.demo'
import { SocketProvider } from '@/components/providers/SocketProvider'

export function Providers({ children }: { children: React.ReactNode }) {
  const initializeAuth = useAuthStore((state) => state.initialize)
  const initializeLocation = useLocationStore((state) => state.initialize)

  useEffect(() => {
    initializeAuth()
    initializeLocation()
  }, [initializeAuth, initializeLocation])

  return (
    <SessionProvider>
      <SocketProvider>
        {children}
      </SocketProvider>
    </SessionProvider>
  )
}
